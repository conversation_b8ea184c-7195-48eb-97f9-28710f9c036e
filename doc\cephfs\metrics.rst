.. _cephfs_metrics:

Metrics
=======

CephFS uses :ref:`Perf Counters` to track metrics. The counters can be labeled (:ref:`Labeled Perf Counters`).

Client Metrics
--------------

CephFS exports client metrics as :ref:`Labeled Perf Counters`, which could be used to monitor the client performance. CephFS exports the below client metrics.

.. list-table:: Client Metrics
   :widths: 25 25 75
   :header-rows: 1

   * - Name
     - Type
     - Description
   * - num_clients
     - Gauge
     - Number of client sessions
   * - cap_hits
     - Gauge
     - Percentage of file capability hits over total number of caps
   * - cap_miss
     - Gauge
     - Percentage of file capability misses over total number of caps
   * - avg_read_latency
     - Gauge
     - Mean value of the read latencies
   * - avg_write_latency
     - Gauge
     - Mean value of the write latencies
   * - avg_metadata_latency
     - Gauge
     - Mean value of the metadata latencies
   * - dentry_lease_hits
     - Gauge
     - Percentage of dentry lease hits handed out over the total dentry lease requests
   * - dentry_lease_miss
     - Gauge
     - Percentage of dentry lease misses handed out over the total dentry lease requests
   * - opened_files
     - Gauge
     - Number of opened files
   * - opened_inodes
     - Gauge
     - Number of opened inodes
   * - pinned_icaps
     - Gauge
     - Number of pinned Inode Caps
   * - total_inodes
     - Gauge
     - Total number of Inodes
   * - total_read_ops
     - Gauge
     - Total number of read operations generated by all process
   * - total_read_size
     - Gauge
     - Number of bytes read in input/output operations generated by all process
   * - total_write_ops
     - Gauge
     - Total number of write operations generated by all process
   * - total_write_size
     - Gauge
     - Number of bytes written in input/output operations generated by all processes

Getting Metrics
===============

The metrics could be scraped from the MDS admin socket as well as using the tell interface. The ``mds_client_metrics-<fsname>`` section in the output of ``counter dump`` command displays the metrics for each client as shown below::

    "mds_client_metrics": [
        {
            "labels": {
                "fs_name": "<fsname>",
                "id": "14213"
            },
            "counters": {
                "num_clients": 2
            }
        }
    ],
    "mds_client_metrics-<fsname>": [
        {
            "labels": {
                "client": "client.0",
                "rank": "0"
            },
            "counters": {
                "cap_hits": 5149,
                "cap_miss": 1,
                "avg_read_latency": 0.000000000,
                "avg_write_latency": 0.000000000,
                "avg_metadata_latency": 0.000000000,
                "dentry_lease_hits": 0,
                "dentry_lease_miss": 0,
                "opened_files": 1,
                "opened_inodes": 2,
                "pinned_icaps": 2,
                "total_inodes": 2,
                "total_read_ops": 0,
                "total_read_size": 0,
                "total_write_ops": 4836,
                "total_write_size": 633864192
            }
        },
        {
            "labels": {
                "client": "client.1",
                "rank": "0"
            },
            "counters": {
                "cap_hits": 3375,
                "cap_miss": 8,
                "avg_read_latency": 0.000000000,
                "avg_write_latency": 0.000000000,
                "avg_metadata_latency": 0.000000000,
                "dentry_lease_hits": 0,
                "dentry_lease_miss": 0,
                "opened_files": 1,
                "opened_inodes": 2,
                "pinned_icaps": 2,
                "total_inodes": 2,
                "total_read_ops": 0,
                "total_read_size": 0,
                "total_write_ops": 3169,
                "total_write_size": 415367168
            }
        }
    ]
