ceph-menv

Installation

1. Build links

# assuming ceph build directory is at $HOME/ceph/build
$ cd ceph-menv
$ ./build_links.sh

A different ceph repository can be passed as the first argument to build_links.sh.

2. Configure shell environment

To your shell startup script (such as $HOME/.bashrc) add the following:

source ~/ceph-menv/.menvrc

(modify line appropriately if ceph-menv was installed at a different location)
