commit 9764da52395923e0b32908d83a9f7304401fee43 (tag: refs/tags/v0.94.5, refs/remotes/gh/hammer)
Author: <PERSON> <jen<PERSON>@ceph.com>
Date:   Mon Oct 26 07:05:32 2015 -0700

    0.94.5

commit 1107f29224e24211860b719b82f9b85a022c25e3
Merge: d86eab5 d3abcbe
Author: <PERSON><PERSON> <<EMAIL>>
Date:   Fri Oct 23 20:41:33 2015 +0200

    Merge pull request #6348 from dillaman/wip-13567-hammer
    
    librbd: potential assertion failure during cache read
    
    Reviewed-by: <PERSON> <<EMAIL>>

commit d86eab5b860186066c888208d92a42a748569d78
Merge: 4ebfa1b 250dc07
Author: <PERSON><PERSON> <<EMAIL>>
Date:   Fri Oct 23 15:20:32 2015 +0200

    Merge pull request #6362 from liewegas/wip-shut-up-osd-hammer
    
    osd/ReplicatedPG: remove stray debug line
    
    Reviewed-by: <PERSON><PERSON> <lda<PERSON><EMAIL>>

commit 250dc0722b9156fe1b8be81e09fb4ead699065f3 (refs/remotes/me/wip-shut-up-osd-hammer)
Author: Sage Weil <<EMAIL>>
Date:   Sat Sep 12 08:33:44 2015 -0400

    osd/ReplicatedPG: remove stray debug line
    
    This snuck in
    
    Signed-off-by: Sage Weil <<EMAIL>>
    (cherry picked from commit ff9600a6cef613d40e875597b6392778df1bb04c)

commit d3abcbea1fdb04f0994f19584b93f6f1b1ff37ca
Author: Jason Dillaman <<EMAIL>>
Date:   Wed Oct 21 13:12:48 2015 -0400

    librbd: potential assertion failure during cache read
    
    It's possible for a cache read from a clone to trigger a writeback if a
    previous read op determined the object doesn't exist in the clone,
    followed by a cached write to the non-existent clone object, followed
    by another read request to the same object.  This causes the cache to
    flush the pending writeback ops while not holding the owner lock.
    
    Fixes: #13559
    Backport: hammer
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 4692c330bd992a06b97b5b8975ab71952b22477a)

commit 991d0f0575411e2f2b53df35e36ff6170bcc9d8b
Author: Jason Dillaman <<EMAIL>>
Date:   Wed Oct 21 13:09:54 2015 -0400

    tests: reproduce crash during read-induced CoW
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 382219b6bba09156f8cf3d420c6348738e7ed4c6)

commit 4ebfa1bcc7e3b57c3c12000633654d36a005d512
Merge: 9529269 51f3d6a
Author: Jason Dillaman <<EMAIL>>
Date:   Thu Oct 22 12:14:42 2015 -0400

    Merge pull request #6330 from dachary/wip-13550-hammer
    
    qemu workunit refers to apt-mirror.front.sepia.ceph.com
    
    Reviewed-by: Jason Dillaman <<EMAIL>>

commit 51f3d6a489760dbcb931676396eea92159ca2ad9
Author: Yuan Zhou <<EMAIL>>
Date:   Fri Jun 19 00:02:20 2015 +0800

    qa: Use public qemu repo
    
    This would allow some external tests outside of sepia lab
    
    Signed-off-by: Yuan Zhou <<EMAIL>>
    (cherry picked from commit 4731c1e35539c9506ff3fe3141553fad099d0eee)
