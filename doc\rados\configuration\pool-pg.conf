[global]

	# By default, <PERSON><PERSON> makes three replicas of RADOS objects. If you want
	# to maintain four copies of an object the default value--a primary
	# copy and three replica copies--reset the default values as shown in
	# 'osd_pool_default_size'. If you want to allow <PERSON><PERSON> to accept an I/O
	# operation to a degraded PG, set 'osd_pool_default_min_size' to a
	# number less than the 'osd_pool_default_size' value.

	osd_pool_default_size = 3  # Write an object three times.
	osd_pool_default_min_size = 2 # Accept an I/O operation to a PG that has two copies of an object.

	# Note: by default, PG autoscaling is enabled and this value is used only
	# in specific circumstances. It is however still recommend to set it.
	# Ensure you have a realistic number of placement groups. We recommend
	# approximately 100 per OSD. E.g., total number of OSDs multiplied by 100
	# divided by the number of replicas (i.e., 'osd_pool_default_size'). So for
	# 10 OSDs and 'osd_pool_default_size' = 4, we'd recommend approximately
	# (100 * 10) / 4 = 250.
	# Always use the nearest power of two.
	osd_pool_default_pg_num = 256
