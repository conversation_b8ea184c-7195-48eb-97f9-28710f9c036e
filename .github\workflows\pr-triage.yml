---
name: "Pull Request Triage"
on: pull_request_target
env:
  GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
jobs:
  pr-triage:
    runs-on: ubuntu-latest
    steps:
      - name: Assign labels based on modified files
        # https://github.com/marketplace/actions/labeler?version=v4.0.2
        uses: actions/labeler@5c7539237e04b714afd8ad9b4aed733815b9fab4
        with:
          sync-labels: ''
          repo-token: "${{ secrets.GITHUB_TOKEN }}"
      - name: Assign to Dashboard project
        # https://github.com/marketplace/actions/add-to-github-projects?version=v0.4.1
        uses: actions/add-to-project@4756e6330fe1e0a736690d3cfd9f11c9399c2ed4
        with:
          project-url: https://github.com/orgs/ceph/projects/2
          github-token: ${{ secrets.GITHUB_TOKEN }}
          labeled: dashboard
      - name: Assign milestone based on target brach name
        # https://github.com/marketplace/actions/pull-request-milestone?version=v1.3.0
        uses: iyu/actions-milestone@e93115c90ff7bcddee71086e9253f1b6a5f4b48a
        with:
          configuration-path: .github/milestone.yml
          repo-token: "${{ secrets.GITHUB_TOKEN }}"
