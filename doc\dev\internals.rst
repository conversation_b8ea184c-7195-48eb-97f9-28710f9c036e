================
 Ceph Internals
================

.. note:: For information on how to use Ceph as a library (from your own
   software), see :doc:`/api/index`.

Starting a Development-mode Ceph Cluster
----------------------------------------

Compile the source and then run the following commands to start a
development-mode Ceph cluster::

	cd build
	OSD=3 MON=3 MGR=3 ../src/vstart.sh -n -x
	# check that it's there
	bin/ceph health

.. rubric:: Mailing list

The ``<EMAIL>`` list is for discussion about the development of Ceph,
its interoperability with other technology, and the operations of the
project itself.  Subscribe by sending a message to ``<EMAIL>``
with the word `subscribe` in the subject.

Alternatively you can visit https://lists.ceph.io and register.

The <EMAIL> list is for discussion
and patch review for the Linux kernel Ceph client component.
Subscribe by sending a message to ``<EMAIL>`` with the line::

 subscribe ceph-devel

in the body of the message.

.. raw:: html

   <!---

.. rubric:: Contents

.. toctree::
   :glob:

   *
   osd_internals/index*
   mds_internals/index*
   radosgw/index*
   ceph-volume/index*
   crimson/index*

.. raw:: html

   --->
