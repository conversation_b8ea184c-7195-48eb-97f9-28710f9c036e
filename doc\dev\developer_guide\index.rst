============================================
Contributing to Ceph: A Guide for Developers
============================================

:Author: <PERSON><PERSON>
:Author: <PERSON>
:License: Creative Commons Attribution Share Alike 3.0 (CC-BY-SA-3.0)

.. note:: You may also be interested in the :doc:`/dev/internals` documentation.

.. toctree::
   :maxdepth: 1

   Introduction <intro>
   Essentials <essentials>
   What is Merged and When <merging>
   Issue tracker <issue-tracker>
   Basic workflow <basic-workflow>
   Tests: Unit Tests <tests-unit-tests>
   Tests: Integration Tests (Teuthology) <testing_integration_tests/index>
   Tests: Running Tests (Locally) <running-tests-locally>
   Tests: Windows <tests-windows>
   Ceph Dashboard Developer Documentation (formerly HACKING.rst) <dash-devel>
   Tracing Developer Documentation <jaegertracing>
   Cephadm Developer Documentation  <../cephadm/index>
   Debugging with GDB <debugging-gdb>
