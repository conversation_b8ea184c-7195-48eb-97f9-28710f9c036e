libcephsqlite.so libsqlite3-mod-ceph #MINVER#
 _ZGVN18SimpleRADOSStriper7biglockB5cxx11E@Base 15.2.0-1
 _ZGVN18SimpleRADOSStriper8lockdescB5cxx11E@Base 15.2.0-1
 _ZN18SimpleRADOSStriper10XATTR_EXCLE@Base 15.2.0-1
 _ZN18SimpleRADOSStriper10XATTR_SIZEE@Base 15.2.0-1
 _ZN18SimpleRADOSStriper12recover_lockEv@Base 15.2.0-1
 _ZN18SimpleRADOSStriper12set_metadataEmb@Base 15.2.0-1
 _ZN18SimpleRADOSStriper12shrink_allocEm@Base 15.2.0-1
 _ZN18SimpleRADOSStriper13XATTR_VERSIONE@Base 15.2.0-1
 _ZN18SimpleRADOSStriper13config_loggerEPN4<PERSON>ph6common11CephContextESt17basic_string_viewIcSt11char_traitsIcEEPSt10shared_ptrINS1_12PerfCountersEE@Base 15.2.0-1
 _ZN18SimpleRADOSStriper13print_lockersERSo@Base 15.2.0-1
 _ZN18SimpleRADOSStriper13wait_for_aiosEb@Base 15.2.0-1
 _ZN18SimpleRADOSStriper15XATTR_ALLOCATEDE@Base 15.2.0-1
 _ZN18SimpleRADOSStriper16lock_keeper_mainEv@Base 15.2.0-1
 _ZN18SimpleRADOSStriper18maybe_shrink_allocEv@Base 15.2.0-1
 _ZN18SimpleRADOSStriper24XATTR_LAYOUT_OBJECT_SIZEE@Base 15.2.0-1
 _ZN18SimpleRADOSStriper24XATTR_LAYOUT_STRIPE_UNITE@Base 15.2.0-1
 _ZN18SimpleRADOSStriper25XATTR_LAYOUT_STRIPE_COUNTE@Base 15.2.0-1
 _ZN18SimpleRADOSStriper4lockEm@Base 15.2.0-1
 _ZN18SimpleRADOSStriper4openEv@Base 15.2.0-1
 _ZN18SimpleRADOSStriper4readEPvmm@Base 15.2.0-1
 _ZN18SimpleRADOSStriper4statEPm@Base 15.2.0-1
 _ZN18SimpleRADOSStriper5flushEv@Base 15.2.0-1
 _ZN18SimpleRADOSStriper5writeEPKvmm@Base 15.2.0-1
 _ZN18SimpleRADOSStriper6createEv@Base 15.2.0-1
 _ZN18SimpleRADOSStriper6removeEv@Base 15.2.0-1
 _ZN18SimpleRADOSStriper6str2blESt17basic_string_viewIcSt11char_traitsIcEE@Base 15.2.0-1
 _ZN18SimpleRADOSStriper6unlockEv@Base 15.2.0-1
 _ZN18SimpleRADOSStriper7biglockB5cxx11E@Base 15.2.0-1
 _ZN18SimpleRADOSStriper7uint2blEm@Base 15.2.0-1
 _ZN18SimpleRADOSStriper8lockdescB5cxx11E@Base 15.2.0-1
 _ZN18SimpleRADOSStriper8truncateEm@Base 15.2.0-1
 _ZN18SimpleRADOSStriperD1Ev@Base 15.2.0-1
 _ZN18SimpleRADOSStriperD2Ev@Base 15.2.0-1
 _ZNK18SimpleRADOSStriper15get_next_extentEmm@Base 15.2.0-1
 cephsqlite_setcct@Base 15.2.0-1
 sqlite3_cephsqlite_init@Base 15.2.0-1
