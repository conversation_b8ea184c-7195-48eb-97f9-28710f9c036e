#! /usr/bin/dh-exec

lib/systemd/system/ceph-osd*
usr/bin/ceph-bluestore-tool
usr/bin/ceph-clsinfo
usr/bin/ceph-erasure-code-tool
usr/bin/ceph-objectstore-tool
usr/bin/ceph-osdomap-tool
<pkg.ceph.crimson> usr/bin/crimson-store-nbd
usr/bin/${CEPH_OSD_BASENAME} => /usr/bin/ceph-osd
usr/bin/ceph_objectstore_bench
usr/libexec/ceph/ceph-osd-prestart.sh
usr/lib/libos_tp.so*
usr/lib/libosd_tp.so*
usr/share/man/man8/ceph-clsinfo.8
usr/share/man/man8/ceph-osd.8
usr/share/man/man8/ceph-bluestore-tool.8
etc/sysctl.d/30-ceph-osd.conf
