#!/bin/sh
# vim: set noet ts=8:
# postinst script for ceph-mgr-diskprediction-local
#
# see: dh_installdeb(1)

set -e

# summary of how this script can be called:
#
# 	postinst configure <most-recently-configured-version>
# 	old-postinst abort-upgrade <new-version>
# 	conflictor's-postinst abort-remove in-favour <package> <new-version>
# 	postinst abort-remove
# 	deconfigured's-postinst abort-deconfigure in-favour <failed-install-package> <version> [<removing conflicting-package> <version>]
#

# for details, see http://www.debian.org/doc/debian-policy/ or
# the debian-policy package

case "$1" in
    configure)
	# attempt to load the plugin if the mgr is running
	deb-systemd-invoke try-restart ceph-mgr.target
    ;;
    abort-upgrade|abort-remove|abort-deconfigure)
	:
    ;;

    *)
        echo "postinst called with unknown argument \`$1'" >&2
        exit 1
    ;;
esac

# dh_installdeb will replace this with shell code automatically
# generated by other debhelper scripts.

#DEBHELPER#

exit 0


