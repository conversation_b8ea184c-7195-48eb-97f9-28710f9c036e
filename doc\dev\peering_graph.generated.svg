<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.43.0 (0)
 -->
<!-- Title: G Pages: 1 -->
<svg width="3777pt" height="1031pt"
 viewBox="0.00 0.00 3777.00 1031.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 1027)">
<title>G</title>
<polygon fill="white" stroke="transparent" points="-4,4 -4,-1027 3773,-1027 3773,4 -4,4"/>
<g id="clust1" class="cluster">
<title>cluster0</title>
<polygon fill="none" stroke="black" points="8,-8 8,-1015 3761,-1015 3761,-8 8,-8"/>
<text text-anchor="middle" x="1884.5" y="-999.8" font-family="Times,serif" font-size="14.00">PeeringMachine</text>
</g>
<g id="clust2" class="cluster">
<title>cluster1</title>
<polygon fill="none" stroke="black" points="132,-16 132,-984 3245,-984 3245,-16 132,-16"/>
<text text-anchor="middle" x="1688.5" y="-968.8" font-family="Times,serif" font-size="14.00">Started</text>
</g>
<g id="clust3" class="cluster">
<title>cluster2</title>
<polygon fill="none" stroke="black" points="1889,-24 1889,-876 3237,-876 3237,-24 1889,-24"/>
<text text-anchor="middle" x="2563" y="-860.8" font-family="Times,serif" font-size="14.00">Primary</text>
</g>
<g id="clust4" class="cluster">
<title>cluster3</title>
<polygon fill="lightgrey" stroke="black" points="2719,-467 2719,-845 3029,-845 3029,-467 2719,-467"/>
<text text-anchor="middle" x="2874" y="-829.8" font-family="Times,serif" font-size="14.00">Peering</text>
</g>
<g id="clust5" class="cluster">
<title>cluster4</title>
<polygon fill="none" stroke="black" points="1897,-32 1897,-737 2711,-737 2711,-32 1897,-32"/>
<text text-anchor="middle" x="2304" y="-721.8" font-family="Times,serif" font-size="14.00">Active</text>
</g>
<g id="clust6" class="cluster">
<title>cluster5</title>
<polygon fill="none" stroke="black" points="140,-554 140,-845 1488,-845 1488,-554 140,-554"/>
<text text-anchor="middle" x="814" y="-829.8" font-family="Times,serif" font-size="14.00">ReplicaActive</text>
</g>
<g id="clust7" class="cluster">
<title>cluster6</title>
<polygon fill="none" stroke="black" points="1496,-467 1496,-629 1881,-629 1881,-467 1496,-467"/>
<text text-anchor="middle" x="1688.5" y="-613.8" font-family="Times,serif" font-size="14.00">ToDelete</text>
</g>
<!-- Crashed -->
<g id="node1" class="node">
<title>Crashed</title>
<ellipse fill="none" stroke="black" cx="3488" cy="-406" rx="49.29" ry="18"/>
<text text-anchor="middle" x="3488" y="-402.3" font-family="Times,serif" font-size="14.00">Crashed</text>
</g>
<!-- Initial -->
<g id="node2" class="node">
<title>Initial</title>
<polygon fill="lightgrey" stroke="black" points="70,-953 15.54,-935 70,-917 124.46,-935 70,-953"/>
<polyline fill="none" stroke="black" points="26.93,-938.77 26.93,-931.23 "/>
<polyline fill="none" stroke="black" points="58.61,-920.77 81.39,-920.77 "/>
<polyline fill="none" stroke="black" points="113.07,-931.23 113.07,-938.77 "/>
<polyline fill="none" stroke="black" points="81.39,-949.23 58.61,-949.23 "/>
<text text-anchor="middle" x="70" y="-931.3" font-family="Times,serif" font-size="14.00">Initial</text>
</g>
<!-- Initial&#45;&gt;Crashed -->
<g id="edge2" class="edge">
<title>Initial&#45;&gt;Crashed</title>
<path fill="none" stroke="#1e90ff" d="M94.78,-925.08C104.94,-921.86 116.89,-918.63 128,-917 170.89,-910.72 3212.54,-922.45 3249,-899 3315.73,-856.09 3306.93,-814.48 3324,-737 3330.41,-707.92 3337.82,-696.38 3324,-670 3317.48,-657.55 3304.52,-664.45 3298,-652 3294.91,-646.09 3293.59,-642 3298,-637 3307.33,-626.41 3348.9,-636.26 3361,-629 3441.38,-580.81 3450.13,-544.77 3483,-457 3485.66,-449.89 3487.09,-441.84 3487.82,-434.31"/>
<polygon fill="#1e90ff" stroke="#1e90ff" points="3491.32,-434.35 3488.44,-424.16 3484.34,-433.93 3491.32,-434.35"/>
<text text-anchor="middle" x="3403.5" y="-640.8" font-family="Times,serif" font-size="14.00" fill="#1e90ff">boost::statechart::event_base</text>
</g>
<!-- Reset -->
<g id="node3" class="node">
<title>Reset</title>
<ellipse fill="none" stroke="black" cx="3290" cy="-493" rx="37.09" ry="18"/>
<text text-anchor="middle" x="3290" y="-489.3" font-family="Times,serif" font-size="14.00">Reset</text>
</g>
<!-- Initial&#45;&gt;Reset -->
<g id="edge1" class="edge">
<title>Initial&#45;&gt;Reset</title>
<path fill="none" stroke="#000000" d="M68.22,-917.57C65.6,-891.88 61,-840.62 61,-797 61,-797 61,-797 61,-579 61,-543.86 101.37,-559.95 136,-554 231.18,-537.65 2965.92,-513.61 3033,-511 3106.78,-508.13 3192.31,-501.89 3243.73,-497.82"/>
<polygon fill="#000000" stroke="#000000" points="3244.17,-501.3 3253.86,-497.01 3243.61,-494.32 3244.17,-501.3"/>
<text text-anchor="middle" x="92.5" y="-684.3" font-family="Times,serif" font-size="14.00" fill="#000000">Initialize</text>
</g>
<!-- GetInfo -->
<g id="node6" class="node">
<title>GetInfo</title>
<polygon fill="lightgrey" stroke="black" points="2859,-814 2794.82,-796 2859,-778 2923.18,-796 2859,-814"/>
<polyline fill="none" stroke="black" points="2806.37,-799.24 2806.37,-792.76 "/>
<polyline fill="none" stroke="black" points="2847.45,-781.24 2870.55,-781.24 "/>
<polyline fill="none" stroke="black" points="2911.63,-792.76 2911.63,-799.24 "/>
<polyline fill="none" stroke="black" points="2870.55,-810.76 2847.45,-810.76 "/>
<text text-anchor="middle" x="2859" y="-792.3" font-family="Times,serif" font-size="14.00">GetInfo</text>
</g>
<!-- Initial&#45;&gt;GetInfo -->
<g id="edge41" class="edge">
<title>Initial&#45;&gt;GetInfo</title>
<path fill="none" stroke="#40e0d0" d="M94.79,-925.1C104.94,-921.89 116.89,-918.65 128,-917 233.63,-901.33 1948.11,-930.95 2050,-899 2061.29,-895.46 2060.71,-887.55 2072,-884 2108.21,-872.61 2692.07,-890.86 2781.21,-878.25"/>
<polygon fill="#40e0d0" stroke="#40e0d0" points="2782.04,-881.65 2791,-876 2780.47,-874.83 2782.04,-881.65"/>
<text text-anchor="middle" x="2114.5" y="-887.8" font-family="Times,serif" font-size="14.00" fill="#40e0d0">MNotifyRec</text>
</g>
<!-- Stray -->
<g id="node27" class="node">
<title>Stray</title>
<ellipse fill="none" stroke="black" cx="1532" cy="-796" rx="36.29" ry="18"/>
<text text-anchor="middle" x="1532" y="-792.3" font-family="Times,serif" font-size="14.00">Stray</text>
</g>
<!-- Initial&#45;&gt;Stray -->
<g id="edge44" class="edge">
<title>Initial&#45;&gt;Stray</title>
<path fill="none" stroke="#1e90ff" d="M95.09,-925.13C105.18,-921.96 117.02,-918.75 128,-917 202.87,-905.1 1428.01,-916.66 1492,-876 1510.2,-864.44 1520.46,-841.83 1526.02,-823.75"/>
<polygon fill="#1e90ff" stroke="#1e90ff" points="1529.43,-824.58 1528.71,-814.01 1522.68,-822.72 1529.43,-824.58"/>
<text text-anchor="middle" x="1489.5" y="-887.8" font-family="Times,serif" font-size="14.00" fill="#1e90ff">MInfoRec</text>
</g>
<!-- Initial&#45;&gt;Stray -->
<g id="edge46" class="edge">
<title>Initial&#45;&gt;Stray</title>
<path fill="none" stroke="#0000ff" d="M95.09,-925.14C105.18,-921.97 117.02,-918.75 128,-917 588.18,-843.54 710.18,-897.23 1176,-884 1193.55,-883.5 1477.16,-885.38 1492,-876 1510.23,-864.48 1520.49,-841.87 1526.04,-823.78"/>
<polygon fill="#0000ff" stroke="#0000ff" points="1529.44,-824.6 1528.73,-814.03 1522.7,-822.74 1529.44,-824.6"/>
<text text-anchor="middle" x="1209.5" y="-887.8" font-family="Times,serif" font-size="14.00" fill="#0000ff">MLogRec</text>
</g>
<!-- Reset&#45;&gt;Crashed -->
<g id="edge3" class="edge">
<title>Reset&#45;&gt;Crashed</title>
<path fill="none" stroke="#ff0000" d="M3276.79,-476.1C3269.62,-465.47 3263.75,-451.7 3272,-442 3291.76,-418.78 3371.65,-410.92 3428.51,-408.28"/>
<polygon fill="#ff0000" stroke="#ff0000" points="3428.99,-411.77 3438.84,-407.85 3428.7,-404.77 3428.99,-411.77"/>
<text text-anchor="middle" x="3377.5" y="-445.8" font-family="Times,serif" font-size="14.00" fill="#ff0000">boost::statechart::event_base</text>
</g>
<!-- Start -->
<g id="node4" class="node">
<title>Start</title>
<polygon fill="lightgrey" stroke="black" points="3188,-953 3139.07,-935 3188,-917 3236.93,-935 3188,-953"/>
<polyline fill="none" stroke="black" points="3150.33,-939.14 3150.33,-930.86 "/>
<polyline fill="none" stroke="black" points="3176.74,-921.14 3199.26,-921.14 "/>
<polyline fill="none" stroke="black" points="3225.67,-930.86 3225.67,-939.14 "/>
<polyline fill="none" stroke="black" points="3199.26,-948.86 3176.74,-948.86 "/>
<text text-anchor="middle" x="3188" y="-931.3" font-family="Times,serif" font-size="14.00">Start</text>
</g>
<!-- Reset&#45;&gt;Start -->
<g id="edge53" class="edge">
<title>Reset&#45;&gt;Start</title>
<path fill="none" stroke="#0000ff" d="M3310.54,-508.36C3318.12,-514.24 3326.44,-521.43 3333,-529 3362.43,-562.96 3377.81,-595.36 3348,-629 3328.4,-651.11 3304.65,-618.02 3282,-637 3272.93,-644.6 3260.35,-681.54 3247.77,-725.55"/>
<polygon fill="#0000ff" stroke="#0000ff" points="3244.35,-724.77 3245,-735.34 3251.09,-726.67 3244.35,-724.77"/>
<text text-anchor="middle" x="3292.5" y="-684.3" font-family="Times,serif" font-size="14.00" fill="#0000ff">ActMap</text>
</g>
<!-- Start&#45;&gt;Crashed -->
<g id="edge4" class="edge">
<title>Start&#45;&gt;Crashed</title>
<path fill="none" stroke="#0000ff" d="M3245,-924.14C3345.1,-905.18 3542,-859.91 3542,-797 3542,-797 3542,-797 3542,-492 3542,-468.63 3527.27,-446.48 3513.2,-430.66"/>
<polygon fill="#0000ff" stroke="#0000ff" points="3515.38,-427.87 3505.98,-422.99 3510.28,-432.67 3515.38,-427.87"/>
<text text-anchor="middle" x="3647.5" y="-640.8" font-family="Times,serif" font-size="14.00" fill="#0000ff">boost::statechart::event_base</text>
</g>
<!-- Start&#45;&gt;Reset -->
<g id="edge48" class="edge">
<title>Start&#45;&gt;Reset</title>
<path fill="none" stroke="#40e0d0" d="M3245,-931.84C3276.16,-928.16 3314.01,-919.37 3342,-899 3437.71,-829.34 3391.79,-751.12 3478,-670 3490.74,-658.01 3503.89,-666.93 3513,-652 3560.6,-573.99 3410.35,-523.93 3333.01,-503.92"/>
<polygon fill="#40e0d0" stroke="#40e0d0" points="3333.61,-500.46 3323.06,-501.4 3331.9,-507.25 3333.61,-500.46"/>
<text text-anchor="middle" x="3507.5" y="-684.3" font-family="Times,serif" font-size="14.00" fill="#40e0d0">AdvMap</text>
</g>
<!-- Start&#45;&gt;GetInfo -->
<g id="edge5" class="edge">
<title>Start&#45;&gt;GetInfo</title>
<path fill="none" stroke="#ffa500" d="M3166.29,-924.85C3132.41,-910.58 3070.55,-884.59 3068,-884 3040.4,-877.66 2970.72,-887.56 2936.7,-879.35"/>
<polygon fill="#ffa500" stroke="#ffa500" points="2937.59,-875.95 2927,-876 2935.31,-882.57 2937.59,-875.95"/>
<text text-anchor="middle" x="3149.5" y="-887.8" font-family="Times,serif" font-size="14.00" fill="#ffa500">MakePrimary</text>
</g>
<!-- Start&#45;&gt;Stray -->
<g id="edge6" class="edge">
<title>Start&#45;&gt;Stray</title>
<path fill="none" stroke="#40e0d0" d="M3164.78,-925.49C3155.61,-922.41 3144.91,-919.16 3135,-917 3069.4,-902.68 3047.87,-922.59 2985,-899 2973.92,-894.84 2974.25,-887.67 2963,-884 2936.79,-875.45 2742.54,-877.36 2715,-876 2271.86,-854.06 1736.28,-813.06 1578.09,-800.65"/>
<polygon fill="#40e0d0" stroke="#40e0d0" points="1578.03,-797.14 1567.79,-799.84 1577.48,-804.11 1578.03,-797.14"/>
<text text-anchor="middle" x="3024.5" y="-887.8" font-family="Times,serif" font-size="14.00" fill="#40e0d0">MakeStray</text>
</g>
<!-- WaitActingChange -->
<g id="node5" class="node">
<title>WaitActingChange</title>
<ellipse fill="none" stroke="black" cx="3133" cy="-580" rx="96.38" ry="18"/>
<text text-anchor="middle" x="3133" y="-576.3" font-family="Times,serif" font-size="14.00">WaitActingChange</text>
</g>
<!-- WaitActingChange&#45;&gt;Reset -->
<g id="edge51" class="edge">
<title>WaitActingChange&#45;&gt;Reset</title>
<path fill="none" stroke="#1e90ff" d="M3152.9,-562.22C3165.98,-551.74 3183.81,-538.49 3201,-529 3216.53,-520.43 3234.6,-512.92 3250.38,-507.07"/>
<polygon fill="#1e90ff" stroke="#1e90ff" points="3251.64,-510.34 3259.87,-503.65 3249.27,-503.75 3251.64,-510.34"/>
<text text-anchor="middle" x="3230.5" y="-532.8" font-family="Times,serif" font-size="14.00" fill="#1e90ff">AdvMap</text>
</g>
<!-- GetInfo&#45;&gt;Reset -->
<g id="edge49" class="edge">
<title>GetInfo&#45;&gt;Reset</title>
<path fill="none" stroke="#c71585" d="M3029,-752.15C3099.34,-721.74 3117.07,-697.64 3190,-652 3200.35,-645.53 3201.42,-640.85 3213,-637 3226.1,-632.64 3327.56,-639.07 3337,-629 3359.8,-604.68 3347.87,-585.51 3337,-554 3332.25,-540.24 3322.91,-527.24 3313.8,-516.87"/>
<polygon fill="#c71585" stroke="#c71585" points="3316.32,-514.44 3306.96,-509.49 3311.19,-519.2 3316.32,-514.44"/>
<text text-anchor="middle" x="3242.5" y="-640.8" font-family="Times,serif" font-size="14.00" fill="#c71585">AdvMap</text>
</g>
<!-- GetLog -->
<g id="node7" class="node">
<title>GetLog</title>
<ellipse fill="none" stroke="black" cx="2825" cy="-688" rx="44.39" ry="18"/>
<text text-anchor="middle" x="2825" y="-684.3" font-family="Times,serif" font-size="14.00">GetLog</text>
</g>
<!-- GetInfo&#45;&gt;GetLog -->
<g id="edge36" class="edge">
<title>GetInfo&#45;&gt;GetLog</title>
<path fill="none" stroke="#000000" d="M2823.83,-787.78C2808.54,-782.71 2792.09,-774.19 2783,-760 2772.83,-744.13 2784.22,-725.81 2797.69,-711.75"/>
<polygon fill="#000000" stroke="#000000" points="2800.51,-713.89 2805.27,-704.42 2795.64,-708.86 2800.51,-713.89"/>
<text text-anchor="middle" x="2810" y="-748.8" font-family="Times,serif" font-size="14.00" fill="#000000">GotInfo</text>
</g>
<!-- Down -->
<g id="node10" class="node">
<title>Down</title>
<ellipse fill="none" stroke="black" cx="2928" cy="-688" rx="37.09" ry="18"/>
<text text-anchor="middle" x="2928" y="-684.3" font-family="Times,serif" font-size="14.00">Down</text>
</g>
<!-- GetInfo&#45;&gt;Down -->
<g id="edge37" class="edge">
<title>GetInfo&#45;&gt;Down</title>
<path fill="none" stroke="#1e90ff" d="M2855.15,-778.82C2853.59,-768.33 2853.47,-754.84 2860,-745 2864.84,-737.71 2870.73,-741.88 2878,-737 2888.51,-729.94 2898.82,-720.58 2907.33,-712"/>
<polygon fill="#1e90ff" stroke="#1e90ff" points="2909.9,-714.38 2914.29,-704.74 2904.85,-709.53 2909.9,-714.38"/>
<text text-anchor="middle" x="2887" y="-748.8" font-family="Times,serif" font-size="14.00" fill="#1e90ff">IsDown</text>
</g>
<!-- Activating -->
<g id="node22" class="node">
<title>Activating</title>
<polygon fill="lightgrey" stroke="black" points="2153,-706 2071.39,-688 2153,-670 2234.61,-688 2153,-706"/>
<polyline fill="none" stroke="black" points="2083.11,-690.58 2083.11,-685.42 "/>
<polyline fill="none" stroke="black" points="2141.28,-672.58 2164.72,-672.58 "/>
<polyline fill="none" stroke="black" points="2222.89,-685.42 2222.89,-690.58 "/>
<polyline fill="none" stroke="black" points="2164.72,-703.42 2141.28,-703.42 "/>
<text text-anchor="middle" x="2153" y="-684.3" font-family="Times,serif" font-size="14.00">Activating</text>
</g>
<!-- GetInfo&#45;&gt;Activating -->
<g id="edge7" class="edge">
<title>GetInfo&#45;&gt;Activating</title>
<path fill="none" stroke="#c71585" d="M2719,-764.71C2664.83,-755.19 2609.28,-746.41 2555.15,-738.47"/>
<polygon fill="#c71585" stroke="#c71585" points="2555.5,-734.98 2545.1,-737 2554.49,-741.91 2555.5,-734.98"/>
<text text-anchor="middle" x="2716.5" y="-748.8" font-family="Times,serif" font-size="14.00" fill="#c71585">Activate</text>
</g>
<!-- GetLog&#45;&gt;Reset -->
<g id="edge50" class="edge">
<title>GetLog&#45;&gt;Reset</title>
<path fill="none" stroke="#000000" d="M2857.85,-675.8C2865.68,-673.53 2874.07,-671.4 2882,-670 2947.99,-658.35 3124.89,-685.37 3183,-652 3189.95,-648.01 3186.27,-641.35 3193,-637 3211.16,-625.26 3224.37,-642.83 3241,-629 3257.89,-614.95 3274.75,-556.24 3283.62,-521.04"/>
<polygon fill="#000000" stroke="#000000" points="3287.11,-521.49 3286.1,-510.95 3280.31,-519.82 3287.11,-521.49"/>
<text text-anchor="middle" x="3303.5" y="-576.3" font-family="Times,serif" font-size="14.00" fill="#000000">AdvMap</text>
</g>
<!-- GetLog&#45;&gt;WaitActingChange -->
<g id="edge38" class="edge">
<title>GetLog&#45;&gt;WaitActingChange</title>
<path fill="none" stroke="#ff0000" d="M2858.54,-676.07C2866.19,-673.83 2874.33,-671.66 2882,-670 2939.44,-657.57 2957.51,-671.34 3013,-652 3030.56,-645.88 3070.25,-621.54 3099.19,-603.05"/>
<polygon fill="#ff0000" stroke="#ff0000" points="3101.27,-605.88 3107.79,-597.53 3097.49,-599.99 3101.27,-605.88"/>
<text text-anchor="middle" x="3111" y="-640.8" font-family="Times,serif" font-size="14.00" fill="#ff0000">NeedActingChange</text>
</g>
<!-- GetMissing -->
<g id="node8" class="node">
<title>GetMissing</title>
<ellipse fill="none" stroke="black" cx="2792" cy="-580" rx="63.89" ry="18"/>
<text text-anchor="middle" x="2792" y="-576.3" font-family="Times,serif" font-size="14.00">GetMissing</text>
</g>
<!-- GetLog&#45;&gt;GetMissing -->
<g id="edge68" class="edge">
<title>GetLog&#45;&gt;GetMissing</title>
<path fill="none" stroke="#ffa500" d="M2786,-679.33C2770.85,-674.24 2755,-665.83 2746,-652 2735.56,-635.96 2747.66,-617.86 2762.07,-603.94"/>
<polygon fill="#ffa500" stroke="#ffa500" points="2764.6,-606.37 2769.7,-597.08 2759.92,-601.17 2764.6,-606.37"/>
<text text-anchor="middle" x="2772" y="-640.8" font-family="Times,serif" font-size="14.00" fill="#ffa500">GotLog</text>
</g>
<!-- Incomplete -->
<g id="node11" class="node">
<title>Incomplete</title>
<ellipse fill="none" stroke="black" cx="2936" cy="-580" rx="62.29" ry="18"/>
<text text-anchor="middle" x="2936" y="-576.3" font-family="Times,serif" font-size="14.00">Incomplete</text>
</g>
<!-- GetLog&#45;&gt;Incomplete -->
<g id="edge39" class="edge">
<title>GetLog&#45;&gt;Incomplete</title>
<path fill="none" stroke="#0000ff" d="M2818.05,-669.86C2814.97,-659.24 2813.52,-645.96 2821,-637 2833.74,-621.74 2846.6,-636.5 2865,-629 2880.45,-622.7 2895.98,-612.71 2908.57,-603.47"/>
<polygon fill="#0000ff" stroke="#0000ff" points="2910.92,-606.08 2916.77,-597.25 2906.69,-600.51 2910.92,-606.08"/>
<text text-anchor="middle" x="2867.5" y="-640.8" font-family="Times,serif" font-size="14.00" fill="#0000ff">IsIncomplete</text>
</g>
<!-- WaitUpThru -->
<g id="node9" class="node">
<title>WaitUpThru</title>
<ellipse fill="none" stroke="black" cx="2794" cy="-493" rx="66.89" ry="18"/>
<text text-anchor="middle" x="2794" y="-489.3" font-family="Times,serif" font-size="14.00">WaitUpThru</text>
</g>
<!-- GetMissing&#45;&gt;WaitUpThru -->
<g id="edge40" class="edge">
<title>GetMissing&#45;&gt;WaitUpThru</title>
<path fill="none" stroke="#ffa500" d="M2792.31,-561.87C2792.49,-552.22 2792.73,-539.94 2793,-529 2793.06,-526.45 2793.13,-523.79 2793.2,-521.13"/>
<polygon fill="#ffa500" stroke="#ffa500" points="2796.7,-521.18 2793.48,-511.09 2789.7,-520.99 2796.7,-521.18"/>
<text text-anchor="middle" x="2839.5" y="-532.8" font-family="Times,serif" font-size="14.00" fill="#ffa500">NeedUpThru</text>
</g>
<!-- Down&#45;&gt;GetInfo -->
<g id="edge42" class="edge">
<title>Down&#45;&gt;GetInfo</title>
<path fill="none" stroke="#c71585" d="M2928.3,-706.22C2927.76,-721.58 2924.91,-744.02 2914,-760 2908.56,-767.96 2900.65,-774.5 2892.52,-779.68"/>
<polygon fill="#c71585" stroke="#c71585" points="2890.62,-776.74 2883.71,-784.77 2894.12,-782.8 2890.62,-776.74"/>
<text text-anchor="middle" x="2963.5" y="-748.8" font-family="Times,serif" font-size="14.00" fill="#c71585">MNotifyRec</text>
</g>
<!-- Incomplete&#45;&gt;Reset -->
<g id="edge52" class="edge">
<title>Incomplete&#45;&gt;Reset</title>
<path fill="none" stroke="#ff0000" d="M2940.02,-561.79C2943.59,-550.57 2950,-536.63 2961,-529 2983.68,-513.26 3157.81,-501.43 3243.15,-496.5"/>
<polygon fill="#ff0000" stroke="#ff0000" points="3243.56,-499.99 3253.35,-495.92 3243.17,-493 3243.56,-499.99"/>
<text text-anchor="middle" x="2990.5" y="-532.8" font-family="Times,serif" font-size="14.00" fill="#ff0000">AdvMap</text>
</g>
<!-- Incomplete&#45;&gt;GetLog -->
<g id="edge43" class="edge">
<title>Incomplete&#45;&gt;GetLog</title>
<path fill="none" stroke="#000000" d="M2934.82,-598.28C2932.87,-614.11 2927.7,-637.25 2914,-652 2907.41,-659.1 2888.34,-667 2869.54,-673.49"/>
<polygon fill="#000000" stroke="#000000" points="2868.17,-670.26 2859.8,-676.75 2870.39,-676.89 2868.17,-670.26"/>
<text text-anchor="middle" x="2966.5" y="-640.8" font-family="Times,serif" font-size="14.00" fill="#000000">MNotifyRec</text>
</g>
<!-- Clean -->
<g id="node12" class="node">
<title>Clean</title>
<ellipse fill="none" stroke="black" cx="2601" cy="-688" rx="37.09" ry="18"/>
<text text-anchor="middle" x="2601" y="-684.3" font-family="Times,serif" font-size="14.00">Clean</text>
</g>
<!-- WaitLocalRecoveryReserved -->
<g id="node21" class="node">
<title>WaitLocalRecoveryReserved</title>
<ellipse fill="none" stroke="black" cx="2378" cy="-580" rx="141.88" ry="18"/>
<text text-anchor="middle" x="2378" y="-576.3" font-family="Times,serif" font-size="14.00">WaitLocalRecoveryReserved</text>
</g>
<!-- Clean&#45;&gt;WaitLocalRecoveryReserved -->
<g id="edge8" class="edge">
<title>Clean&#45;&gt;WaitLocalRecoveryReserved</title>
<path fill="none" stroke="#000000" d="M2575.42,-674.84C2537.96,-657.03 2467.67,-623.62 2421.86,-601.85"/>
<polygon fill="#000000" stroke="#000000" points="2423.29,-598.65 2412.76,-597.52 2420.29,-604.98 2423.29,-598.65"/>
<text text-anchor="middle" x="2565" y="-640.8" font-family="Times,serif" font-size="14.00" fill="#000000">DoRecovery</text>
</g>
<!-- Recovered -->
<g id="node13" class="node">
<title>Recovered</title>
<ellipse fill="none" stroke="black" cx="2614" cy="-58" rx="59.59" ry="18"/>
<text text-anchor="middle" x="2614" y="-54.3" font-family="Times,serif" font-size="14.00">Recovered</text>
</g>
<!-- Recovered&#45;&gt;Clean -->
<g id="edge12" class="edge">
<title>Recovered&#45;&gt;Clean</title>
<path fill="none" stroke="#ffa500" d="M2626.76,-75.96C2646.43,-103.28 2683.33,-159.52 2697,-214 2715.94,-289.5 2682.6,-310.36 2677,-388 2675.85,-403.96 2672.99,-408.51 2677,-424 2679.3,-432.86 2684.7,-433.14 2687,-442 2688.67,-448.45 2688.19,-450.44 2687,-457 2673.02,-534.29 2634.62,-619.45 2614.19,-661.13"/>
<polygon fill="#ffa500" stroke="#ffa500" points="2610.96,-659.77 2609.65,-670.29 2617.23,-662.88 2610.96,-659.77"/>
<text text-anchor="middle" x="2711.5" y="-358.8" font-family="Times,serif" font-size="14.00" fill="#ffa500">GoClean</text>
</g>
<!-- Recovered&#45;&gt;WaitLocalRecoveryReserved -->
<g id="edge9" class="edge">
<title>Recovered&#45;&gt;WaitLocalRecoveryReserved</title>
<path fill="none" stroke="#1e90ff" d="M2616.2,-76.33C2618.06,-98.63 2617.93,-137.52 2599,-163 2584.24,-182.86 2518.29,-197.33 2500,-214 2485.97,-226.79 2492.06,-238.44 2477,-250 2417.45,-295.69 2385.31,-272.2 2316,-301 2284.06,-314.27 2279.04,-323.97 2247,-337 2221.21,-347.49 2207.56,-336.23 2187,-355 2177.1,-364.04 2144.55,-453.83 2142,-467 2138.29,-486.2 2130.24,-495.37 2142,-511 2159.51,-534.27 2225.53,-552.07 2283.3,-563.61"/>
<polygon fill="#1e90ff" stroke="#1e90ff" points="2282.75,-567.06 2293.23,-565.55 2284.09,-560.19 2282.75,-567.06"/>
<text text-anchor="middle" x="2359" y="-315.3" font-family="Times,serif" font-size="14.00" fill="#1e90ff">DoRecovery</text>
</g>
<!-- Backfilling -->
<g id="node14" class="node">
<title>Backfilling</title>
<ellipse fill="none" stroke="black" cx="2515" cy="-145" rx="59.59" ry="18"/>
<text text-anchor="middle" x="2515" y="-141.3" font-family="Times,serif" font-size="14.00">Backfilling</text>
</g>
<!-- Backfilling&#45;&gt;Recovered -->
<g id="edge55" class="edge">
<title>Backfilling&#45;&gt;Recovered</title>
<path fill="none" stroke="#40e0d0" d="M2516.82,-126.9C2518.76,-116.29 2522.76,-103.01 2531,-94 2538.84,-85.43 2549.11,-78.89 2559.74,-73.92"/>
<polygon fill="#40e0d0" stroke="#40e0d0" points="2561.29,-77.06 2569.13,-69.94 2558.56,-70.62 2561.29,-77.06"/>
<text text-anchor="middle" x="2566.5" y="-97.8" font-family="Times,serif" font-size="14.00" fill="#40e0d0">Backfilled</text>
</g>
<!-- WaitLocalBackfillReserved -->
<g id="node16" class="node">
<title>WaitLocalBackfillReserved</title>
<ellipse fill="none" stroke="black" cx="2070" cy="-319" rx="133.78" ry="18"/>
<text text-anchor="middle" x="2070" y="-315.3" font-family="Times,serif" font-size="14.00">WaitLocalBackfillReserved</text>
</g>
<!-- Backfilling&#45;&gt;WaitLocalBackfillReserved -->
<g id="edge59" class="edge">
<title>Backfilling&#45;&gt;WaitLocalBackfillReserved</title>
<path fill="none" stroke="#ff0000" d="M2468.63,-156.41C2400.77,-171.97 2280.74,-200.87 2265,-214 2238.89,-235.77 2258.67,-261.92 2232,-283 2221.44,-291.34 2196.98,-298.26 2170.18,-303.68"/>
<polygon fill="#ff0000" stroke="#ff0000" points="2169.38,-300.27 2160.23,-305.61 2170.71,-307.14 2169.38,-300.27"/>
<text text-anchor="middle" x="2366" y="-228.3" font-family="Times,serif" font-size="14.00" fill="#ff0000">RemoteReservationRevoked</text>
</g>
<!-- NotBackfilling -->
<g id="node17" class="node">
<title>NotBackfilling</title>
<ellipse fill="none" stroke="black" cx="1990" cy="-58" rx="77.19" ry="18"/>
<text text-anchor="middle" x="1990" y="-54.3" font-family="Times,serif" font-size="14.00">NotBackfilling</text>
</g>
<!-- Backfilling&#45;&gt;NotBackfilling -->
<g id="edge56" class="edge">
<title>Backfilling&#45;&gt;NotBackfilling</title>
<path fill="none" stroke="#c71585" d="M2478.49,-130.53C2461.79,-124.22 2441.8,-116.46 2424,-109 2409.14,-102.77 2406.58,-98.09 2391,-94 2333.41,-78.88 2174.83,-68.38 2075.21,-63.05"/>
<polygon fill="#c71585" stroke="#c71585" points="2075.33,-59.55 2065.16,-62.52 2074.96,-66.54 2075.33,-59.55"/>
<text text-anchor="middle" x="2471" y="-97.8" font-family="Times,serif" font-size="14.00" fill="#c71585">DeferBackfill</text>
</g>
<!-- Backfilling&#45;&gt;NotBackfilling -->
<g id="edge57" class="edge">
<title>Backfilling&#45;&gt;NotBackfilling</title>
<path fill="none" stroke="#000000" d="M2471.42,-132.58C2461.49,-130.37 2450.93,-128.33 2441,-127 2416.59,-123.73 2016.41,-125.36 1998,-109 1991.65,-103.35 1988.95,-94.84 1988.06,-86.43"/>
<polygon fill="#000000" stroke="#000000" points="1991.55,-86.31 1987.73,-76.43 1984.56,-86.53 1991.55,-86.31"/>
<text text-anchor="middle" x="2055.5" y="-97.8" font-family="Times,serif" font-size="14.00" fill="#000000">UnfoundBackfill</text>
</g>
<!-- Backfilling&#45;&gt;NotBackfilling -->
<g id="edge58" class="edge">
<title>Backfilling&#45;&gt;NotBackfilling</title>
<path fill="none" stroke="#1e90ff" d="M2471.01,-132.62C2461.19,-130.44 2450.79,-128.4 2441,-127 2372.91,-117.27 2196.3,-133.44 2132,-109 2121.94,-105.18 2122.58,-98.9 2113,-94 2095.49,-85.05 2075.38,-78.1 2056.56,-72.84"/>
<polygon fill="#1e90ff" stroke="#1e90ff" points="2057.42,-69.45 2046.85,-70.24 2055.61,-76.21 2057.42,-69.45"/>
<text text-anchor="middle" x="2259.5" y="-97.8" font-family="Times,serif" font-size="14.00" fill="#1e90ff">RemoteReservationRevokedTooFull</text>
</g>
<!-- WaitRemoteBackfillReserved -->
<g id="node15" class="node">
<title>WaitRemoteBackfillReserved</title>
<ellipse fill="none" stroke="black" cx="2070" cy="-232" rx="144.87" ry="18"/>
<text text-anchor="middle" x="2070" y="-228.3" font-family="Times,serif" font-size="14.00">WaitRemoteBackfillReserved</text>
</g>
<!-- WaitRemoteBackfillReserved&#45;&gt;Backfilling -->
<g id="edge13" class="edge">
<title>WaitRemoteBackfillReserved&#45;&gt;Backfilling</title>
<path fill="none" stroke="#40e0d0" d="M2099.47,-214.2C2109.25,-208.52 2120.15,-202.08 2130,-196 2140.39,-189.59 2141.42,-184.84 2153,-181 2213.87,-160.82 2377.54,-172.21 2441,-163 2447.57,-162.05 2454.43,-160.81 2461.19,-159.44"/>
<polygon fill="#40e0d0" stroke="#40e0d0" points="2461.95,-162.85 2471.01,-157.35 2460.5,-156.01 2461.95,-162.85"/>
<text text-anchor="middle" x="2226.5" y="-184.8" font-family="Times,serif" font-size="14.00" fill="#40e0d0">AllBackfillsReserved</text>
</g>
<!-- WaitRemoteBackfillReserved&#45;&gt;NotBackfilling -->
<g id="edge24" class="edge">
<title>WaitRemoteBackfillReserved&#45;&gt;NotBackfilling</title>
<path fill="none" stroke="#ff0000" d="M2091.67,-213.9C2113.97,-196.07 2145.89,-169.79 2149,-163 2155.67,-148.46 2159.68,-138.91 2149,-127 2124.53,-99.7 2009.47,-136.3 1985,-109 1979.45,-102.81 1978.73,-94.3 1980,-86.06"/>
<polygon fill="#ff0000" stroke="#ff0000" points="1983.41,-86.85 1982.36,-76.31 1976.61,-85.21 1983.41,-86.85"/>
<text text-anchor="middle" x="2283" y="-141.3" font-family="Times,serif" font-size="14.00" fill="#ff0000">RemoteReservationRejectedTooFull</text>
</g>
<!-- WaitRemoteBackfillReserved&#45;&gt;NotBackfilling -->
<g id="edge60" class="edge">
<title>WaitRemoteBackfillReserved&#45;&gt;NotBackfilling</title>
<path fill="none" stroke="#0000ff" d="M2062.7,-214C2057.03,-202.87 2048.1,-188.97 2036,-181 2000.84,-157.84 1969.79,-195.47 1943,-163 1923.26,-139.07 1945.24,-105.52 1965.29,-83.06"/>
<polygon fill="#0000ff" stroke="#0000ff" points="1967.98,-85.32 1972.23,-75.61 1962.86,-80.54 1967.98,-85.32"/>
<text text-anchor="middle" x="2044" y="-141.3" font-family="Times,serif" font-size="14.00" fill="#0000ff">RemoteReservationRevoked</text>
</g>
<!-- WaitLocalBackfillReserved&#45;&gt;WaitRemoteBackfillReserved -->
<g id="edge14" class="edge">
<title>WaitLocalBackfillReserved&#45;&gt;WaitRemoteBackfillReserved</title>
<path fill="none" stroke="#c71585" d="M2070,-300.8C2070,-289.16 2070,-273.55 2070,-260.24"/>
<polygon fill="#c71585" stroke="#c71585" points="2073.5,-260.18 2070,-250.18 2066.5,-260.18 2073.5,-260.18"/>
<text text-anchor="middle" x="2149" y="-271.8" font-family="Times,serif" font-size="14.00" fill="#c71585">LocalBackfillReserved</text>
</g>
<!-- NotBackfilling&#45;&gt;WaitLocalBackfillReserved -->
<g id="edge15" class="edge">
<title>NotBackfilling&#45;&gt;WaitLocalBackfillReserved</title>
<path fill="none" stroke="#000000" d="M1962.04,-74.97C1954.7,-80.25 1947.36,-86.67 1942,-94 1900.52,-150.74 1877.63,-191.11 1916,-250 1932.13,-274.75 1959.53,-290.56 1986.79,-300.63"/>
<polygon fill="#000000" stroke="#000000" points="1985.81,-303.99 1996.4,-303.95 1988.1,-297.38 1985.81,-303.99"/>
<text text-anchor="middle" x="1953" y="-184.8" font-family="Times,serif" font-size="14.00" fill="#000000">RequestBackfill</text>
</g>
<!-- NotRecovering -->
<g id="node18" class="node">
<title>NotRecovering</title>
<ellipse fill="none" stroke="black" cx="2553" cy="-319" rx="79.89" ry="18"/>
<text text-anchor="middle" x="2553" y="-315.3" font-family="Times,serif" font-size="14.00">NotRecovering</text>
</g>
<!-- NotRecovering&#45;&gt;WaitLocalRecoveryReserved -->
<g id="edge10" class="edge">
<title>NotRecovering&#45;&gt;WaitLocalRecoveryReserved</title>
<path fill="none" stroke="#ff0000" d="M2591.96,-334.83C2600.34,-339.92 2608.13,-346.53 2613,-355 2636.25,-395.47 2613.73,-418.06 2588,-457 2554.21,-508.13 2534.64,-514.34 2481,-544 2469.85,-550.17 2457.42,-555.51 2445.17,-560.04"/>
<polygon fill="#ff0000" stroke="#ff0000" points="2443.66,-556.86 2435.4,-563.5 2446,-563.46 2443.66,-556.86"/>
<text text-anchor="middle" x="2640" y="-445.8" font-family="Times,serif" font-size="14.00" fill="#ff0000">DoRecovery</text>
</g>
<!-- Recovering -->
<g id="node19" class="node">
<title>Recovering</title>
<ellipse fill="none" stroke="black" cx="2324" cy="-406" rx="63.09" ry="18"/>
<text text-anchor="middle" x="2324" y="-402.3" font-family="Times,serif" font-size="14.00">Recovering</text>
</g>
<!-- Recovering&#45;&gt;Recovered -->
<g id="edge33" class="edge">
<title>Recovering&#45;&gt;Recovered</title>
<path fill="none" stroke="#ffa500" d="M2322.28,-387.65C2322.19,-376.95 2324.01,-363.66 2332,-355 2354.96,-330.13 2376.06,-352.78 2406,-337 2478.39,-298.84 2478.42,-264.26 2543,-214 2575.1,-189.02 2599.86,-198.89 2619,-163 2631.7,-139.18 2627.88,-107.76 2622.49,-85.71"/>
<polygon fill="#ffa500" stroke="#ffa500" points="2625.86,-84.75 2619.88,-76.01 2619.1,-86.57 2625.86,-84.75"/>
<text text-anchor="middle" x="2620" y="-228.3" font-family="Times,serif" font-size="14.00" fill="#ffa500">AllReplicasRecovered</text>
</g>
<!-- Recovering&#45;&gt;WaitLocalBackfillReserved -->
<g id="edge17" class="edge">
<title>Recovering&#45;&gt;WaitLocalBackfillReserved</title>
<path fill="none" stroke="#ff0000" d="M2277.91,-393.69C2256.04,-387.7 2229.76,-379.59 2207,-370 2194.35,-364.67 2192.6,-360.44 2180,-355 2165.89,-348.91 2150.36,-343.32 2135.53,-338.49"/>
<polygon fill="#ff0000" stroke="#ff0000" points="2136.49,-335.13 2125.9,-335.43 2134.37,-341.8 2136.49,-335.13"/>
<text text-anchor="middle" x="2263" y="-358.8" font-family="Times,serif" font-size="14.00" fill="#ff0000">RequestBackfill</text>
</g>
<!-- Recovering&#45;&gt;NotRecovering -->
<g id="edge66" class="edge">
<title>Recovering&#45;&gt;NotRecovering</title>
<path fill="none" stroke="#ff0000" d="M2328.18,-388.01C2331.82,-376.89 2338.25,-362.98 2349,-355 2351.73,-352.98 2422.37,-341.14 2479.77,-331.78"/>
<polygon fill="#ff0000" stroke="#ff0000" points="2480.64,-335.19 2489.95,-330.13 2479.51,-328.28 2480.64,-335.19"/>
<text text-anchor="middle" x="2402" y="-358.8" font-family="Times,serif" font-size="14.00" fill="#ff0000">DeferRecovery</text>
</g>
<!-- Recovering&#45;&gt;NotRecovering -->
<g id="edge67" class="edge">
<title>Recovering&#45;&gt;NotRecovering</title>
<path fill="none" stroke="#0000ff" d="M2375.04,-395.32C2399.82,-389.63 2429.63,-381.31 2455,-370 2466.15,-365.03 2467.33,-360.93 2478,-355 2487.71,-349.6 2498.4,-344.27 2508.59,-339.46"/>
<polygon fill="#0000ff" stroke="#0000ff" points="2510.17,-342.59 2517.76,-335.2 2507.22,-336.24 2510.17,-342.59"/>
<text text-anchor="middle" x="2542" y="-358.8" font-family="Times,serif" font-size="14.00" fill="#0000ff">UnfoundRecovery</text>
</g>
<!-- WaitRemoteRecoveryReserved -->
<g id="node20" class="node">
<title>WaitRemoteRecoveryReserved</title>
<ellipse fill="none" stroke="black" cx="2304" cy="-493" rx="153.27" ry="18"/>
<text text-anchor="middle" x="2304" y="-489.3" font-family="Times,serif" font-size="14.00">WaitRemoteRecoveryReserved</text>
</g>
<!-- WaitRemoteRecoveryReserved&#45;&gt;Recovering -->
<g id="edge30" class="edge">
<title>WaitRemoteRecoveryReserved&#45;&gt;Recovering</title>
<path fill="none" stroke="#1e90ff" d="M2297.78,-474.96C2295.13,-465.12 2293.38,-452.62 2297,-442 2298.21,-438.45 2299.94,-434.98 2301.95,-431.69"/>
<polygon fill="#1e90ff" stroke="#1e90ff" points="2304.85,-433.65 2307.76,-423.46 2299.13,-429.61 2304.85,-433.65"/>
<text text-anchor="middle" x="2371" y="-445.8" font-family="Times,serif" font-size="14.00" fill="#1e90ff">AllRemotesReserved</text>
</g>
<!-- WaitLocalRecoveryReserved&#45;&gt;NotRecovering -->
<g id="edge65" class="edge">
<title>WaitLocalRecoveryReserved&#45;&gt;NotRecovering</title>
<path fill="none" stroke="#1e90ff" d="M2447.25,-564.08C2456.6,-559.17 2465.01,-552.65 2471,-544 2475.48,-537.52 2477.38,-548.41 2466,-467 2464.46,-455.96 2458.17,-451.5 2464,-442 2501.03,-381.7 2568.97,-430.3 2606,-370 2612.54,-359.35 2606.14,-349.47 2595.81,-341.36"/>
<polygon fill="#1e90ff" stroke="#1e90ff" points="2597.51,-338.28 2587.29,-335.51 2593.55,-344.06 2597.51,-338.28"/>
<text text-anchor="middle" x="2524" y="-445.8" font-family="Times,serif" font-size="14.00" fill="#1e90ff">RecoveryTooFull</text>
</g>
<!-- WaitLocalRecoveryReserved&#45;&gt;WaitRemoteRecoveryReserved -->
<g id="edge31" class="edge">
<title>WaitLocalRecoveryReserved&#45;&gt;WaitRemoteRecoveryReserved</title>
<path fill="none" stroke="#ff0000" d="M2321.68,-563.37C2313.09,-558.53 2305.32,-552.22 2300,-544 2295.66,-537.29 2294.94,-528.97 2295.81,-521.05"/>
<polygon fill="#ff0000" stroke="#ff0000" points="2299.29,-521.51 2297.7,-511.04 2292.41,-520.22 2299.29,-521.51"/>
<text text-anchor="middle" x="2385.5" y="-532.8" font-family="Times,serif" font-size="14.00" fill="#ff0000">LocalRecoveryReserved</text>
</g>
<!-- Activating&#45;&gt;Recovered -->
<g id="edge32" class="edge">
<title>Activating&#45;&gt;Recovered</title>
<path fill="none" stroke="#0000ff" d="M2144.93,-671.5C2112.8,-609.36 1996.9,-380.36 2020,-355 2049.01,-323.16 2174.28,-355.87 2213,-337 2232.78,-327.36 2227.93,-311.99 2247,-301 2302.39,-269.08 2324.7,-282.35 2387,-268 2422.51,-259.82 2440.6,-275.12 2467,-250 2479.03,-238.55 2467,-227.25 2477,-214 2493.95,-191.55 2505.53,-192.93 2531,-181 2553.53,-170.45 2566.46,-180.64 2584,-163 2601.84,-145.06 2595.04,-133.32 2602,-109 2604.1,-101.65 2606.14,-93.62 2607.92,-86.19"/>
<polygon fill="#0000ff" stroke="#0000ff" points="2611.39,-86.75 2610.25,-76.22 2604.57,-85.16 2611.39,-86.75"/>
<text text-anchor="middle" x="2097" y="-358.8" font-family="Times,serif" font-size="14.00" fill="#0000ff">AllReplicasRecovered</text>
</g>
<!-- Activating&#45;&gt;WaitLocalBackfillReserved -->
<g id="edge16" class="edge">
<title>Activating&#45;&gt;WaitLocalBackfillReserved</title>
<path fill="none" stroke="#1e90ff" d="M2116.05,-678.09C2051.03,-661.33 1923,-622.99 1923,-581 1923,-581 1923,-581 1923,-405 1923,-370.66 1953.44,-349.97 1986.62,-337.62"/>
<polygon fill="#1e90ff" stroke="#1e90ff" points="1988.19,-340.78 1996.5,-334.2 1985.91,-334.16 1988.19,-340.78"/>
<text text-anchor="middle" x="1979" y="-489.3" font-family="Times,serif" font-size="14.00" fill="#1e90ff">RequestBackfill</text>
</g>
<!-- Activating&#45;&gt;WaitLocalRecoveryReserved -->
<g id="edge11" class="edge">
<title>Activating&#45;&gt;WaitLocalRecoveryReserved</title>
<path fill="none" stroke="#0000ff" d="M2177.94,-675.25C2215.51,-657.55 2287.18,-623.79 2333.76,-601.84"/>
<polygon fill="#0000ff" stroke="#0000ff" points="2335.45,-604.91 2343.01,-597.49 2332.47,-598.58 2335.45,-604.91"/>
<text text-anchor="middle" x="2301" y="-640.8" font-family="Times,serif" font-size="14.00" fill="#0000ff">DoRecovery</text>
</g>
<!-- RepRecovering -->
<g id="node23" class="node">
<title>RepRecovering</title>
<ellipse fill="none" stroke="black" cx="414" cy="-796" rx="81.49" ry="18"/>
<text text-anchor="middle" x="414" y="-792.3" font-family="Times,serif" font-size="14.00">RepRecovering</text>
</g>
<!-- RepNotRecovering -->
<g id="node26" class="node">
<title>RepNotRecovering</title>
<polygon fill="lightgrey" stroke="black" points="722,-706 583.41,-688 722,-670 860.59,-688 722,-706"/>
<polyline fill="none" stroke="black" points="595.31,-689.55 595.31,-686.45 "/>
<polyline fill="none" stroke="black" points="710.1,-671.55 733.9,-671.55 "/>
<polyline fill="none" stroke="black" points="848.69,-686.45 848.69,-689.55 "/>
<polyline fill="none" stroke="black" points="733.9,-704.45 710.1,-704.45 "/>
<text text-anchor="middle" x="722" y="-684.3" font-family="Times,serif" font-size="14.00">RepNotRecovering</text>
</g>
<!-- RepRecovering&#45;&gt;RepNotRecovering -->
<g id="edge20" class="edge">
<title>RepRecovering&#45;&gt;RepNotRecovering</title>
<path fill="none" stroke="#40e0d0" d="M471.08,-783.17C480.39,-781.36 489.96,-779.57 499,-778 553,-768.62 570.09,-780.31 621,-760 625.06,-758.38 664.95,-730 693.37,-709.61"/>
<polygon fill="#40e0d0" stroke="#40e0d0" points="695.68,-712.26 701.76,-703.58 691.6,-706.57 695.68,-712.26"/>
<text text-anchor="middle" x="694.5" y="-748.8" font-family="Times,serif" font-size="14.00" fill="#40e0d0">RecoveryDone</text>
</g>
<!-- RepRecovering&#45;&gt;RepNotRecovering -->
<g id="edge22" class="edge">
<title>RepRecovering&#45;&gt;RepNotRecovering</title>
<path fill="none" stroke="#000000" d="M469.61,-782.84C479.37,-781.01 489.46,-779.3 499,-778 526.38,-774.28 728.18,-780.23 747,-760 759.05,-747.04 751.36,-727.92 741.44,-712.79"/>
<polygon fill="#000000" stroke="#000000" points="744.12,-710.53 735.47,-704.42 738.42,-714.59 744.12,-710.53"/>
<text text-anchor="middle" x="881" y="-748.8" font-family="Times,serif" font-size="14.00" fill="#000000">RemoteReservationRejectedTooFull</text>
</g>
<!-- RepRecovering&#45;&gt;RepNotRecovering -->
<g id="edge26" class="edge">
<title>RepRecovering&#45;&gt;RepNotRecovering</title>
<path fill="none" stroke="#ffa500" d="M407.27,-777.99C404.21,-767.15 402.85,-753.57 411,-745 423.11,-732.26 551.7,-740.1 569,-737 607.45,-730.11 649.71,-716.35 680.06,-705.33"/>
<polygon fill="#ffa500" stroke="#ffa500" points="681.3,-708.6 689.48,-701.85 678.88,-702.03 681.3,-708.6"/>
<text text-anchor="middle" x="514" y="-748.8" font-family="Times,serif" font-size="14.00" fill="#ffa500">RemoteReservationCanceled</text>
</g>
<!-- RepWaitBackfillReserved -->
<g id="node24" class="node">
<title>RepWaitBackfillReserved</title>
<ellipse fill="none" stroke="black" cx="757" cy="-580" rx="127.28" ry="18"/>
<text text-anchor="middle" x="757" y="-576.3" font-family="Times,serif" font-size="14.00">RepWaitBackfillReserved</text>
</g>
<!-- RepWaitBackfillReserved&#45;&gt;RepRecovering -->
<g id="edge64" class="edge">
<title>RepWaitBackfillReserved&#45;&gt;RepRecovering</title>
<path fill="none" stroke="#000000" d="M669.39,-593.13C563.69,-607.97 399.48,-631.77 395,-637 363.58,-673.66 383.91,-733.89 399.98,-768.43"/>
<polygon fill="#000000" stroke="#000000" points="397.04,-770.4 404.56,-777.86 403.34,-767.34 397.04,-770.4"/>
<text text-anchor="middle" x="474.5" y="-684.3" font-family="Times,serif" font-size="14.00" fill="#000000">RemoteBackfillReserved</text>
</g>
<!-- RepWaitBackfillReserved&#45;&gt;RepNotRecovering -->
<g id="edge25" class="edge">
<title>RepWaitBackfillReserved&#45;&gt;RepNotRecovering</title>
<path fill="none" stroke="#0000ff" d="M851.8,-592.13C935.05,-603.88 1039.4,-624.51 1015,-652 1014.89,-652.13 887.51,-667.31 800.76,-677.63"/>
<polygon fill="#0000ff" stroke="#0000ff" points="800.18,-674.18 790.66,-678.83 801,-681.13 800.18,-674.18"/>
<text text-anchor="middle" x="1146" y="-640.8" font-family="Times,serif" font-size="14.00" fill="#0000ff">RemoteReservationRejectedTooFull</text>
</g>
<!-- RepWaitBackfillReserved&#45;&gt;RepNotRecovering -->
<g id="edge29" class="edge">
<title>RepWaitBackfillReserved&#45;&gt;RepNotRecovering</title>
<path fill="none" stroke="#000000" d="M698.09,-596.08C677.31,-604.53 656.12,-617.47 644,-637 634.42,-652.45 648.49,-664 667.21,-672.08"/>
<polygon fill="#000000" stroke="#000000" points="666.1,-675.41 676.69,-675.78 668.64,-668.89 666.1,-675.41"/>
<text text-anchor="middle" x="747" y="-640.8" font-family="Times,serif" font-size="14.00" fill="#000000">RemoteReservationCanceled</text>
</g>
<!-- RepWaitRecoveryReserved -->
<g id="node25" class="node">
<title>RepWaitRecoveryReserved</title>
<ellipse fill="none" stroke="black" cx="284" cy="-580" rx="135.68" ry="18"/>
<text text-anchor="middle" x="284" y="-576.3" font-family="Times,serif" font-size="14.00">RepWaitRecoveryReserved</text>
</g>
<!-- RepWaitRecoveryReserved&#45;&gt;RepRecovering -->
<g id="edge61" class="edge">
<title>RepWaitRecoveryReserved&#45;&gt;RepRecovering</title>
<path fill="none" stroke="#ffa500" d="M252.44,-597.79C227.61,-612.96 194.81,-637.84 180,-670 167.55,-697.05 161.65,-713.55 180,-737 185.3,-743.78 280.47,-765.81 347.79,-780.68"/>
<polygon fill="#ffa500" stroke="#ffa500" points="347.4,-784.18 357.92,-782.91 348.9,-777.34 347.4,-784.18"/>
<text text-anchor="middle" x="274" y="-684.3" font-family="Times,serif" font-size="14.00" fill="#ffa500">RemoteRecoveryReserved</text>
</g>
<!-- RepWaitRecoveryReserved&#45;&gt;RepNotRecovering -->
<g id="edge28" class="edge">
<title>RepWaitRecoveryReserved&#45;&gt;RepNotRecovering</title>
<path fill="none" stroke="#c71585" d="M311.41,-597.79C339.94,-614.42 386.46,-639.2 430,-652 440.1,-654.97 558.3,-668.63 641.18,-677.98"/>
<polygon fill="#c71585" stroke="#c71585" points="641.03,-681.49 651.36,-679.13 641.81,-674.53 641.03,-681.49"/>
<text text-anchor="middle" x="533" y="-640.8" font-family="Times,serif" font-size="14.00" fill="#c71585">RemoteReservationCanceled</text>
</g>
<!-- RepNotRecovering&#45;&gt;RepWaitBackfillReserved -->
<g id="edge62" class="edge">
<title>RepNotRecovering&#45;&gt;RepWaitBackfillReserved</title>
<path fill="none" stroke="#40e0d0" d="M782.09,-677.74C811.03,-671.92 841.33,-663.35 850,-652 865.89,-631.18 842.19,-613.33 814.74,-600.73"/>
<polygon fill="#40e0d0" stroke="#40e0d0" points="815.91,-597.42 805.34,-596.66 813.13,-603.84 815.91,-597.42"/>
<text text-anchor="middle" x="926" y="-640.8" font-family="Times,serif" font-size="14.00" fill="#40e0d0">RequestBackfillPrio</text>
</g>
<!-- RepNotRecovering&#45;&gt;RepWaitRecoveryReserved -->
<g id="edge63" class="edge">
<title>RepNotRecovering&#45;&gt;RepWaitRecoveryReserved</title>
<path fill="none" stroke="#c71585" d="M654.67,-678.68C628.02,-675.61 597.1,-672.31 569,-670 550.42,-668.47 246.82,-665.53 234,-652 220.15,-637.39 234.36,-618.7 251.12,-604.16"/>
<polygon fill="#c71585" stroke="#c71585" points="253.37,-606.84 258.91,-597.81 248.95,-601.42 253.37,-606.84"/>
<text text-anchor="middle" x="311" y="-640.8" font-family="Times,serif" font-size="14.00" fill="#c71585">RequestRecoveryPrio</text>
</g>
<!-- RepNotRecovering&#45;&gt;RepNotRecovering -->
<g id="edge21" class="edge">
<title>RepNotRecovering&#45;&gt;RepNotRecovering</title>
<path fill="none" stroke="#c71585" d="M841.63,-690.48C863.47,-690.17 878.79,-689.34 878.79,-688 878.79,-686.88 868.09,-686.12 851.82,-685.72"/>
<polygon fill="#c71585" stroke="#c71585" points="851.7,-682.21 841.63,-685.52 851.56,-689.21 851.7,-682.21"/>
<text text-anchor="middle" x="930.29" y="-684.3" font-family="Times,serif" font-size="14.00" fill="#c71585">RecoveryDone</text>
</g>
<!-- RepNotRecovering&#45;&gt;RepNotRecovering -->
<g id="edge23" class="edge">
<title>RepNotRecovering&#45;&gt;RepNotRecovering</title>
<path fill="none" stroke="#1e90ff" d="M823.64,-692.82C898.58,-694.11 981.79,-692.5 981.79,-688 981.79,-683.7 905.89,-682.04 833.78,-683.02"/>
<polygon fill="#1e90ff" stroke="#1e90ff" points="833.58,-679.52 823.64,-683.18 833.69,-686.52 833.58,-679.52"/>
<text text-anchor="middle" x="1109.79" y="-684.3" font-family="Times,serif" font-size="14.00" fill="#1e90ff">RemoteReservationRejectedTooFull</text>
</g>
<!-- RepNotRecovering&#45;&gt;RepNotRecovering -->
<g id="edge27" class="edge">
<title>RepNotRecovering&#45;&gt;RepNotRecovering</title>
<path fill="none" stroke="#40e0d0" d="M817.87,-693.59C970.41,-697.94 1237.79,-696.08 1237.79,-688 1237.79,-680.1 982.28,-678.14 828.32,-682.13"/>
<polygon fill="#40e0d0" stroke="#40e0d0" points="827.78,-678.64 817.87,-682.41 827.97,-685.64 827.78,-678.64"/>
<text text-anchor="middle" x="1340.79" y="-684.3" font-family="Times,serif" font-size="14.00" fill="#40e0d0">RemoteReservationCanceled</text>
</g>
<!-- WaitDeleteReserved -->
<g id="node28" class="node">
<title>WaitDeleteReserved</title>
<polygon fill="lightgrey" stroke="black" points="1652,-598 1504.03,-580 1652,-562 1799.97,-580 1652,-598"/>
<polyline fill="none" stroke="black" points="1515.94,-581.45 1515.94,-578.55 "/>
<polyline fill="none" stroke="black" points="1640.09,-563.45 1663.91,-563.45 "/>
<polyline fill="none" stroke="black" points="1788.06,-578.55 1788.06,-581.45 "/>
<polyline fill="none" stroke="black" points="1663.91,-596.55 1640.09,-596.55 "/>
<text text-anchor="middle" x="1652" y="-576.3" font-family="Times,serif" font-size="14.00">WaitDeleteReserved</text>
</g>
<!-- RepNotRecovering&#45;&gt;WaitDeleteReserved -->
<g id="edge18" class="edge">
<title>RepNotRecovering&#45;&gt;WaitDeleteReserved</title>
<path fill="none" stroke="#0000ff" d="M1488,-629.63C1490,-629.3 1492,-628.97 1494,-628.63"/>
<polygon fill="#0000ff" stroke="#0000ff" points="1486.71,-633.39 1496,-628.3 1485.57,-626.48 1486.71,-633.39"/>
<text text-anchor="middle" x="1347" y="-640.8" font-family="Times,serif" font-size="14.00" fill="#0000ff">DeleteStart</text>
</g>
<!-- Stray&#45;&gt;RepNotRecovering -->
<g id="edge45" class="edge">
<title>Stray&#45;&gt;RepNotRecovering</title>
<path fill="none" stroke="#ff0000" d="M1507.04,-782.9C1504.29,-781.81 1501.45,-780.77 1497.9,-779.71"/>
<polygon fill="#ff0000" stroke="#ff0000" points="1498.55,-776.27 1488,-777.15 1496.8,-783.04 1498.55,-776.27"/>
<text text-anchor="middle" x="1312.5" y="-748.8" font-family="Times,serif" font-size="14.00" fill="#ff0000">MInfoRec</text>
</g>
<!-- Stray&#45;&gt;RepNotRecovering -->
<g id="edge47" class="edge">
<title>Stray&#45;&gt;RepNotRecovering</title>
<path fill="none" stroke="#ffa500" d="M1506,-783.19C1503.47,-782.16 1500.9,-781.16 1497.68,-779.99"/>
<polygon fill="#ffa500" stroke="#ffa500" points="1498.59,-776.61 1488,-776.69 1496.33,-783.23 1498.59,-776.61"/>
<text text-anchor="middle" x="1464.5" y="-748.8" font-family="Times,serif" font-size="14.00" fill="#ffa500">MLogRec</text>
</g>
<!-- Stray&#45;&gt;WaitDeleteReserved -->
<g id="edge19" class="edge">
<title>Stray&#45;&gt;WaitDeleteReserved</title>
<path fill="none" stroke="#ffa500" d="M1541.22,-778.56C1557.87,-748.87 1593.68,-685.01 1620.1,-637.89"/>
<polygon fill="#ffa500" stroke="#ffa500" points="1623.25,-639.43 1625.08,-629 1617.14,-636.01 1623.25,-639.43"/>
<text text-anchor="middle" x="1643" y="-684.3" font-family="Times,serif" font-size="14.00" fill="#ffa500">DeleteStart</text>
</g>
<!-- WaitDeleteReserved&#45;&gt;WaitDeleteReserved -->
<g id="edge54" class="edge">
<title>WaitDeleteReserved&#45;&gt;WaitDeleteReserved</title>
<path fill="none" stroke="#ffa500" d="M1737.4,-587.64C1779.99,-588.7 1817.98,-586.15 1817.98,-580 1817.98,-574.35 1785.97,-571.74 1747.77,-572.17"/>
<polygon fill="#ffa500" stroke="#ffa500" points="1747.34,-568.68 1737.4,-572.36 1747.46,-575.68 1747.34,-568.68"/>
<text text-anchor="middle" x="1845.48" y="-576.3" font-family="Times,serif" font-size="14.00" fill="#ffa500">ActMap</text>
</g>
<!-- Deleting -->
<g id="node29" class="node">
<title>Deleting</title>
<ellipse fill="none" stroke="black" cx="1614" cy="-493" rx="50.09" ry="18"/>
<text text-anchor="middle" x="1614" y="-489.3" font-family="Times,serif" font-size="14.00">Deleting</text>
</g>
<!-- WaitDeleteReserved&#45;&gt;Deleting -->
<g id="edge34" class="edge">
<title>WaitDeleteReserved&#45;&gt;Deleting</title>
<path fill="none" stroke="#40e0d0" d="M1579.03,-570.87C1563.44,-565.6 1548.75,-557.23 1539,-544 1527.51,-528.42 1544.64,-516.22 1565.41,-507.78"/>
<polygon fill="#40e0d0" stroke="#40e0d0" points="1566.64,-511.05 1574.78,-504.27 1564.19,-504.49 1566.64,-511.05"/>
<text text-anchor="middle" x="1596" y="-532.8" font-family="Times,serif" font-size="14.00" fill="#40e0d0">DeleteReserved</text>
</g>
<!-- Deleting&#45;&gt;WaitDeleteReserved -->
<g id="edge35" class="edge">
<title>Deleting&#45;&gt;WaitDeleteReserved</title>
<path fill="none" stroke="#c71585" d="M1637.31,-509.28C1643.59,-514.69 1649.59,-521.36 1653,-529 1656.16,-536.09 1656.99,-544.32 1656.74,-552.03"/>
<polygon fill="#c71585" stroke="#c71585" points="1653.24,-551.8 1655.83,-562.08 1660.22,-552.43 1653.24,-551.8"/>
<text text-anchor="middle" x="1721" y="-532.8" font-family="Times,serif" font-size="14.00" fill="#c71585">DeleteInterrupted</text>
</g>
</g>
</svg>
