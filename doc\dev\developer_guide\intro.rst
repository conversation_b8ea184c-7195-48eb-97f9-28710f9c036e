Introduction
============

This guide has two aims. First, it should lower the barrier to entry for
software developers who wish to get involved in the Ceph project. Second,
it should serve as a reference for Ceph developers.

We assume that readers are already familiar with Ceph (the distributed
object store and file system designed to provide excellent performance,
reliability and scalability). If not, please refer to the `project website`_
and especially the `publications list`_. Another way to learn about what's
happening in Ceph is to check out our `youtube channel`_ , where we post Tech
Talks, Code walk-throughs and Ceph Developer Monthly recordings.

.. _`project website`: https://ceph.com
.. _`publications list`: https://ceph.com/publications/
.. _`youtube channel`: https://www.youtube.com/c/CephStorage

Since this document is to be consumed by developers, who are assumed to
have Internet access, topics covered elsewhere, either within the Ceph
documentation or elsewhere on the web, are treated by linking. If you
notice that a link is broken or if you know of a better link, please
`report it as a bug`_.

.. _`report it as a bug`: http://tracker.ceph.com/projects/ceph/issues/new
