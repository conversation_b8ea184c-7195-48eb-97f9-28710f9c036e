commit 56c4847ba82a92023700e2d4920b59cdaf23428d
Author: <PERSON> <<EMAIL>>
Date:   Wed May 8 17:23:47 2013 -0700

    v0.61.1

commit 3b94f03ec58abe3d7a6d0359ff9b4d75826f3777
Author: <PERSON> <<EMAIL>>
Date:   Thu May 2 14:13:07 2013 -0700

    mon: dump MonitorDBStore transactions to file
    
    Signed-off-by: <PERSON> <<EMAIL>>
    (cherry picked from commit 797089ef082b99910eebfd9454c03d1f027c93bb)

commit 9143d6d0d0ebaaee81622587dc21a3d562b05c9c
Author: <PERSON> <<EMAIL>>
Date:   Mon May 6 14:21:28 2013 -0700

    osd: optionally enable leveldb logging
    
    Signed-off-by: <PERSON>l <<EMAIL>>
    (cherry picked from commit 0b4c5c1a3349670d11cc3c4fb3c4b3c1a80b2502)

commit 8f456e89ec38f37ae4b444f5cabccd9fc0e415b8
Author: <PERSON> <<EMAIL>>
Date:   Mon May 6 14:13:50 2013 -0700

    mon: allow leveldb logging
    
    'mon leveldb log = filename'
    
    Signed-off-by: Sage Weil <<EMAIL>>
    (cherry picked from commit c1d5f815546b731e10bfcb81cbcd48b7d432e9c4)

commit 306ebc6a9164feab084123f91a0fe928125f4b74
Author: Gary Lowell <<EMAIL>>
Date:   Wed May 8 16:33:05 2013 -0700

    debian/control:  squeeze requres cryptsetup package
    
    Squeeze requires the cryptsetup package which has been renamed
    cryptsetup-bin in later versions.  Allow either package to
    satisfy the dependency.
    
    Signed-off-by: Gary Lowell  <<EMAIL>>
    (cherry picked from commit 83bbae415de16f708ca1cb24861ddbb0bd514a7f)

commit e0c0a5c1a711263d234b70b454aca07c23e98243
Author: Yehuda Sadeh <<EMAIL>>
Date:   Wed May 8 12:18:49 2013 -0700

    osd: don't assert if get_omap_iterator() returns NULL
    
    Fixes: #4949
    This can happen if the object does not exist and it's
    a write operation. Just return -ENOENT.
    
    Signed-off-by: Yehuda Sadeh <<EMAIL>>
    (cherry picked from commit 36ec6f9bce63641f4fc2e4ab04d03d3ec1638ea0)

commit 82b9299512537b8d921caa044107472adacbdabe
Author: Sage Weil <<EMAIL>>
Date:   Wed May 8 14:54:33 2013 -0700

    ceph-create-keys: gracefully handle no data from admin socket
    
    Old ceph-mon (prior to 393c9372f82ef37fc6497dd46fc453507a463d42) would
    return an empty string and success if the command was not registered yet.
    Gracefully handle that case by retrying.
    
    If we still fail to parse, exit entirely with EINVAL.
    
    Fixes: #4952
    Signed-off-by: Sage Weil <<EMAIL>>
    Reviewed-by: Dan Mick <<EMAIL>>
    (cherry picked from commit e2528ae42c455c522154c9f68b5032a3362fca8e)

commit ee3da880fbc0488467c30b1d0b635e3c50e23cc3
Author: Sage Weil <<EMAIL>>
Date:   Wed May 8 14:35:54 2013 -0700

    init-ceph: fix osd_data location when checking df utilization
    
    Do not assume default osd data location.
    
    Fixes: #4951
    Backport: cuttlefish, bobtail
    Signed-off-by: Sage Weil <<EMAIL>>
    Reviewed-by: Gary Lowelll <<EMAIL>>
    (cherry picked from commit f2a54cc9c98a9f31aef049c74ea932b2d9000d3c)

commit 4848fac24224879bcdc4fcf202d6ab689d8d990f
Author: Samuel Just <<EMAIL>>
Date:   Tue May 7 16:41:22 2013 -0700

    OSD: handle stray snap collections from upgrade bug
    
    Previously, we failed to clear snap_collections, which causes split to
    spawn a bunch of snap collections.  In load_pgs, we now clear any such
    snap collections and then snap_collections field on the PG itself.
    
    Related: #4927
    Signed-off-by: Samuel Just <<EMAIL>>
    Reviewed-by: Sage Weil <<EMAIL>>
    (cherry picked from commit 8e89db89cb36a217fd97cbc1f24fd643b62400dc)

commit dc6b9e6b2e8c1b41e982430795ff41a65a5813dc
Author: Samuel Just <<EMAIL>>
Date:   Tue May 7 16:35:57 2013 -0700

    PG: clear snap_collections on upgrade
    
    Signed-off-by: Samuel Just <<EMAIL>>
    Reviewed-by: Sage Weil <<EMAIL>>
    (cherry picked from commit 252d71a81ef4536830a74897c84a7015ae6ec9fe)

commit b514941ba5f49b6465082e4906b7f4022631bfb3
Author: Samuel Just <<EMAIL>>
Date:   Tue May 7 16:34:57 2013 -0700

    OSD: snap collections can be ignored on split
    
    Signed-off-by: Samuel Just <<EMAIL>>
    Reviewed-by: Sage Weil <<EMAIL>>
    (cherry picked from commit 438d9aa152e546b2008ec355b481df71aa1c51a5)

commit 30ffca77df006a244044604074779af538721f14
Author: Sage Weil <<EMAIL>>
Date:   Wed May 8 11:05:29 2013 -0700

    ceph: return error code when failing to get result from admin socket
    
    Make sure we return a non-zero result code when we fail to read something
    from the admin socket.
    
    Backport: cuttlefish, bobtail
    Signed-off-by: Sage Weil <<EMAIL>>
    (cherry picked from commit 393c9372f82ef37fc6497dd46fc453507a463d42)
