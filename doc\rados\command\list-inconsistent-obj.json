{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"epoch": {"description": "Scrub epoch", "type": "integer"}, "inconsistents": {"type": "array", "items": {"type": "object", "properties": {"object": {"description": "Identify a Ceph object", "type": "object", "properties": {"name": {"type": "string"}, "nspace": {"type": "string"}, "locator": {"type": "string"}, "version": {"type": "integer", "minimum": 0}, "snap": {"oneOf": [{"type": "string", "enum": ["head", "snapdir"]}, {"type": "integer", "minimum": 0}]}}, "required": ["name", "nspace", "locator", "version", "snap"]}, "selected_object_info": {"type": "object", "description": "Selected object information", "additionalProperties": true}, "union_shard_errors": {"description": "Union of all shard errors", "type": "array", "items": {"enum": ["missing", "stat_error", "read_error", "data_digest_mismatch_info", "omap_digest_mismatch_info", "size_mismatch_info", "ec_hash_error", "ec_size_error", "info_missing", "info_corrupted", "obj_size_info_mismatch", "snapset_missing", "snapset_corrupted", "hinfo_missing", "hinfo_corrupted"]}, "minItems": 0, "uniqueItems": true}, "errors": {"description": "Errors related to the analysis of this object", "type": "array", "items": {"enum": ["object_info_inconsistency", "data_digest_mismatch", "omap_digest_mismatch", "size_mismatch", "attr_value_mismatch", "attr_name_mismatch", "snapset_inconsistency", "hinfo_inconsistency", "size_too_large"]}, "minItems": 0, "uniqueItems": true}, "shards": {"description": "All found or expected shards", "type": "array", "items": {"description": "Information about a particular shard of object", "type": "object", "properties": {"object_info": {"oneOf": [{"type": "string"}, {"type": "object", "description": "Object information", "additionalProperties": true}]}, "snapset": {"oneOf": [{"type": "string"}, {"type": "object", "description": "Snap set information", "additionalProperties": true}]}, "hashinfo": {"oneOf": [{"type": "string"}, {"type": "object", "description": "Erasure code hash information", "additionalProperties": true}]}, "shard": {"type": "integer"}, "osd": {"type": "integer"}, "primary": {"type": "boolean"}, "size": {"type": "integer"}, "omap_digest": {"description": "Hex representation (e.g. 0x1abd1234)", "type": "string"}, "data_digest": {"description": "Hex representation (e.g. 0x1abd1234)", "type": "string"}, "errors": {"description": "Errors with this shard", "type": "array", "items": {"enum": ["missing", "stat_error", "read_error", "data_digest_mismatch_info", "omap_digest_mismatch_info", "size_mismatch_info", "ec_hash_error", "ec_size_error", "info_missing", "info_corrupted", "obj_size_info_mismatch", "snapset_missing", "snapset_corrupted", "hinfo_missing", "hinfo_corrupted"]}, "minItems": 0, "uniqueItems": true}, "attrs": {"description": "If any shard's attr error is set then all attrs are here", "type": "array", "items": {"description": "Information about a particular shard of object", "type": "object", "properties": {"name": {"type": "string"}, "value": {"type": "string"}, "Base64": {"type": "boolean"}}, "required": ["name", "value", "Base64"], "additionalProperties": false}}}, "additionalProperties": false, "required": ["osd", "primary", "errors"]}}}, "required": ["object", "union_shard_errors", "errors", "shards"]}}}, "required": ["epoch", "inconsistents"]}