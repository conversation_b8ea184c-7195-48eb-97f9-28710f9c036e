#!/bin/sh
# vim: set noet ts=8:
# postinst script for ceph
#
# see: dh_installdeb(1)

set -e

# summary of how this script can be called:
#
# 	postinst configure <most-recently-configured-version>
# 	old-postinst abort-upgrade <new-version>
# 	conflictor's-postinst abort-remove in-favour <package> <new-version>
# 	postinst abort-remove
# 	deconfigured's-postinst abort-deconfigure in-favour <failed-install-package> <version> [<removing conflicting-package> <version>]
#
# The current action is to simply remove the mistakenly-added
# /etc/init/ceph.conf file; this could be done in any of these cases,
# although technically it will leave the system in a different state
# than the original install that included that file.  So instead we
# only remove on "configure", since that's the only time we know we're
# successful in installing a newer package than the erroneous version.

# for details, see http://www.debian.org/doc/debian-policy/ or
# the debian-policy package

[ -f "/etc/default/ceph" ] && . /etc/default/ceph
[ -z "$SERVER_USER" ] && SERVER_USER=ceph
[ -z "$SERVER_GROUP" ] && SERVER_GROUP=ceph

case "$1" in
    configure)
	rm -f /etc/init/ceph.conf
	[ -x /sbin/start ] && start ceph-all || :

        # adjust file and directory permissions
	for DIR in /var/lib/ceph/* ; do
	    if ! dpkg-statoverride --list "${DIR}" >/dev/null
	    then
		chown "${SERVER_USER}:${SERVER_GROUP}" "${DIR}"
	    fi
	done

	chown "${SERVER_USER}:${SERVER_GROUP}" -R /var/lib/ceph/crash/*;
    ;;
    abort-upgrade|abort-remove|abort-deconfigure)
	:
    ;;

    *)
        echo "postinst called with unknown argument \`$1'" >&2
        exit 1
    ;;
esac

# dh_installdeb will replace this with shell code automatically
# generated by other debhelper scripts.

#DEBHELPER#

exit 0


