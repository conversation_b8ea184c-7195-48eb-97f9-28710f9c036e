#!/bin/sh
# postrm script for ceph-common
#
# see: dh_installdeb(1)

set -e

# summary of how this script can be called:
#        * <postrm> `remove'
#        * <postrm> `purge'
#        * <old-postrm> `upgrade' <new-version>
#        * <new-postrm> `failed-upgrade' <old-version>
#        * <new-postrm> `abort-install'
#        * <new-postrm> `abort-install' <old-version>
#        * <new-postrm> `abort-upgrade' <old-version>
#        * <disappearer's-postrm> `disappear' <overwriter>
#          <overwriter-version>
# for details, see http://www.debian.org/doc/debian-policy/ or
# the debian-policy package


case "$1" in
    remove)
    ;;

    purge)
        [ -f "/etc/default/ceph" ] && . /etc/default/ceph
        [ -z "$SERVER_USER" ] && SERVER_USER=ceph

        rm -rf /var/log/ceph
        rm -rf /etc/ceph

        if [ -f /etc/shadow ]; then
            usermod -L -e 1 $SERVER_USER
        else
            usermod -L $SERVER_USER
        fi
    ;;

    upgrade|failed-upgrade|abort-install|abort-upgrade|disappear)
    ;;

    *)
        echo "postrm called with unknown argument \`$1'" >&2
        exit 1
    ;;
esac

# dh_installdeb will replace this with shell code automatically
# generated by other debhelper scripts.

#DEBHELPER#

exit 0


