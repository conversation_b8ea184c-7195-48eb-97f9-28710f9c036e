#!/bin/sh
# vim: set noet ts=8:
# postinst script for ceph-mgr
#
# see: dh_installdeb(1)

set -e

# summary of how this script can be called:
#
# 	postinst configure <most-recently-configured-version>
# 	old-postinst abort-upgrade <new-version>
# 	conflictor's-postinst abort-remove in-favour <package> <new-version>
# 	postinst abort-remove
# 	deconfigured's-postinst abort-deconfigure in-favour <failed-install-package> <version> [<removing conflicting-package> <version>]
#

# for details, see http://www.debian.org/doc/debian-policy/ or
# the debian-policy package

[ -f "/etc/default/ceph" ] && . /etc/default/ceph
[ -z "$SERVER_USER" ] && SERVER_USER=ceph
[ -z "$SERVER_GROUP" ] && SERVER_GROUP=ceph

case "$1" in
    configure)
	[ -x /sbin/start ] && start ceph-mgr-all || :

	if ! dpkg-statoverride --list /var/lib/ceph/mgr >/dev/null
	then
            chown $SERVER_USER:$SERVER_GROUP /var/lib/ceph/mgr
	fi
    ;;
    abort-upgrade|abort-remove|abort-deconfigure)
	:
    ;;

    *)
        echo "postinst called with unknown argument \`$1'" >&2
        exit 1
    ;;
esac

# dh_installdeb will replace this with shell code automatically
# generated by other debhelper scripts.

#DEBHELPER#

exit 0


