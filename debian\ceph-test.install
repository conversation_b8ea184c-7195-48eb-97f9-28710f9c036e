usr/bin/ceph-client-debug
usr/bin/ceph-coverage
usr/bin/ceph_bench_log
usr/bin/ceph_erasure_code_benchmark
usr/bin/ceph_multi_stress_watch
usr/bin/ceph_omapbench
usr/bin/ceph_perf_local
usr/bin/ceph_perf_msgr_client
usr/bin/ceph_perf_msgr_server
usr/bin/ceph_perf_objectstore
usr/bin/ceph_psim
usr/bin/ceph_radosacl
usr/bin/ceph_rgw_jsonparser
usr/bin/ceph_rgw_multiparser
usr/bin/ceph_scratchtool
usr/bin/ceph_scratchtoolpp
usr/bin/ceph_test_*
usr/bin/ceph-dedup-tool
usr/lib/ceph/ceph-monstore-update-crush.sh
usr/share/java/libcephfs-test.jar
