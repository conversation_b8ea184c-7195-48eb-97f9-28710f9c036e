# Dashboard team to review dashboard related code
/src/pybind/mgr/dashboard                       @ceph/dashboard
/qa/suites/rados/dashboard                      @ceph/dashboard
/qa/tasks/mgr/test_dashboard.py                 @ceph/dashboard
/qa/tasks/mgr/test_module_selftest.py           @ceph/dashboard
/qa/tasks/mgr/dashboard                         @ceph/dashboard
/monitoring                                     @ceph/dashboard
/doc/mgr/dashboard.rst                          @ceph/dashboard

# For Orchestrator related PRs
/src/cephadm                                    @ceph/orchestrators
/src/pybind/mgr/orchestrator                    @ceph/orchestrators
/src/pybind/mgr/rook                            @ceph/orchestrators
/src/pybind/mgr/cephadm                         @ceph/orchestrators
/src/pybind/mgr/test_orchestrator               @ceph/orchestrators
/src/python-common/ceph/deployment              @ceph/orchestrators
/qa/workunits/cephadm                           @ceph/orchestrators
/qa/tasks/cephadm.py                            @ceph/orchestrators
/qa/tasks/cephadm_cases                         @ceph/orchestrators
/qa/tasks/mgr/test_orchestrator_cli.py          @ceph/orchestrators
/qa/tasks/mgr/test_cephadm_orchestrator.py      @ceph/orchestrators
/qa/suites/orch                                 @ceph/orchestrators
/doc/mgr/orchestrator.rst                       @ceph/orchestrators
/doc/mgr/orchestrator_modules.rst               @ceph/orchestrators
/doc/cephadm                                    @ceph/orchestrators
/doc/dev/cephadm                                @ceph/orchestrators
/doc/man/8/cephadm.rst                          @ceph/orchestrators

/qa/suites/orch/rook                            @ceph/rook
/src/pybind/mgr/rook                            @ceph/rook
/doc/mgr/rook.rst                               @ceph/rook

#ceph-volume
/src/ceph-volume                                @ceph/ceph-volume
/doc/ceph-volume                                @ceph/ceph-volume

# crimson
/src/crimson                                    @ceph/crimson
/src/test/crimson                               @ceph/crimson

# doc-writers
AUTHORS                                         @ceph/doc-writers
CodingStyle                                     @ceph/doc-writers
COPYING*                                        @ceph/doc-writers
/doc/                                           @ceph/doc-writers
README*                                         @ceph/doc-writers
*.rst                                           @ceph/doc-writers

# core
/doc/man/8/ceph-authtool.rst                    @ceph/core @ceph/doc-writers
/doc/man/8/ceph-conf.rst                        @ceph/core @ceph/doc-writers
/doc/man/8/ceph-create-keys.rst                 @ceph/core @ceph/doc-writers
/doc/man/8/ceph-kvstore-tool.rst                @ceph/core @ceph/doc-writers
/doc/man/8/ceph-mon.rst                         @ceph/core @ceph/doc-writers
/doc/man/8/ceph-objectstore-tool.rst            @ceph/core @ceph/doc-writers
/doc/man/8/ceph-osd.rst                         @ceph/core @ceph/doc-writers
/doc/man/8/ceph.rst                             @ceph/core @ceph/doc-writers
/doc/man/8/crushtool.rst                        @ceph/core @ceph/doc-writers
/doc/man/8/monmaptool.rst                       @ceph/core @ceph/doc-writers
/doc/man/8/rados.rst                            @ceph/core @ceph/doc-writers
/doc/rados                                      @ceph/core @ceph/doc-writers
/examples/librados                              @ceph/core
/qa/standalone                                  @ceph/core
/qa/suites/rados                                @ceph/core
/qa/workunits/erasure-code                      @ceph/core
/qa/workunits/mgr                               @ceph/core
/qa/workunits/mon                               @ceph/core
/qa/workunits/objectstore                       @ceph/core
/qa/workunits/rados                             @ceph/core
/src/ceph.in                                    @ceph/core
/src/ceph_osd.cc                                @ceph/core
/src/ceph_mon.cc                                @ceph/core
/src/blk                                        @ceph/core
/src/crush                                      @ceph/core
/src/erasure-code                               @ceph/core
/src/kv                                         @ceph/core
/src/librados                                   @ceph/core
/src/mgr                                        @ceph/core
/src/mon                                        @ceph/core
/src/msg                                        @ceph/core
/src/os                                         @ceph/core
/src/osd                                        @ceph/core
/src/tools/rados                                @ceph/core
/src/test/osd                                   @ceph/core

# rbd
/doc/dev/rbd*                                   @ceph/rbd @ceph/doc-writers
/doc/man/8/ceph-rbdnamer.rst                    @ceph/rbd @ceph/doc-writers
/doc/man/8/rbd*                                 @ceph/rbd @ceph/doc-writers
/doc/rbd                                        @ceph/rbd @ceph/doc-writers
/doc/start/quick-rbd.rst                        @ceph/rbd @ceph/doc-writers
/examples/librbd                                @ceph/rbd
/examples/rbd-replay                            @ceph/rbd
/qa/rbd                                         @ceph/rbd
/qa/run_xfstests*                               @ceph/rbd
/qa/suites/krbd                                 @ceph/rbd
/qa/suites/rbd                                  @ceph/rbd
/qa/tasks/ceph_iscsi_client.py                  @ceph/rbd
/qa/tasks/metadata.yaml                         @ceph/rbd
/qa/tasks/qemu.py                               @ceph/rbd
/qa/tasks/rbd*                                  @ceph/rbd
/qa/tasks/userdata*                             @ceph/rbd
/qa/workunits/cls/test_cls_journal.sh           @ceph/rbd
/qa/workunits/cls/test_cls_lock.sh              @ceph/rbd
/qa/workunits/cls/test_cls_rbd.sh               @ceph/rbd
/qa/workunits/rbd                               @ceph/rbd
/qa/workunits/windows                           @ceph/rbd
/src/ceph-rbdnamer                              @ceph/rbd
/src/cls/journal                                @ceph/rbd
/src/cls/lock                                   @ceph/rbd @ceph/rgw
/src/cls/rbd                                    @ceph/rbd
/src/common/options/rbd*                        @ceph/rbd
/src/etc-rbdmap                                 @ceph/rbd
/src/include/krbd.h                             @ceph/rbd
/src/include/rbd*                               @ceph/rbd
/src/journal                                    @ceph/rbd
/src/krbd.cc                                    @ceph/rbd
/src/librbd                                     @ceph/rbd
/src/ocf                                        @ceph/rbd
/src/pybind/mgr/rbd_support                     @ceph/rbd
/src/pybind/rbd                                 @ceph/rbd
/src/rbd*                                       @ceph/rbd
/src/test/cli/rbd                               @ceph/rbd
/src/test/cli-integration/rbd                   @ceph/rbd
/src/test/cls_journal                           @ceph/rbd
/src/test/cls_lock                              @ceph/rbd @ceph/rgw
/src/test/cls_rbd                               @ceph/rbd
/src/test/journal                               @ceph/rbd
/src/test/librbd                                @ceph/rbd
/src/test/pybind/test_rbd.py                    @ceph/rbd
/src/test/rbd*                                  @ceph/rbd
/src/test/run-rbd*                              @ceph/rbd
/src/test/test_rbd*                             @ceph/rbd
/src/tools/rbd*                                 @ceph/rbd
/systemd/ceph-rbd-mirror*                       @ceph/rbd
/systemd/rbdmap.service.in                      @ceph/rbd
/udev/50-rbd.rules                              @ceph/rbd

# rgw
/doc/dev/radosgw                                @ceph/rgw @ceph/doc-writers
/doc/radosgw                                    @ceph/rgw @ceph/doc-writers
/examples/rgw                                   @ceph/rgw
/qa/rgw                                         @ceph/rgw
/qa/suites/rgw                                  @ceph/rgw
/qa/tasks/barbican.py                           @ceph/rgw
/qa/tasks/kafka.py                              @ceph/rgw
/qa/tasks/keycloak.py                           @ceph/rgw
/qa/tasks/keystone.py                           @ceph/rgw
/qa/tasks/pykmip.py                             @ceph/rgw
/qa/tasks/rabbitmq.py                           @ceph/rgw
/qa/tasks/rgw*                                  @ceph/rgw
/qa/tasks/s3*                                   @ceph/rgw
/qa/tasks/tempest.py                            @ceph/rgw
/qa/tasks/vault.py                              @ceph/rgw
/qa/workunits/rgw                               @ceph/rgw
/src/cls/2pc_queue                              @ceph/rgw
/src/cls/cmpomap                                @ceph/rgw
/src/cls/fifo                                   @ceph/rgw
/src/cls/log                                    @ceph/rgw
/src/cls/otp                                    @ceph/rgw
/src/cls/queue                                  @ceph/rgw
/src/cls/refcount                               @ceph/rgw
/src/cls/rgw                                    @ceph/rgw
/src/cls/rgw_gc                                 @ceph/rgw
/src/cls/user                                   @ceph/rgw
/src/cls/version                                @ceph/rgw
/src/rgw                                        @ceph/rgw
/src/s3select                                   @ceph/rgw
/src/spawn                                      @ceph/rgw
/src/test/cls_2pc_queue                         @ceph/rgw
/src/test/cls_cmpomap                           @ceph/rgw
/src/test/cls_otp                               @ceph/rgw
/src/test/cls_queue                             @ceph/rgw
/src/test/cls_refcount                          @ceph/rgw
/src/test/cls_rgw                               @ceph/rgw
/src/test/cls_rgw_gc                            @ceph/rgw
/src/test/cls_version                           @ceph/rgw
/src/test/rgw                                   @ceph/rgw
/src/test/test_rgw*                             @ceph/rgw
