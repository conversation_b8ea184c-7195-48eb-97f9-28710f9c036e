======================
 MDS Config Reference
======================

.. confval:: mds_cache_mid
.. confval:: mds_dir_max_commit_size
.. confval:: mds_dir_max_entries
.. confval:: mds_decay_halflife
.. confval:: mds_beacon_interval
.. confval:: mds_beacon_grace
.. confval:: mon_mds_blocklist_interval
.. confval:: mds_reconnect_timeout
.. confval:: mds_tick_interval
.. confval:: mds_dirstat_min_interval
.. confval:: mds_scatter_nudge_interval
.. confval:: mds_client_prealloc_inos
.. confval:: mds_early_reply
.. confval:: mds_default_dir_hash
.. confval:: mds_log_skip_corrupt_events
.. confval:: mds_log_max_events
.. confval:: mds_log_max_segments
.. confval:: mds_bal_sample_interval
.. confval:: mds_bal_replicate_threshold
.. confval:: mds_bal_unreplicate_threshold
.. confval:: mds_bal_split_size
.. confval:: mds_bal_split_rd
.. confval:: mds_bal_split_wr
.. confval:: mds_bal_split_bits
.. confval:: mds_bal_merge_size
.. confval:: mds_bal_interval
.. confval:: mds_bal_fragment_interval
.. confval:: mds_bal_fragment_fast_factor
.. confval:: mds_bal_fragment_size_max
.. confval:: mds_bal_idle_threshold
.. confval:: mds_bal_max
.. confval:: mds_bal_max_until
.. confval:: mds_bal_mode
.. confval:: mds_bal_min_rebalance
.. confval:: mds_bal_min_start
.. confval:: mds_bal_need_min
.. confval:: mds_bal_need_max
.. confval:: mds_bal_midchunk
.. confval:: mds_bal_minchunk
.. confval:: mds_replay_interval
.. confval:: mds_shutdown_check
.. confval:: mds_thrash_exports
.. confval:: mds_thrash_fragments
.. confval:: mds_dump_cache_on_map
.. confval:: mds_dump_cache_after_rejoin
.. confval:: mds_verify_scatter
.. confval:: mds_debug_scatterstat
.. confval:: mds_debug_frag
.. confval:: mds_debug_auth_pins
.. confval:: mds_debug_subtrees
.. confval:: mds_kill_mdstable_at
.. confval:: mds_kill_export_at
.. confval:: mds_kill_import_at
.. confval:: mds_kill_link_at
.. confval:: mds_kill_rename_at
.. confval:: mds_inject_skip_replaying_inotable
.. confval:: mds_kill_after_journal_logs_flushed
.. confval:: mds_wipe_sessions
.. confval:: mds_wipe_ino_prealloc
.. confval:: mds_skip_ino
.. confval:: mds_min_caps_per_client
.. confval:: mds_symlink_recovery
.. confval:: mds_extraordinary_events_dump_interval
