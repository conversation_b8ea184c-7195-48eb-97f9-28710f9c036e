#!/bin/sh
# vim: set noet ts=8:
# postinst script for cephadm
#
# see: dh_installdeb(1)

set -e

# summary of how this script can be called:
#
# 	postinst configure <most-recently-configured-version>
# 	old-postinst abort-upgrade <new-version>
# 	conflictor's-postinst abort-remove in-favour <package> <new-version>
# 	postinst abort-remove
# 	deconfigured's-postinst abort-deconfigure in-favour <failed-install-package> <version> [<removing conflicting-package> <version>]
#

# for details, see http://www.debian.org/doc/debian-policy/ or
# the debian-policy package


case "$1" in
    configure)
       # create cephadm user
       # 1. create user if not existing
       if ! getent passwd | grep -q "^cephadm:"; then
         echo -n "Adding system user cephadm.."
         adduser --quiet \
                 --system \
                 --disabled-password \
                 --home /home/<USER>
                 --shell /bin/bash cephadm 2>/dev/null || true
         usermod --comment "cephadm user for mgr/cephadm" cephadm
         echo "..done"
       fi

       # 2. make sure user is unlocked
       if [ -f /etc/shadow ]; then
           usermod -U -e '' cephadm
       else
           usermod -U cephadm
       fi

       # set up (initially empty) .ssh/authorized_keys file
       if ! test -d /home/<USER>/.ssh; then
           mkdir /home/<USER>/.ssh
           chown --reference /home/<USER>/home/<USER>/.ssh
           chmod 0700 /home/<USER>/.ssh
       fi
       if ! test -e /home/<USER>/.ssh/authorized_keys; then
           touch /home/<USER>/.ssh/authorized_keys
           chown --reference /home/<USER>/home/<USER>/.ssh/authorized_keys
           chmod 0600 /home/<USER>/.ssh/authorized_keys
       fi

    ;;
    abort-upgrade|abort-remove|abort-deconfigure)
       :
    ;;

    *)
        echo "postinst called with unknown argument \`$1'" >&2
        exit 1
    ;;
esac

# dh_installdeb will replace this with shell code automatically
# generated by other debhelper scripts.

#DEBHELPER#

exit 0
