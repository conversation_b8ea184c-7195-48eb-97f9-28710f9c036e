# Configuration for stale action workflow - https://github.com/actions/stale

name: 'Close stale issues and PRs'
on:
  schedule:
    - cron: '0 * * * *'

jobs:
  stale:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/stale@v5
        with:
          # PAT for GitHub API authentication
          repo-token: "${{ secrets.GITHUB_TOKEN }}"

          # Comment on the staled PRs
          stale-pr-message: >
                  This pull request has been automatically marked as stale because it
                  has not had any activity for 60 days. It will be closed if no further
                  activity occurs for another 30 days.
                  
                  If you are a maintainer or core committer, please follow-up on this
                  pull request to identify what steps should be taken by the author to move this
                  proposed change forward.
                  
                  If you are the author of this pull request, thank you for your proposed
                  contribution.  If you believe this change is still appropriate, please
                  ensure that any feedback has been addressed and ask for a code review.
          
          # Comment on the staled PRs while closed
          close-pr-message: >
                  This pull request has been automatically closed because there has
                  been no activity for 90 days.  Please feel free to reopen this pull
                  request (or open a new one) if the proposed change is still
                  appropriate.  Thank you for your contribution!
          
          # Idle number of days before making PRs stale (exempts Issues)
          days-before-pr-stale: 60
          
          # Idle number of days before closing stale PRs (exempts Issues)
          days-before-pr-close: 30
          
          # Label to apply on staled PRs
          stale-pr-label: 'stale'
          
          # Labels on PRs exempted from stale
          exempt-pr-labels: 'pinned,security'
          
          # Exempt all PRs with milestones from stale (also exempts Issues)
          exempt-all-pr-milestones: true
          
          # Max number of operations per run
          operations-per-run: 100

          # Change the order used to fetch the issues and pull requests from GitHub
          # So we now start with the oldest PRs and work our way backwards
          ascending: true
