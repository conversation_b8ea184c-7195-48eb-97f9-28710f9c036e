==========================
 Journal Config Reference
==========================
.. warning:: Filestore has been deprecated in the Reef release and is no longer supported.
.. index:: journal; journal configuration

Filestore OSDs use a journal for two reasons: speed and consistency.  Note
that since Luminous, the BlueStore OSD back end has been preferred and default.
This information is provided for pre-existing OSDs and for rare situations where
Filestore is preferred for new deployments.

- **Speed:** The journal enables the Ceph OSD Daemon to commit small writes 
  quickly. <PERSON><PERSON> writes small, random i/o to the journal sequentially, which 
  tends to speed up bursty workloads by allowing the backing file system more 
  time to coalesce writes. The Ceph OSD Daemon's journal, however, can lead 
  to spiky performance with short spurts of high-speed writes followed by 
  periods without any write progress as the file system catches up to the 
  journal.

- **Consistency:** Ceph OSD Daemons require a file system interface that 
  guarantees atomic compound operations. Ceph OSD Daemons write a description 
  of the operation to the journal and apply the operation to the file system. 
  This enables atomic updates to an object (for example, placement group 
  metadata). Every few seconds--between ``filestore max sync interval`` and
  ``filestore min sync interval``--the Ceph OSD Daemon stops writes and 
  synchronizes the journal with the file system, allowing Ceph OSD Daemons to 
  trim operations from the journal and reuse the space. On failure, Ceph 
  OSD Daemons replay the journal starting after the last synchronization 
  operation.

Ceph OSD Daemons recognize the following journal settings: 

.. confval:: journal_dio
.. confval:: journal_aio
.. confval:: journal_block_align
.. confval:: journal_max_write_bytes
.. confval:: journal_max_write_entries
.. confval:: journal_align_min_size
.. confval:: journal_zero_on_create
