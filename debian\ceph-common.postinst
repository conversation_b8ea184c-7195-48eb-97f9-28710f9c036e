#!/bin/sh
# -*- mode:sh; tab-width:8; indent-tabs-mode:nil -*-
# vim: set noet ts=8:
# postinst script for ceph-mds
#
# see: dh_installdeb(1)

set -e

# summary of how this script can be called:
#
# 	postinst configure <most-recently-configured-version>
# 	old-postinst abort-upgrade <new-version>
# 	conflictor's-postinst abort-remove in-favour <package> <new-version>
# 	postinst abort-remove
# 	deconfigured's-postinst abort-deconfigure in-favour <failed-install-package> <version> [<removing conflicting-package> <version>]
#

# for details, see http://www.debian.org/doc/debian-policy/ or
# the debian-policy package


# Let the admin override these distro-specified defaults.  This is NOT
# recommended!
[ -f "/etc/default/ceph" ] && . /etc/default/ceph

[ -z "$SERVER_HOME" ] && SERVER_HOME=/var/lib/ceph
[ -z "$SERVER_USER" ] && SERVER_USER=ceph
[ -z "$SERVER_NAME" ] && SERVER_NAME="Ceph storage service"
[ -z "$SERVER_GROUP" ] && SERVER_GROUP=ceph
[ -z "$SERVER_UID" ] && SERVER_UID=64045  # alloc by Debian base-passwd maintainer
[ -z "$SERVER_GID" ] && SERVER_GID=$SERVER_UID


# Groups that the user will be added to, if undefined, then none.
[ -z "$SERVER_ADDGROUP" ] && SERVER_ADDGROUP=

case "$1" in
    configure)
       # create user to avoid running server as root
       # 1. create group if not existing
       if ! getent group | grep -q "^$SERVER_GROUP:" ; then
          echo -n "Adding group $SERVER_GROUP.."
          addgroup --quiet --system --gid $SERVER_GID \
              $SERVER_GROUP 2>/dev/null ||true
          echo "..done"
       fi
       # 2. create user if not existing
       if ! getent passwd | grep -q "^$SERVER_USER:"; then
         echo -n "Adding system user $SERVER_USER.."
         adduser --quiet \
                 --system \
                 --no-create-home \
                 --disabled-password \
                 --home $SERVER_HOME \
                 --uid $SERVER_UID \
                 --gid $SERVER_GID \
                 $SERVER_USER 2>/dev/null || true
         echo "..done"
       fi
       # 3. adjust passwd entry
       # NOTE: we should use "adduser --comment" if we don't need to
       # support adduser <3.136. "adduser --gecos" is deprecated,
       # and will be removed, so we don't use it. the first distro
       # using --comment is debian/trixie or ubuntu/mantic.
       echo -n "Setting system user $SERVER_USER properties.."
       usermod --comment "$SERVER_NAME" \
               --gid $SERVER_GROUP      \
               $SERVER_USER
       # Unlock $SERVER_USER in case it is locked from an uninstall
       if [ -f /etc/shadow ]; then
           usermod -U -e '' $SERVER_USER
       else
           usermod -U $SERVER_USER
       fi
       echo "..done"

       # 4. adjust file and directory permissions
       if ! dpkg-statoverride --list $SERVER_HOME >/dev/null; then
           chown $SERVER_USER:$SERVER_GROUP $SERVER_HOME
           chmod u=rwx,g=rx,o= $SERVER_HOME
       fi
       if ! dpkg-statoverride --list /var/log/ceph >/dev/null; then
           # take care not to touch cephadm log subdirs
           chown $SERVER_USER:$SERVER_GROUP /var/log/ceph
           chown $SERVER_USER:$SERVER_GROUP /var/log/ceph/*.log* || true
           # members of group ceph can log here, but cannot remove
           # others' files.  non-members cannot read any logs.
           chmod u=rwx,g=rwxs,o=t /var/log/ceph
       fi

       # 5. fix /var/run/ceph
       if [ -d /var/run/ceph ]; then
           echo -n "Fixing /var/run/ceph ownership.."
           chown $SERVER_USER:$SERVER_GROUP /var/run/ceph
           echo "..done"
       fi

       # create /run/ceph.  fail softly if systemd isn't present or
       # something.
       [ -x /bin/systemd-tmpfiles ] && systemd-tmpfiles --create || true
    ;;
    abort-upgrade|abort-remove|abort-deconfigure)
       :
    ;;

    *)
        echo "postinst called with unknown argument \`$1'" >&2
        exit 1
    ;;
esac

# dh_installdeb will replace this with shell code automatically
# generated by other debhelper scripts.

#DEBHELPER#

exit 0
