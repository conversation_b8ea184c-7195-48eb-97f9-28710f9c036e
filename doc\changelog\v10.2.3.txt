commit ecc23778eb545d8dd55e2e4735b53cc93f92e65b
Author: Jenkins Build Slave User <<EMAIL>>
Date:   Tue Sep 20 22:04:23 2016 +0000

    10.2.3

commit 9bfc0cf178dc21b0fe33e0ce3b90a18858abaf1b
Merge: aeee80e 1cbc839
Author: <PERSON><PERSON> <<EMAIL>>
Date:   Mon Aug 29 13:43:15 2016 +0200

    Merge pull request #10804 from dachary/wip-17089-jewel
    
    jewel: OSD failed to subscribe skipped osdmaps after ceph osd pause
    
    Reviewed-by: <PERSON><PERSON> <PERSON><PERSON> <<EMAIL>>

commit aeee80e64af2f685ac9785c3444f40b60d0877f2
Merge: 3168a84 dc154cd
Author: <PERSON><PERSON> <<EMAIL>>
Date:   Mon Aug 29 13:43:00 2016 +0200

    Merge pull request #10760 from dachary/wip-16865-jewel
    
    jewel: saw valgrind issues in ReplicatedPG::new_repop
    
    Reviewed-by: <PERSON><PERSON> <PERSON><PERSON> <<EMAIL>>

commit 3168a8482211a2aaf9b4d715c0f7a920e896d9bb
Merge: 12b374f ce986aa
Author: Loic Dachary <<EMAIL>>
Date:   Fri Aug 26 22:12:39 2016 +0200

    Merge pull request #10662 from dachary/wip-16901-jewel
    
    jewel: segfault in RGWOp_MDLog_Notify
    
    Reviewed-by: Yehuda Sadeh <<EMAIL>>

commit 12b374fccfb9792998ab88a4d7777f8b8134cb5c
Merge: 91ee75f f298643
Author: Orit Wasserman <<EMAIL>>
Date:   Fri Aug 26 10:31:06 2016 +0200

    Merge pull request #10763 from dachary/wip-17034-jewel
    
    jewel: rgw: object expirer's hints might be trimmed without processing in some circumstances
    Reviewed-by: Orit Wasserman <<EMAIL>>

commit 91ee75f39c05fe0c25aab2664f1fa71f858e7c6f
Merge: d8bdc22 a865f26
Author: Orit Wasserman <<EMAIL>>
Date:   Fri Aug 26 10:28:10 2016 +0200

    Merge pull request #10658 from dachary/wip-16862-jewel
    
    jewel: default zone and zonegroup cannot be added to a realm
    Reviewed-by: Orit Wasserman <<EMAIL>>

commit d8bdc2245ddec91c2f55d367045e4407112d2af1
Merge: 6d270f6 fa2e42d
Author: Orit Wasserman <<EMAIL>>
Date:   Fri Aug 26 10:26:53 2016 +0200

    Merge pull request #10659 from dachary/wip-16863-jewel
    
    jewel: use zone endpoints instead of zonegroup endpoints
    Reviewed-by: Orit Wasserman <<EMAIL>>

commit 6d270f6084a035040638638ff65c14f5d76eeef1
Merge: fe23c2e 2485efc
Author: Orit Wasserman <<EMAIL>>
Date:   Fri Aug 26 10:24:20 2016 +0200

    Merge pull request #10660 from dachary/wip-16864-jewel
    
    jewel: multisite segfault on ~RGWRealmWatcher if realm was deleted
    Reviewed-by: Orit Wasserman <<EMAIL>>

commit fe23c2edc70d1ad15e640195570e624b31415aef
Merge: a57c21a 577336e
Author: Loic Dachary <<EMAIL>>
Date:   Fri Aug 26 00:46:17 2016 +0200

    Merge pull request #10847 from gregsfortytwo/jewel-fix-mds
    
    mds: fix double-unlock on shutdown
    
    Reviewed-by: Patrick Donnelly <<EMAIL>>

commit a57c21afa686967a3afc4ddabb97c665e5894b21
Merge: 49022d4 ce5e250
Author: Loic Dachary <<EMAIL>>
Date:   Thu Aug 25 23:04:37 2016 +0200

    Merge pull request #10663 from dachary/wip-16934-jewel
    
    jewel: Add zone rename to radosgw_admin
    
    Reviewed-by: Casey Bodley <<EMAIL>>

commit 49022d4b2e2c5506dd83f3d894ac0a4ba83f8c7b
Merge: f7afbbf 22c9df1
Author: Loic Dachary <<EMAIL>>
Date:   Thu Aug 25 23:04:22 2016 +0200

    Merge pull request #10664 from dachary/wip-16958-jewel
    
    jewel: Bug when using port 443s in rgw.
    
    Reviewed-by: Casey Bodley <<EMAIL>>

commit f7afbbf094e5eabc1a651379f522d0b1544b01a3
Merge: 7b672cb 73cc608
Author: Loic Dachary <<EMAIL>>
Date:   Thu Aug 25 23:04:07 2016 +0200

    Merge pull request #10764 from dachary/wip-17032-jewel
    
    jewel: multisite: RGWPeriodPuller tries to pull from itself
    
    Reviewed-by: Casey Bodley <<EMAIL>>

commit 7b672cbe343955228b2a701eba91a30f5cac7fe5
Merge: 28bc826 55460e2
Author: Loic Dachary <<EMAIL>>
Date:   Thu Aug 25 23:03:40 2016 +0200

    Merge pull request #10765 from dachary/wip-17006-jewel
    
    jewel: Increase log level for messages occuring while running rgw admin command
    
    Reviewed-by: Casey Bodley <<EMAIL>>

commit 28bc826e9140520e86e2eb34470103979d10ae49
Merge: 8b37c68 21da103
Author: Loic Dachary <<EMAIL>>
Date:   Thu Aug 25 22:25:21 2016 +0200

    Merge pull request #10845 from cbodley/wip-16702
    
    jewel: rgw: add tenant support to multisite sync
    
    Reviewed-by: Loic Dachary <<EMAIL>>

commit 8b37c68f761d59025678c184a4f9b1755c90a628
Merge: 94eb163 2266287
Author: Loic Dachary <<EMAIL>>
Date:   Thu Aug 25 09:17:52 2016 +0200

    Merge pull request #9405 from SUSE/wip-16083-jewel
    
    jewel: mds: wrongly treat symlink inode as normal file/dir when symlink inode is stale on kcephfs
    
    Reviewed-by: Greg Farnum <<EMAIL>>

commit 94eb163c19e02ffae06659ced714140bd55584f0
Merge: 9de2e93 f5db5a4
Author: Loic Dachary <<EMAIL>>
Date:   Thu Aug 25 09:02:17 2016 +0200

    Merge pull request #10654 from dachary/wip-9577-jewel
    
    jewel: mon: "mon metadata" fails when only one monitor exists
    
    Reviewed-by: John Spray <<EMAIL>>

commit 577336e20f68aa289311103e54d4def793b0cc01
Author: Greg Farnum <<EMAIL>>
Date:   Wed Aug 24 13:34:43 2016 -0700

    mds: fix double-unlock on shutdown
    
    We did a bad backport or something and accidentally ended up with two Unlock()
    calls on mds_lock. Don't.
    
    Signed-off-by: Greg Farnum <<EMAIL>>

commit 21da1039fc57bcf4054c0e7a13bb2732781770f6
Author: Yehuda Sadeh <<EMAIL>>
Date:   Tue Jul 12 20:36:35 2016 -0700

    rgw: collect skips a specific coroutine stack
    
    Fixes: http://tracker.ceph.com/issues/16665
    
    Instead of drain_all_but() that specifies number of stacks to leave behind,
    added drain_all_but_stack() that has a specific stack specified. This is needed
    so that we don't call wakeup() through lease_cr->go_down() on a cr stack that
    was already collected.
    
    Signed-off-by: Yehuda Sadeh <<EMAIL>>
    (cherry picked from commit 5a2e8f0526db92a290c711f82627fc5042c290ea)

commit 9de2e93d1b55c5f5f54b3dbf334d1513abb3f174
Merge: 1d5dedf d1e0512
Author: Loic Dachary <<EMAIL>>
Date:   Wed Aug 24 22:08:58 2016 +0200

    Merge pull request #10791 from dachary/wip-17004-jewel
    
    jewel: rbd-mirror: FAILED assert(m_state == STATE_STOPPING)
    
    Reviewed-by: Jason Dillaman <<EMAIL>>

commit 1d5dedf0307f0fddf55f6487486a281c2625ef92
Merge: e30c76e ecea6dc
Author: Loic Dachary <<EMAIL>>
Date:   Wed Aug 24 22:08:30 2016 +0200

    Merge pull request #10827 from dillaman/wip-16950-jewel
    
    jewel: librbd: delay acquiring lock if image watch has failed
    
    Reviewed-by: Jason Dillaman <<EMAIL>>

commit 98779c32de98efc367e888b99fd154ad4e5274d9
Author: Josh Durgin <<EMAIL>>
Date:   Mon Jul 11 17:38:43 2016 -0700

    rgw: fix compilation
    
    Signed-off-by: Josh Durgin <<EMAIL>>
    (cherry picked from commit 874de58024e12d893ae050cd421ef67c9521c753)

commit f5d9537d3e69197c7eadb21a6d0d01373530e2de
Author: Casey Bodley <<EMAIL>>
Date:   Thu Jul 7 14:42:00 2016 -0400

    rgw: remove datalog keys from error repo on ENOENT
    
    Signed-off-by: Casey Bodley <<EMAIL>>
    (cherry picked from commit 16976eedb70292e821193e39bb577a68df1bc95a)

commit 32505b2c8d703d09224b6a2f7007b20a79e0fb3d
Author: Casey Bodley <<EMAIL>>
Date:   Fri Jul 1 12:36:04 2016 -0400

    test/rgw: add optional --tenant flag to test_multi.py
    
    Signed-off-by: Casey Bodley <<EMAIL>>
    (cherry picked from commit 6f65d192cd3917cdcf7d9deb5396750d2c641842)

commit f68337aaa535ea182258eb4934af739fb703f556
Author: Casey Bodley <<EMAIL>>
Date:   Thu Jun 30 15:50:00 2016 -0400

    rgw: add tenant id to GetBucketInstanceInfo
    
    use the rgw_bucket overload of get_bucket_instance_info() so it can
    supply the tenant id
    
    Signed-off-by: Casey Bodley <<EMAIL>>
    (cherry picked from commit 3cf715c6828d7d9732bf6a6fd9b46bbd8c08de5f)

commit a0ffffa5aad423f363bf10a6b8dbaf19a47262a2
Author: Casey Bodley <<EMAIL>>
Date:   Wed Jun 29 11:58:55 2016 -0400

    rgw: carry tenant id with data sync
    
    use rgw_bucket_shard to track buckets, which includes tenant id
    
    Fixes: http://tracker.ceph.com/issues/16469
    
    Signed-off-by: Casey Bodley <<EMAIL>>
    (cherry picked from commit d945e2099525e39588b572e357b115df98c8cdca)

commit bff626f7b32b3fe918510c147f88c49ead5007fa
Author: Casey Bodley <<EMAIL>>
Date:   Thu Jun 30 17:43:40 2016 -0400

    rgw: add tenant to url for RGWRESTStreamWriteRequest
    
    Signed-off-by: Casey Bodley <<EMAIL>>
    (cherry picked from commit 2a1e1a776c58f62fbcb57877d56b92eb1e8933e6)

commit dc50687f9ee7291192b2291c3c5770c46c852677
Author: Casey Bodley <<EMAIL>>
Date:   Thu Jun 30 17:42:54 2016 -0400

    rgw: add tenant to rgw_bucket json format
    
    Signed-off-by: Casey Bodley <<EMAIL>>
    (cherry picked from commit fc0df4802b48fa095587b93557c82da5446d9ca3)

commit 06223adc51d2ccc1494c5e6891ad367edde69a91
Author: Casey Bodley <<EMAIL>>
Date:   Thu Jun 30 00:13:47 2016 -0400

    rgw: add rgw_bucket_parse_bucket_key()
    
    Signed-off-by: Casey Bodley <<EMAIL>>
    (cherry picked from commit d5ac140040ea2f17e33d2846870cb033c7ae6ffa)

commit b4f687ee7fce682a41c707af7e2feb6b3a1cbe15
Author: Casey Bodley <<EMAIL>>
Date:   Tue Jun 28 11:24:46 2016 -0400

    rgw: add tenant name to datalog entries
    
    Signed-off-by: Casey Bodley <<EMAIL>>
    (cherry picked from commit c8aa668fc3e34a7651ce6e2dda62902ced808541)

commit dda0ee035fe9bb0c2fc4366df8355184db5034e3
Author: Casey Bodley <<EMAIL>>
Date:   Wed Jul 6 11:50:47 2016 -0400

    rgw: convert bucket instance listings back to metadata key format
    
    Signed-off-by: Casey Bodley <<EMAIL>>
    (cherry picked from commit 3eae201c8a1513bae23233700c754ec076360664)

commit 6225a6d45b8e76f9afaa741fa61d4b9742ef60fb
Author: Casey Bodley <<EMAIL>>
Date:   Tue Jul 5 09:00:19 2016 -0400

    rgw: use tenant/ for bucket instance metadata keys
    
    to work around the ambiguity of parsing tenant: and :shard in the same
    bucket instance metadata key, use tenant/ instead
    
    to preserve backward compatibility with existing objects, new helper
    function rgw_bucket_instance_key_to_oid() converts this / back to a :
    before being used as an object name
    
    Signed-off-by: Casey Bodley <<EMAIL>>
    (cherry picked from commit a0befa1e7be3aeb37209bac2e2a7de0edf5d5a95)

commit cac6612a8a4f344e910d0c880ee8d020fac1ec79
Author: Casey Bodley <<EMAIL>>
Date:   Thu Jun 30 18:09:03 2016 -0400

    rgw: add get_key() methods to format rgw_buckets
    
    Signed-off-by: Casey Bodley <<EMAIL>>
    (cherry picked from commit 51ff8ef7c6bb7c9f55f3ca8ad059b029a4088cdf)

commit ac557e04e2d104fe9fdeae1d1ca31e212cfc89f6
Author: Yehuda Sadeh <<EMAIL>>
Date:   Mon Jun 6 16:16:33 2016 -0700

    rgw: data sync debug logging
    
    a high level debug logging of the data sync process.
    
    Signed-off-by: Yehuda Sadeh <<EMAIL>>
    (cherry picked from commit 97ef16c0cc0ad8345b5a897108717d83ab0aa9ab)

commit 6bb8c15a38e1ebf4e2b03f43bb527d460cd82dc3
Author: Pritha Srivastava <<EMAIL>>
Date:   Thu Jun 16 14:45:57 2016 +0530

    rgw: modifying multi-site log messages.
    
    Increasing the log level and removing 'ERROR' from messages that appear during normal multi-site operation.
    Fixes: http://tracker.ceph.com/issues/16121
    
    Signed-off-by: Pritha Srivastava <<EMAIL>>
    (cherry picked from commit e1737ddaa035f23757d021dd34b0c87d24ce372c)

commit e30c76ee33ddcb1a58835637c5e506c6c4b61b12
Merge: df71e57 308f514
Author: Loic Dachary <<EMAIL>>
Date:   Wed Aug 24 18:57:23 2016 +0200

    Merge pull request #10653 from dachary/wip-10495-jewel
    
    jewel : 60-ceph-partuuid-workaround-rules still needed by debian jessie (udev 215-17)
    
    Reviewed-by: Loic Dachary <<EMAIL>>

commit df71e5740255ffc0a809b2c437ce37826aed11d3
Merge: 286b30f f80d10c
Author: Loic Dachary <<EMAIL>>
Date:   Wed Aug 24 18:51:54 2016 +0200

    Merge pull request #10357 from SUSE/wip-16748-jewel
    
    jewel: mount.ceph: move from ceph-base to ceph-common and add symlink in /sbin for SUSE
    
    Reviewed-by: Loic Dachary <<EMAIL>>

commit 286b30fc54c1ea6a41c38a80c5227f9deeb13741
Merge: eb28401 48ee3aa
Author: Loic Dachary <<EMAIL>>
Date:   Wed Aug 24 17:48:00 2016 +0200

    Merge pull request #10790 from dachary/wip-17005-jewel
    
    jewel: ImageReplayer::is_replaying does not include flush state
    
    Reviewed-by: Jason Dillaman <<EMAIL>>

commit eb28401a59e2ca1a30d0f29aab7a2ea5eb704aea
Merge: 4f3605b d336735
Author: Loic Dachary <<EMAIL>>
Date:   Wed Aug 24 17:47:45 2016 +0200

    Merge pull request #10792 from dachary/wip-16978-jewel
    
    jewel: rbd-mirror: FAILED assert(m_on_update_status_finish == nullptr)
    
    Reviewed-by: Jason Dillaman <<EMAIL>>

commit 4f3605b0c3cba436b362318487bec4358ea8c4d0
Merge: 0c60d47 8316b6a
Author: Loic Dachary <<EMAIL>>
Date:   Wed Aug 24 17:47:36 2016 +0200

    Merge pull request #10646 from dachary/wip-16576-jewel
    
    jewel: rbd-mirror: FAILED assert(m_local_image_ctx->object_map != nullptr)
    
    Reviewed-by: Jason Dillaman <<EMAIL>>

commit 0c60d47579591f07bc4e8d24a52c0355fbd17917
Merge: 6dd0327 5968664
Author: Loic Dachary <<EMAIL>>
Date:   Wed Aug 24 17:47:18 2016 +0200

    Merge pull request #10647 from dachary/wip-16593-jewel
    
    jewel: FAILED assert(object_no < m_object_map.size())
    
    Reviewed-by: Jason Dillaman <<EMAIL>>

commit 6dd0327b40b35e19cb14ce30dec0d75d5a7da086
Merge: cb4cb4a e098fb1
Author: Loic Dachary <<EMAIL>>
Date:   Wed Aug 24 17:47:08 2016 +0200

    Merge pull request #10786 from dachary/wip-17061-jewel
    
    jewel: bashism in src/rbdmap
    
    Reviewed-by: Jason Dillaman <<EMAIL>>

commit cb4cb4a02b160d9dd65e624cd762c12284eed241
Merge: 089bb43 46246e3
Author: Loic Dachary <<EMAIL>>
Date:   Wed Aug 24 14:12:34 2016 +0200

    Merge pull request #10421 from SUSE/wip-16659-jewel
    
    jewel: ReplicatedBackend doesn't increment stats on pull, only push
    
    Reviewed-by: Kefu Chai <<EMAIL>>

commit 089bb43b146408e5e670c3788990b712d8d26543
Merge: 59b6c20 28697fc
Author: Loic Dachary <<EMAIL>>
Date:   Tue Aug 23 15:42:11 2016 +0200

    Merge pull request #10501 from Abhishekvrshny/wip-16621-jewel
    
    jewel: mds:  tell command blocks forever with async messenger (TestVolumeClient.test_evict_client failure)
    
    Reviewed-by: Douglas Fuller <<EMAIL>>
    Reviewed-by: Greg Farnum <<EMAIL>>

commit 59b6c20ea5bfb2d7062f7119ffbecd312d342bc4
Merge: e1ddce7 f4fb598
Author: Loic Dachary <<EMAIL>>
Date:   Tue Aug 23 15:23:34 2016 +0200

    Merge pull request #10816 from SUSE/wip-17092-jewel
    
    jewel: build/ops: need rocksdb commit 7ca731b12ce for ppc64le build
    
    Reviewed-by: Loic Dachary <<EMAIL>>

commit ecea6dcf1c36bc5d478cf030f7ba1e01ca35a2d0
Author: Jason Dillaman <<EMAIL>>
Date:   Thu Aug 4 13:24:54 2016 -0400

    librbd: delay acquiring exclusive lock if watch has failed
    
    Fixes: http://tracker.ceph.com/issues/16923
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit dfe9f3eac9cca3b83962e0e1b7eac38e6e76d7a5)

commit 49a39ebf6f7c6b7b0b19e4486fc10e57637e143c
Author: Jason Dillaman <<EMAIL>>
Date:   Thu Aug 4 13:47:33 2016 -0400

    librbd: convert ImageWatcher class to template
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 814c305ce8c35b5ce01d7e29a912d5ef3978754b)
    
     Conflicts:
            src/librbd/ImageWatcher.cc: no shrink guard
            src/librbd/Operations.cc: no shrink guard

commit e1ddce7654698f3e41d89406981b1eb26f8f72a3
Merge: 2c387d5 a7987f0
Author: Loic Dachary <<EMAIL>>
Date:   Tue Aug 23 08:48:19 2016 +0200

    Merge pull request #10103 from xiaoxichen/wip-16037-jewel
    
    jewel: MDSMonitor::check_subs() is very buggy
    
    Reviewed-by: Greg Farnum <<EMAIL>>

commit 2c387d5cd2785d518982f1c0d209235c240cb7ae
Merge: 128251c 64d99b1
Author: Loic Dachary <<EMAIL>>
Date:   Tue Aug 23 08:48:01 2016 +0200

    Merge pull request #10105 from xiaoxichen/wip-16515-jewel
    
    jewel: Session::check_access() is buggy
    
    Reviewed-by: Greg Farnum <<EMAIL>>

commit 128251ceddf6129a1836a6d991a727adc93869df
Merge: d27c6ac bce5646
Author: Loic Dachary <<EMAIL>>
Date:   Tue Aug 23 08:47:37 2016 +0200

    Merge pull request #10106 from xiaoxichen/wip-16215-jewel
    
    jewel: client: crash in unmount when fuse_use_invalidate_cb is enabled
    
    Reviewed-by: Greg Farnum <<EMAIL>>

commit d27c6ac57b5cd2f5566c5ecf476b8fe37041ab27
Merge: 63422d0 fd7ff96
Author: Loic Dachary <<EMAIL>>
Date:   Tue Aug 23 08:47:23 2016 +0200

    Merge pull request #10108 from xiaoxichen/wip-16320-jewel
    
    jewel: fuse mounted file systems fails SAMBA CTDB ping_pong rw test with v9.0.2
    
    Reviewed-by: Greg Farnum <<EMAIL>>

commit 63422d08ea6d401cefd6b438a7de48c1528da114
Merge: 9fd5291 aa39361
Author: Loic Dachary <<EMAIL>>
Date:   Tue Aug 23 08:47:10 2016 +0200

    Merge pull request #10199 from SUSE/wip-16625-jewel
    
    jewel: Failing file operations on kernel based cephfs mount point leaves unaccessible file behind on hammer 0.94.7
    
    Reviewed-by: Greg Farnum <<EMAIL>>

commit 9fd5291345e4f7d08faf8110ba78df244d030db6
Merge: 2823714 2beb56a
Author: Loic Dachary <<EMAIL>>
Date:   Tue Aug 23 08:46:53 2016 +0200

    Merge pull request #10499 from Abhishekvrshny/wip-16299-jewel
    
    jewel: mds: fix SnapRealm::have_past_parents_open()
    
    Reviewed-by: Greg Farnum <<EMAIL>>

commit 282371465476e37408f65fc33145f8a76cf35ac7
Merge: e2f7de6 1d3a816
Author: Loic Dachary <<EMAIL>>
Date:   Tue Aug 23 08:46:38 2016 +0200

    Merge pull request #10500 from Abhishekvrshny/wip-16620-jewel
    
    jewel: Fix shutting down mds timed-out due to deadlock
    
    Reviewed-by: Greg Farnum <<EMAIL>>

commit e2f7de68393dcf008e366cfc40b199b5acfcd428
Merge: 6f287bf 7c2eab1
Author: Loic Dachary <<EMAIL>>
Date:   Tue Aug 23 08:46:13 2016 +0200

    Merge pull request #10502 from Abhishekvrshny/wip-16797-jewel
    
    jewel: MDS Deadlock on shutdown active rank while busy with metadata IO
    
    Reviewed-by: Greg Farnum <<EMAIL>>

commit 6f287bf2b99b8f6b1ae21f017d905dfa4a569649
Merge: d68d41e d244b7a
Author: Loic Dachary <<EMAIL>>
Date:   Tue Aug 23 08:45:41 2016 +0200

    Merge pull request #10104 from xiaoxichen/wip-16560-jewel
    
    jewel: mds: enforce a dirfrag limit on entries
    
    Reviewed-by: Patrick Donnelly <<EMAIL>>

commit d68d41ebdae92666b4dcf6cc8455cf17ab1d903a
Merge: bc6e329 a5f5513
Author: Loic Dachary <<EMAIL>>
Date:   Tue Aug 23 01:19:33 2016 +0200

    Merge pull request #10074 from ceph/jewel-16002
    
    jewel: ObjectCacher split BufferHead read fix
    
    Reviewed-by: Greg Farnum <<EMAIL>>

commit f4fb59896ccb0d8ac01434cd4cf6ad67776fb0a0
Author: Nathan Cutler <<EMAIL>>
Date:   Mon Aug 22 20:56:38 2016 +0200

    build/ops: bump rocksdb submodule
    
    Fixes a FTBFS on ppc64le
    
    Fixes: http://tracker.ceph.com/issues/17092
    Signed-off-by: Nathan Cutler <<EMAIL>>

commit bc6e3291b4960779fb84b2976079490d5f8f4935
Merge: 46106f0 e825dd2
Author: Loic Dachary <<EMAIL>>
Date:   Mon Aug 22 10:37:14 2016 +0200

    Merge pull request #10537 from theanalyst/wip-16778-jewel
    
    jewel: rgw multisite: preserve zone's extra pool
    
    Reviewed-by: Orit Wasserman <<EMAIL>>

commit 46106f0c0cfeb8746a521559bb466a5021a2c221
Merge: 1bf76cd 96ad2d1
Author: Orit Wasserman <<EMAIL>>
Date:   Mon Aug 22 10:31:39 2016 +0200

    Merge pull request #10655 from dachary/wip-16163-jewel
    
    jewel: rgw: can set negative max_buckets on RGWUserInfo
    Reviewed-by: Orit Wasserman <<EMAIL>>

commit 1bf76cd58ec3635291e5c0080df47f0845088b77
Merge: 3cef399 8b9954d
Author: Orit Wasserman <<EMAIL>>
Date:   Mon Aug 22 10:29:40 2016 +0200

    Merge pull request #10580 from dreamhost/wip-16928-jewel
    
    jewel: rgw: Fix civetweb IPv6
    Reviewed-by: Orit Wasserman <<EMAIL>>

commit 3cef399857b5cb234e0f4cd54933d58744736f62
Merge: e1bc847 9e5a3ae
Author: Orit Wasserman <<EMAIL>>
Date:   Mon Aug 22 10:28:14 2016 +0200

    Merge pull request #10216 from SUSE/wip-16637-jewel
    
    jewel: add socket backlog setting for via ceph.conf
    Reviewed-by: Orit Wasserman <<EMAIL>>

commit e1bc84776e7a95062311fb6b00a85a5327926c92
Merge: 089967f 429e9c0
Author: Orit Wasserman <<EMAIL>>
Date:   Mon Aug 22 10:27:03 2016 +0200

    Merge pull request #10167 from jmunhoz/aws4-streaming-backport-jewel
    
    jewel: rgw: aws4: add STREAMING-AWS4-HMAC-SHA256-PAYLOAD support
    Reviewed-by: Orit Wasserman <<EMAIL>>

commit 089967f4374569495d177c7dea1e345ca08c772a
Merge: 8e2de35 44decb4
Author: Orit Wasserman <<EMAIL>>
Date:   Mon Aug 22 10:13:51 2016 +0200

    Merge pull request #9544 from Abhishekvrshny/wip-16085-jewel
    
    jewel: A query on a static large object fails with 404 error
    Reviewed-by: Orit Wasserman <<EMAIL>>

commit 8e2de35324258cdbe44dfe8e7ce757ca1b3ef99b
Merge: 3f007a1 059ed62
Author: Orit Wasserman <<EMAIL>>
Date:   Mon Aug 22 10:10:25 2016 +0200

    Merge pull request #10525 from Abhishekvrshny/wip-16732-jewel
    
    jewel: Bucket index shards orphaned after bucket delete
    Reviewed-by: Orit Wasserman <<EMAIL>>

commit 3f007a192f8629b7a6000846db41550eb94cdcb2
Merge: 1019325 8655803
Author: Orit Wasserman <<EMAIL>>
Date:   Mon Aug 22 10:09:21 2016 +0200

    Merge pull request #10188 from dreamhost/wip-16618-jewel
    
    jewel: rgw: fix multi-delete query param parsing.
    Reviewed-by: Orit Wasserman <<EMAIL>>

commit 1019325af6c7153893a74b126f2c5e6534841bc4
Merge: 0cd574f 4a3c9f3
Author: Orit Wasserman <<EMAIL>>
Date:   Mon Aug 22 10:06:51 2016 +0200

    Merge pull request #9266 from Abhishekvrshny/wip-15964-jewel
    
    jewel: rgw: realm pull fails when using apache frontend
    Reviewed-by: Orit Wasserman <<EMAIL>>

commit 0cd574f5a9a1db4ddfe262e2c4eb4643c5af9075
Merge: cdd4ab2 dbf8cf0
Author: Loic Dachary <<EMAIL>>
Date:   Mon Aug 22 10:02:20 2016 +0200

    Merge pull request #10710 from rzarzynski/wip-16393-jewel
    
    jewel: rgw: improve support for Swift's object versioning.
    
    Reviewed-by: Orit Wasserman <<EMAIL>>

commit 1cbc839ebd11acb2ed0ebf0b0117eab09b5141c3
Author: Kefu Chai <<EMAIL>>
Date:   Mon Aug 15 15:42:29 2016 +0800

    osd: set objecter.osdmap when starts
    
    if any OSD is full or the pause flag is set in cluster, objecter will
    always try to subscribe the next osdmap using the mon client shared with
    OSD. but if the OSD's osdmap is very far behind on osd maps due to some
    reasons, `MonClient::sub_want_increment()` will reject the subscribe
    request from OSD, because it's asking for older maps while the mon client
    is about to send the request from objecter for the *next* osdmap.
    
    so we need to update objecter with the latest local osdmap, so it is
    able to skip the new osdmaps if OSD needs to catch up with the cluster
    first.
    
    Fixes: http://tracker.ceph.com/issues/17023
    Signed-off-by: Kefu Chai <<EMAIL>>
    (cherry picked from commit 1515e08e7d425f01d2e9d02c34b2ae40cdf2611a)

commit 4d37cfb6eed1737b21d9f3f18eee6169f48445b2
Author: Kefu Chai <<EMAIL>>
Date:   Fri Aug 12 19:19:51 2016 +0800

    mon/MonClient: should update sub_sent with sub_new
    
    std::map::insert() does not overwrite existing items with the same key.
    so we need to do this in a different way. and we will check sub_sent
    aftewards, so we need to keep an updated sub_sent around.
    
    Fixes: http://tracker.ceph.com/issues/17023
    Signed-off-by: Kefu Chai <<EMAIL>>
    (cherry picked from commit 550814900ebca9dd89e088b9abe24f66eb5e3e6d)

commit cdd4ab2897a3d0004b1e50401754004f95559e18
Merge: 38433ad 5498377
Author: Loic Dachary <<EMAIL>>
Date:   Sun Aug 21 21:48:20 2016 +0200

    Merge pull request #10797 from trociny/wip-17080-jewel
    
    jewel: the option 'rbd_cache_writethrough_until_flush=true' dosn't work
    
    Reviewed-by: Mykola Golub <<EMAIL>>

commit 5498377205523052476ed81aebb2c2e6973f67ef
Author: Jason Dillaman <<EMAIL>>
Date:   Wed Aug 17 12:08:37 2016 -0400

    librbd: cache was not switching to writeback after first flush
    
    Fixes: http://tracker.ceph.com/issues/16654
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 2d9840af39555ce00246b50e4a5c186798bd88ff)

commit 471871e752963bf8cc42dcc1ec7e44e34e7e0ca2
Author: Jason Dillaman <<EMAIL>>
Date:   Tue Jul 26 23:28:43 2016 -0400

    test: unit test cases for disabling librbd journal by policy
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 4a256f5044ac54a8a0a9598ee7fd151b8ea08f84)
    
    Conflicts:
            src/test/librbd/CMakeLists.txt (test_ConsistencyGroups.cc)

commit 062162f88f1bdaaaa96713429007757b72ec92f9
Author: Jason Dillaman <<EMAIL>>
Date:   Wed Jul 27 10:56:48 2016 -0400

    librbd: utilize factory methods to create AioObjectRequest objects
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 2e5076eb19172919deeb0f4d11f2b2b6cc03fab3)

commit d8eddc6b58fa2b0589719fab54beb42bc8e440bf
Author: Jason Dillaman <<EMAIL>>
Date:   Wed Jul 27 09:07:00 2016 -0400

    librbd: convert AioObjectRequest/AioObjectRead classes to templates
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 840a473e7fb94124ca7c571fc3dd2c787e0d265a)

commit 1a7cb60ee4b898aaea43a9963e95ed575ec66f56
Author: Jason Dillaman <<EMAIL>>
Date:   Wed Jul 27 09:54:44 2016 -0400

    librbd: move read callback helpers to narrowest scope
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 65b336f68596a430629692a8682c5cfe883c31f1)

commit 026f6d2da25d41f21f5bb5f8ee367cc7601296f7
Author: Jason Dillaman <<EMAIL>>
Date:   Tue Jul 26 23:26:08 2016 -0400

    librbd: convert AioImageRequest derived classes to templates
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit a945c2c235d9259b1d4a8a579d7e6efc2ed35916)

commit 61f0acb82ba344139bc2aa486fb096e5576e2553
Author: Jason Dillaman <<EMAIL>>
Date:   Wed Jul 27 08:49:44 2016 -0400

    librbd: removed namespace indentation from legacy classes
    
    Better follows the Ceph C++ style guide
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit f1e391982b43ddfb363ff913260460368a6d5834)

commit e83866bcf01f32c35af94a47ee614361b4a787e6
Author: Jason Dillaman <<EMAIL>>
Date:   Tue Jul 26 22:58:24 2016 -0400

    librbd: do not record journal events if append is disabled by policy
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 405142c615352613e8bacee46e92484eb0c08f26)

commit a9a84bad431f2d9916bdf88980b1cd2d0e36a1e5
Author: Jason Dillaman <<EMAIL>>
Date:   Tue Jul 26 20:57:40 2016 -0400

    librbd: remove unused journal replay canceled callback
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit f453554edcb16ec72714b2456c08bab9e339b1eb)

commit 942950007f9c424949eacf1d1cf3a74ef3b95725
Author: Jason Dillaman <<EMAIL>>
Date:   Tue Jul 26 20:48:13 2016 -0400

    librbd: optionally support disabling journal appends via policy
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit f7eeacd02f8f80c5d1d5d9cfed35bbd23f64b018)

commit 1f63307a339fcfe3045cc001a72d1ecc197a7010
Author: shawn <<EMAIL>>
Date:   Fri Jun 17 01:45:58 2016 -0400

    librbd: optimize header file dependency & modify related file.
    
    Signed-off-by: Xiaowei Chen <<EMAIL>>
    (cherry picked from commit 2185a6275c89d9462611e965da407ea0d504987d)

commit c49398dcd92bab90ab852407427bcc2c23147779
Author: shawn <<EMAIL>>
Date:   Tue Jun 7 03:12:55 2016 -0400

    librbd: optimize operation header file dependency
    
    Signed-off-by: Xiaowei Chen <<EMAIL>>
    (cherry picked from commit 88afa408683238b803606f3d63de326ead8cefee)

commit 5366973897c1103a1c291c2f886692da4170d901
Author: shawn <<EMAIL>>
Date:   Tue Jun 7 01:59:05 2016 -0400

    librbd: optimize journal header file dependency
    
    Signed-off-by: Xiaowei Chen <<EMAIL>>
    (cherry picked from commit 59397e27a04fb0f2b990a86dcaf3c418457d1b72)

commit c95b83e7c0cae0b322af713045745fb1c8fa411a
Author: shawn <<EMAIL>>
Date:   Mon Jun 6 23:36:06 2016 -0400

    librbd: optimize image_watcher header file dependency
    
    Signed-off-by: Xiaowei Chen <<EMAIL>>
    (cherry picked from commit aee1559f312e9493d6580e5ffcb606fe21ee3c59)

commit e955496fb638163427c71b0734f6506aa6636d28
Author: shawn <<EMAIL>>
Date:   Mon Jun 6 23:22:39 2016 -0400

    librbd: optimize image header file dependency
    
    Signed-off-by: Xiaowei Chen <<EMAIL>>
    (cherry picked from commit 461958ebc7e8ccef76b3f08a979482762267c10b)

commit f403abafd5da00e5afc13abba7514fa2bdce9760
Author: shawn <<EMAIL>>
Date:   Mon Jun 6 22:56:49 2016 -0400

    librbd: optimize exclusive_lock  header file dependency
    
    Signed-off-by: Xiaowei Chen <<EMAIL>>
    (cherry picked from commit 01282eb5cdeda7bbbb77438c7d1953d023ea54b6)

commit 0ca8071e13492a9124be8322843cd96ca2bba531
Author: shawn <<EMAIL>>
Date:   Mon Jun 6 03:37:22 2016 -0400

    librbd: optimize object-map header file dependency
    
    Signed-off-by: Xiaowei Chen <<EMAIL>>
    (cherry picked from commit 1a2276927d5956918f7a4830b4a44048ac090328)

commit 03314145d1bb5f4330a92d945af9df8856284724
Author: Mykola Golub <<EMAIL>>
Date:   Thu Jun 30 14:31:23 2016 +0300

    test: fix librbd tests for rbd_skip_partial_discard
    
    Signed-off-by: Mykola Golub <<EMAIL>>
    (cherry picked from commit 9717417b7b8aa1b24a82a0bfddbfc23748188641)
    
    Conflicts:
            src/rocksdb this was a mistake in the original commit

commit d0c0c2fce39091915428e815bc6aee265ac9e351
Author: Mykola Golub <<EMAIL>>
Date:   Thu Jun 30 14:30:08 2016 +0300

    librbd: discard hangs when 'rbd_skip_partial_discard' is enabled
    
    Fixes: http://tracker.ceph.com/issues/16386
    Signed-off-by: Mykola Golub <<EMAIL>>
    (cherry picked from commit dc41731fbfd73d9fbb63d3ff360d4c5dd62deaf1)

commit 5bf4398e373ecc26958d8480171f4322341a9d82
Author: xinxin shu <<EMAIL>>
Date:   Tue Jun 7 12:07:55 2016 +0800

    librbd: object_may_exist always return true when you write an empty object
    
    if you write an empty object, object map is updated firstly
    
    Signed-off-by: xinxin shu <<EMAIL>>
    (cherry picked from commit a54073808146d205d54d6a932a6e0fd750f1dc38)

commit 67861061fcd85f143e7d26077c71a582c6439745
Author: Jason Dillaman <<EMAIL>>
Date:   Mon Jul 25 12:43:13 2016 -0400

    librbd: ensure that AIO ops are started prior to request
    
    Fixes: http://tracker.ceph.com/issues/16708
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 6064f2346de0a8bf2878bf5bfac9a992cda7c4ca)

commit 47279f8e0158d0483011bca01455ef9735453b34
Author: Jason Dillaman <<EMAIL>>
Date:   Mon Jul 25 12:42:26 2016 -0400

    librbd: helper method for creating and starting AioCompletions
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 3df7213c0a9f0186e3a37c9e4a10f1c8bc84446e)

commit 38433ad90b81b4e398ae6f4b8e196af1a9ea20b8
Merge: b98e27c 9fc5b41
Author: Loic Dachary <<EMAIL>>
Date:   Fri Aug 19 20:35:19 2016 +0200

    Merge pull request #10649 from dachary/wip-16867-jewel
    
    jewel: mkfs.xfs slow performance with discards and object map
    
    Reviewed-by: Jason Dillaman <<EMAIL>>

commit d3367354b02be7ee231a50b9ad8bca098f840f46
Author: Jason Dillaman <<EMAIL>>
Date:   Mon Aug 8 14:41:00 2016 -0400

    rbd-mirror: potential assertion failure during error-induced shutdown
    
    Fixes: http://tracker.ceph.com/issues/16956
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 6a465d9dad417e8b52909c5478f7e3e433748948)

commit d1e05127b73c53a02944edc267548656732231fd
Author: Jason Dillaman <<EMAIL>>
Date:   Wed Aug 10 12:50:53 2016 -0400

    rbd-mirror: potential race condition during failure shutdown
    
    Fixes: http://tracker.ceph.com/issues/16980
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 74ec7c91f17630c77647cfc9813090d688b3410d)

commit 48ee3aacef8b1628f41936bd13cc0327c44e59ef
Author: Jason Dillaman <<EMAIL>>
Date:   Tue Aug 9 08:13:30 2016 -0400

    rbd-mirror: replaying state should include flush action
    
    Fixes: http://tracker.ceph.com/issues/16970
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 7246f1b771b4d1f336ac11b9e881c9eb32bcd4e1)

commit e098fb14f106bf294780abd5d580244e7ce3b20c
Author: Jason Dillaman <<EMAIL>>
Date:   Mon Aug 15 14:10:27 2016 -0400

    doc: added rbdmap man page to RBD restructured index
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 4e05cbf7e6eda797a9b08e0081aead19523d5762)

commit b7793d757fca99aa2eb371934e667b3cc555d666
Author: Jason Dillaman <<EMAIL>>
Date:   Mon Aug 15 14:07:43 2016 -0400

    rbdmap: specify bash shell interpreter
    
    Fixes: http://tracker.ceph.com/issues/16608
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 51237c33d1ec4034c5c07f5d63d63838a76bce88)

commit b98e27c42f29f4fa44cbe7f4b7d9ab3633404f0c
Merge: eb706ab 92d7882
Author: Loic Dachary <<EMAIL>>
Date:   Fri Aug 19 07:12:40 2016 +0200

    Merge pull request #10684 from dillaman/wip-16904-jewel
    
    jewel: rbd-mirror: reduce memory footprint during journal replay
    
    Reviewed-by: Loic Dachary <<EMAIL>>
    Reviewed-by: Jason Dillaman <<EMAIL>>

commit 92d7882661d632af9e9f07817610297411c0248f
Author: Mykola Golub <<EMAIL>>
Date:   Thu Jun 30 16:18:56 2016 +0300

    rbd-mirror: remove ceph_test_rbd_mirror_image_replay test case
    
    Fixes: http://tracker.ceph.com/issues/16539
    Signed-off-by: Mykola Golub <<EMAIL>>
    (cherry picked from commit 06a333b116383de4d39c9e098e6e5fa195ceb370)

commit 0d9d57385bcb544115ccdb00d6bbd67b987dbad0
Author: Jason Dillaman <<EMAIL>>
Date:   Wed Jul 20 16:18:23 2016 -0400

    qa/workunits/rbd: override rbd-mirror integration test poll frequency
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 574be7486ad737892422aed0322f80e5750a75a0)

commit 96cdb11934e359024c1a2f7cbffd55a5c7f6715b
Author: Jason Dillaman <<EMAIL>>
Date:   Thu Jul 21 07:28:54 2016 -0400

    rbd-mirror: do not cancel maintenance ops with missing finish events
    
    librbd will replay these ops when opening an image, so rbd-mirror
    should also ensure these ops are replayed.
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 862e581553fff510286b58135a1fd69705c06096)

commit d47a23272765633df2303e142d67d78af5bd2bf4
Author: Jason Dillaman <<EMAIL>>
Date:   Tue Jul 19 15:46:49 2016 -0400

    rbd-mirror: potential memory leak when attempting to cancel image sync
    
    The cancel request could race with the actual scheduling of the image
    sync operation.
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit e6cdf955bad500561ddada2791641eba5fb27762)

commit 1e4d98cdd4c5d671870735823a80e80751ba13ae
Author: Jason Dillaman <<EMAIL>>
Date:   Tue Jul 19 15:42:27 2016 -0400

    rbd-mirror: fix issues detected when attempting clean shut down
    
    Fixed lockdep issue from status update callback and fixed the
    potential for a stuck status state.
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 0275c7ca23b27dc5250cd33f317e2273470a9fe8)

commit e4c43190b49e94ccfeda05ac7a7a8835d81b49a5
Author: Jason Dillaman <<EMAIL>>
Date:   Tue Jul 19 13:50:20 2016 -0400

    rbd-mirror: shut down image replayers in parallel
    
    When multiple pools are being replicated, start the shut down
    process concurrently across all pool replayers.
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 73cdd08007c27d2c3c41fe644601e7a144f21c82)

commit 1e3821268fbd17581a1352401af626a3e56ee3d4
Author: Jason Dillaman <<EMAIL>>
Date:   Tue Jul 19 00:50:14 2016 -0400

    rbd-mirror: configuration options to control replay throttling
    
    Fixes: http://tracker.ceph.com/issues/16223
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 24883e0605907d1f9bcd1206c8a95c3bde30d5dc)

commit 549aada7f90b9f7d4a4447d2794c18d5f61807a5
Author: Jason Dillaman <<EMAIL>>
Date:   Wed Jul 20 08:11:53 2016 -0400

    librbd: new configuration option to restrict journal payload size
    
    Ensure that, by default, IO journal events are broken up into manageable
    sizes when factoring in that an rbd-mirror daemon might be replaying
    events from thousands of images.
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 11d7500b9bcda7b7c1d8756ade3373f404257f32)

commit 99195e89e4d229922c6e88aed6a024619e939040
Author: Jason Dillaman <<EMAIL>>
Date:   Tue Jul 19 00:42:16 2016 -0400

    librbd: wait for journal commit op event to be safely recorded
    
    Operation request op finish events should not be fire and forget.
    Instead, ensure the event is committed to the journal before
    completing the op. This will avoid several possible split-brain
    events during mirroring.
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 47e0fbf231e52d00069c97b72c57c3158445bcf0)
    
    Conflicts:
            src/test/librbd/operation/test_mock_ResizeRequest.cc: no shrink restriction

commit 3ae52ebadb5ef0de63ba30f937dcbcad507a7048
Author: Jason Dillaman <<EMAIL>>
Date:   Wed Jul 20 16:17:41 2016 -0400

    journal: optimize speed of live replay journal pruning
    
    When streaming playback, avoid the unnecessary watch delay when
    one or more entries have been pruned.
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 08a8ee98c03b5dfb30341c8d209f0c231b2c5d27)

commit 3850ded99728d1d6acfcaa72cf3923e791dd8fed
Author: Jason Dillaman <<EMAIL>>
Date:   Wed Jul 20 10:04:21 2016 -0400

    journal: possible deadlock during flush of journal entries
    
    If a future flush is requested at the exact same moment that an
    overflow is detected, the two threads will deadlock since locks
    are not taken in a consistent order.
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 2c65471de4b0f54b8ed722f5deaf51ba62632e37)

commit b4b984325f67a617c890ce6ccfbea5f42322cec5
Author: Jason Dillaman <<EMAIL>>
Date:   Wed Jul 20 09:15:26 2016 -0400

    journal: improve debug log messages
    
    rbd-mirror debugging involved potentially thousands of journals
    concurrently running. The instance address will correlate log
    messages between journals.
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 11475f4fe740cccdfea459ebeabdca8cb94dc911)

commit 196de276d5f1102a1cb4c7600252c9f1e783acc6
Author: Jason Dillaman <<EMAIL>>
Date:   Mon Jul 18 15:34:53 2016 -0400

    journal: support streaming entry playback
    
    Now that it's possible for the ObjectPlayer to only read a
    partial subset of available entries, the JournalPlayer needs
    to detect that more entries might be available.
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 28d5ca16cbcb445f985469413b2a8a3048ab66b7)

commit b08335d052ba04765690397f70d0d7f9b54898ac
Author: Jason Dillaman <<EMAIL>>
Date:   Mon Jul 18 15:15:58 2016 -0400

    journal: replay should only read from a single object set
    
    Previously it was prefetching up to 2 object sets worth of journal
    data objects which consumed too much memory.
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 2666d366645b22a5db2a2bcbfce466726bf0b3c0)

commit c6f5303fb2d12830d0c9ac8605884b0c89d16539
Author: Jason Dillaman <<EMAIL>>
Date:   Wed Jul 20 08:06:13 2016 -0400

    journal: optionally restrict maximum entry payload size
    
    Journal playback will need to read at least a full entry which was
    currently limited to the maximum object size. In memory constrained
    environment, this new optional limit will set a fix upper bound on
    memory usage.
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 8c1877b82fee0db1dba76252b32ff348226d41a7)

commit 6dc609f24e54c8363a5acecef982aa67beb6130e
Author: Jason Dillaman <<EMAIL>>
Date:   Mon Jul 18 11:01:26 2016 -0400

    journal: optionally fetch entries in small chunks during replay
    
    Support fetching the full object or incremental chunks (with a
    minimum of at least a single decoded entry if available).
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit f7362e9a57e484fffd840ca0eef01778dcacb65b)

commit 91c70f34edabfb3983da643478d746568ed6b738
Author: Jason Dillaman <<EMAIL>>
Date:   Mon Jul 18 09:31:40 2016 -0400

    journal: helper class for organizing optional settings
    
    Additional runtime configuration settings will be needed. The
    new class will avoid the need to expand the constructor.
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit dad8328f2d502d18923c35f7b86a0cc2ccec133a)

commit d68fe79eedc475c11858c9da5cb3fd2b66ed2b2f
Author: Jason Dillaman <<EMAIL>>
Date:   Mon Jul 11 15:32:45 2016 -0400

    rbd-mirror: preprocess journal events prior to applying
    
    Fixes: http://tracker.ceph.com/issues/16622
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 4df913d10b2dd0444db806fccb2812547efa1b56)

commit 01f5f3f15c0da44588b4644905953e234a551def
Author: Jason Dillaman <<EMAIL>>
Date:   Fri Jul 8 16:19:52 2016 -0400

    rbd-mirror: event preprocessor to handle snap rename operations
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit fdfca557370c9d86acb81d50edb6aafc42044747)

commit f71dc87e509ada03b40b2bec5ea82579709e0d2d
Author: Jason Dillaman <<EMAIL>>
Date:   Fri Jul 8 15:16:04 2016 -0400

    librbd: improve journaling debug log messages
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 270cb74bc276bfb1f828a6a6933fa827f6cdce42)
    
    Conflicts:
            src/librbd/journal/Replay.cc: no snap limit restriction

commit 14db53587aa9a918174b616f006c38d1b4c99e58
Author: Jason Dillaman <<EMAIL>>
Date:   Fri Jul 8 14:37:14 2016 -0400

    librbd: separate journal event decoding and processing
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 57cd75e8058b84b5dce38f3d8f4b7b4138ac6c9a)

commit 0c4a73fb422ea78e4c5db0763866206cbb54f120
Author: Jason Dillaman <<EMAIL>>
Date:   Fri Jul 8 09:14:58 2016 -0400

    librbd: record original snap name in snap rename journal event
    
    Remote peers need a key to map snapshot ids between clusters.
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit f70b90c48d9520bbb4bb29058375e8205cf63771)

commit 1a25490367343d7d4083961163c62f1c32cac105
Author: Jason Dillaman <<EMAIL>>
Date:   Fri Jul 8 09:13:07 2016 -0400

    librbd: simple duplicate op checks for all maintenance operations
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 77699bfe749bc7a898024638fb8347c53fe12123)
    
    Conflicts:
            src/test/librbd/mock/MockOperations.h: no shrink restriction

commit 45a0b74a3eb50395a8e5ebb0f2a87add5090ddc1
Author: Jason Dillaman <<EMAIL>>
Date:   Thu Jul 7 22:16:51 2016 -0400

    qa/workunits/rbd: exercise snapshot renames within rbd-mirror test
    
    Snapshot rename operations utilize the (cluster) unique snapshot
    sequence to prevent attempts at replays. When mirroring to a
    different cluster, these sequences will not align.
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 2f4cb26d8bbd1457bc261547103b56ad40b3c464)

commit c7ab24e7ed63dd1754753c7b0e5c8981a8454a0b
Author: Mykola Golub <<EMAIL>>
Date:   Wed Jun 15 11:31:14 2016 +0300

    librbd: re-register watch on old format image rename
    
    The watching object name is changed when renaming an old format image,
    so unregister the watcher before the rename, and register back after,
    to avoid "Transport endpoint is not connected" error.
    
    Fixes: http://tracker.ceph.com/issues/16321
    Signed-off-by: Mykola Golub <<EMAIL>>
    (cherry picked from commit 1a3973c8814200dc449a557a4fc8212911633985)

commit 46fdba458750a13d217708ae3209b0f8bd058072
Author: Jason Dillaman <<EMAIL>>
Date:   Thu Jun 23 20:28:33 2016 -0400

    rbd-mirror: gracefully restart pool replayer when blacklisted
    
    Fixes: http://tracker.ceph.com/issues/16349
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 2f55aa5e33b2fe242ebb9702ba9ff6f8d5cef96c)

commit a806cdbb057e6bf14322ec6c3da8670ea2e9cfdc
Author: Jason Dillaman <<EMAIL>>
Date:   Thu Jun 23 20:28:02 2016 -0400

    rbd-mirror: do not clear image replayer error code after shut down
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 1fc27545c2092c178293ed636b6aadb11bc8cbd3)

commit b88a851d96b224d8332aa76a61d17a659dd5d14e
Author: Jason Dillaman <<EMAIL>>
Date:   Thu Jun 23 16:57:50 2016 -0400

    rbd-mirror: image deleter should use provided librados connection
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 03c2aec4c613f11fcfed5bcd47855005f42abd79)

commit 4b05677581ce9910e356e84203000251c92d8405
Author: Jason Dillaman <<EMAIL>>
Date:   Thu Jun 23 16:03:03 2016 -0400

    rbd-mirror: each pool replayer should use its own librados connection
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 48f301decbf1f27937bb77a3b47e54933f272d7d)

commit 569fbee99d1367bc0d51e96e42dcf64eeb930648
Author: Jason Dillaman <<EMAIL>>
Date:   Mon Jun 27 09:05:33 2016 -0400

    rbd-mirror: clean up ImageReplayer before stopping state machine
    
    Fixes: http://tracker.ceph.com/issues/16489
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit c97f7245a015dbdda25584134840d023fd65cdd1)

commit eb706abd814fbd5cc704f21f3f9839ff09aaea6e
Merge: 3167918 fcc00f7
Author: Loic Dachary <<EMAIL>>
Date:   Wed Aug 17 19:17:56 2016 +0200

    Merge pull request #10679 from dillaman/wip-16735-jewel
    
    jewel: rbd-nbd does not properly handle resize notifications
    
    Reviewed-by: Jason Dillaman <<EMAIL>>

commit 3167918679b519ae7d2625ea08b96419111c9992
Merge: e6f9f28 2afc176
Author: Loic Dachary <<EMAIL>>
Date:   Wed Aug 17 19:10:04 2016 +0200

    Merge pull request #10148 from SUSE/wip-16599-jewel
    
    jewel: rgw: Swift API returns double space usage and objects of account metadata
    
    Reviewed-by: Pritha Srivastava <<EMAIL>>

commit 55460e2e7b0f1298796b37b38a64820a8f23f6e2
Author: Shilpa Jagannath <<EMAIL>>
Date:   Sat Aug 6 14:25:48 2016 +0530

    rgw: raise log levels for common radosgw-admin errors
    
    Currently while running radosgw-admin command we see these messages thrown at
    the endusers since debug level is set to 0:
    
    2016-08-02 14:48:28.687251 7f9e630639c0  0 Cannot find zone
    id=7a2a89b9-4f5e-4f60-a29e-451ac9acc5a8 (name=us-west), switching to local
    zonegroup configuration
    2016-08-02 14:48:35.742185 7f9e630639c0  0 error read_lastest_epoch
    .rgw.root:periods.a3db7884-2445-4bab-a165-6730f8573b8f:staging.latest_epoch
    
    Raising the log level to 1.
    
    Fixes: http://tracker.ceph.com/issues/16935
    Signed-off-by: Shilpa Jagannath <<EMAIL>>
    (cherry picked from commit 8ff2b8e71abd34648c8eefb6b158ae5de46bd709)

commit 73cc60862891cba8581370e3d5726a9f75ba1651
Author: Casey Bodley <<EMAIL>>
Date:   Fri Aug 5 11:28:13 2016 -0400

    rgw: fixes for period puller
    
    * reset epoch=0 before RGWPeriod::init() so we get the latest_epoch
    * if we're the metadata master, don't try to pull periods from ourself
    
    Fixes: http://tracker.ceph.com/issues/16939
    
    Signed-off-by: Casey Bodley <<EMAIL>>
    (cherry picked from commit 06c384f1c5aa5b4b6404a3caf8a5f88ee40b0b03)

commit f29864302648f38050626086bb87d47448a72aaf
Author: Radoslaw Zarzynski <<EMAIL>>
Date:   Fri Jul 15 17:18:23 2016 +0200

    rgw: ONLY improve code formatting in rgw_object_expirer_core.{cc,h}.
    
    Signed-off-by: Radoslaw Zarzynski <<EMAIL>>
    (cherry picked from commit bc1ecdfb42bf9c9c5825af035409359536ed5b1e)

commit e7be5a544a712142742323d573586dc9e98cbd77
Author: Radoslaw Zarzynski <<EMAIL>>
Date:   Fri Jul 15 17:11:04 2016 +0200

    rgw: fix skipping some objects to delete by RadosGW's object expirer.
    
    Wei Qiaomiao has found that expired objects, which should be removed by
    the object expirer of RadosGW, might be left unprocessed till next
    restart of a RadosGW's instance. This happens when process_single_shard
    method of RGWObjectExpirer class exhaust a time slot for a single round
    and finishes without informing caller about the situation.
    
    Fixes: http://tracker.ceph.com/issues/16705
    Signed-off-by: Radoslaw Zarzynski <<EMAIL>>
    (cherry picked from commit 99f7d6eb565cee116c662305411df2adef002cae)

commit af7e1e0dde953a56f33197c80da499e89f3dfc93
Author: Radoslaw Zarzynski <<EMAIL>>
Date:   Fri Jul 15 17:05:37 2016 +0200

    rgw: fix trimming object expirer's hints.
    
    Fixes: http://tracker.ceph.com/issues/16684
    Signed-off-by: Radoslaw Zarzynski <<EMAIL>>
    (cherry picked from commit 83cd7852ed1fd8350d7c95a1b8811ffd34d7789f)

commit dc154cd1e497b02386b91fde28a658d79ec6659e
Author: Kefu Chai <<EMAIL>>
Date:   Mon Jul 25 11:12:14 2016 +0800

    osd: fix the mem leak of RepGather
    
    ReplicatedPG::new_repop() returns a pointer to RepGather with two refcounts,
    one is held by ReplicatedPG::repop_queue, the other is supposed to be
    held by the caller of this function. but it's caller
    ReplicatedPG::submit_log_entries() assigns it to a
    boost::intrusive_ptr<RepGather>() directly, why by default add_ref() in
    its constructor. this makes the refcount 3. that's why we have a leak of
    RepGather in `ReplicatedPG::new_repop(ObcLockManager&&,
    boost::optional<std::function<void ()>>&&)`.
    
    Fixes: http://tracker.ceph.com/issues/16801
    Signed-off-by: Kefu Chai <<EMAIL>>
    (cherry picked from commit d3a28465fc7b7002f38cff364fdf601f7486add3)

commit e6f9f287e226e44035920827be2a45753f7452d1
Merge: 84e3810 90d29fc
Author: Loic Dachary <<EMAIL>>
Date:   Wed Aug 17 13:41:12 2016 +0200

    Merge pull request #10678 from dillaman/wip-16701-jewel
    
    jewel: rbd-mirror: include local pool id in resync throttle unique key
    
    Reviewed-by: Jason Dillaman <<EMAIL>>

commit 84e38109472b4a2a169eb8286c6632da4b1edb6e
Merge: c2defb0 0ff0960
Author: Loic Dachary <<EMAIL>>
Date:   Wed Aug 17 13:39:02 2016 +0200

    Merge pull request #10614 from SUSE/wip-16959-jewel
    
    jewel: rpm: OBS needs ExclusiveArch
    
    Reviewed-by: Loic Dachary <<EMAIL>>

commit c2defb0cea4ba9eed88af3f234c248c57ba48f4f
Merge: 6d7ebb1 461782e
Author: Loic Dachary <<EMAIL>>
Date:   Wed Aug 17 13:38:27 2016 +0200

    Merge pull request #10519 from Abhishekvrshny/wip-16312-jewel
    
    jewel: selinux denials in RGW
    
    Reviewed-by: Loic Dachary <<EMAIL>>

commit 6d7ebb10e6b20f7c3656fd763d96e64ac247e59f
Merge: 338a58c b26acc0
Author: Loic Dachary <<EMAIL>>
Date:   Wed Aug 17 13:37:51 2016 +0200

    Merge pull request #10364 from SUSE/wip-16750-jewel
    
    jewel: ceph-osd-prestart.sh contains Upstart-specific code
    
    Reviewed-by: Loic Dachary <<EMAIL>>

commit 338a58ca2ed9ba27f9d66c22e725322ce15466af
Merge: c7a1c81 c1a47c7
Author: Loic Dachary <<EMAIL>>
Date:   Wed Aug 17 13:36:47 2016 +0200

    Merge pull request #10008 from dachary/wip-16099-jewel
    
    jewel: ceph-base requires parted
    
    Reviewed-by: Loic Dachary <<EMAIL>>

commit c7a1c812c4aab478ee38db60947e6f686e8a7407
Merge: 2d5ada4 ad3b788
Author: Loic Dachary <<EMAIL>>
Date:   Wed Aug 17 13:35:54 2016 +0200

    Merge pull request #9917 from SUSE/wip-16461-jewel
    
    jewel: ceph Resource Agent does not work with systemd
    
    Reviewed-by: Loic Dachary <<EMAIL>>

commit 96ad2d16df6ec43cd730a4c21b866524a188c532
Author: Yehuda Sadeh <<EMAIL>>
Date:   Thu Jun 2 04:28:30 2016 -0700

    rgw: can set negative max_buckets on RGWUserInfo
    
    This can be used to disable user's bucket creation
    
    Fixes: http://tracker.ceph.com/issues/14534
    
    Signed-off-by: Yehuda Sadeh <<EMAIL>>
    (cherry picked from commit 626d795139a6c5104be098780b70500c2de9b8f4)

commit 2d5ada4ef8ee7d6803a903b7395e0c2b19b781ea
Merge: aa03e1d 00f3fd4
Author: Loic Dachary <<EMAIL>>
Date:   Tue Aug 16 16:39:22 2016 +0200

    Merge pull request #10303 from ukernel/jewel-16655
    
    jewel: ceph-fuse: link to libtcmalloc or jemalloc
    
    Reviewed-by: Loic Dachary <<EMAIL>>

commit aa03e1dbe6b243c56ed40eee8734051f434c2ed2
Merge: 0280773 3da251f
Author: Loic Dachary <<EMAIL>>
Date:   Tue Aug 16 16:21:01 2016 +0200

    Merge pull request #10420 from SUSE/wip-16798-jewel
    
    jewel: ceph command line tool chokes on ceph –w (the dash is unicode 'en dash' &ndash, copy-paste to reproduce)
    
    Reviewed-by: Kefu Chai <<EMAIL>>

commit dbf8cf0b5d46cc49eaf0a14bcc05a4fb215d98fb
Author: Radoslaw Zarzynski <<EMAIL>>
Date:   Fri May 13 18:23:12 2016 +0200

    rgw: improve support for Swift's object versioning.
    
    This patch allows RadosGW to pass the RefStack with an accuracy
    to the RFC7230 violation issue which is clearly a Tempest bug.
    
    Fixes: http://tracker.ceph.com/issues/15925
    Signed-off-by: Radoslaw Zarzynski <<EMAIL>>
    (cherry picked from commit 237ad1210f5a6e4f191293ce532ca15869612a93)
    
    Conflicts:
            src/rgw/rgw_op.cc
              In contrast to master, Jewel doesn't support
              container quota of Swift API. All tracks of this
              feature have been eradicated from the patch.
            src/rgw/rgw_rest_swift.cc
              Jewel doesn't include boost/optional.hpp but has
              boost/utility/in_place_factory.hpp. The conflict
              has been resolved to pull in both headers.

commit 02807732b0f66826c2d110fe06ece15751924760
Merge: 1e04a35 67eb961
Author: Loic Dachary <<EMAIL>>
Date:   Tue Aug 16 10:40:05 2016 +0200

    Merge pull request #10520 from Abhishekvrshny/wip-16319-jewel
    
    jewel: radosgw-admin: inconsistency in uid/email handling
    
    Reviewed-by: Orit Wasserman <<EMAIL>>

commit 1e04a357a6e9e67c81b5104f57ab61e5ce07a5e3
Merge: f6e927c 3f76e4a
Author: Casey Bodley <<EMAIL>>
Date:   Mon Aug 15 17:18:36 2016 -0400

    Merge pull request #10073 from SUSE/wip-16565-jewel
    
    jewel: rgw: data sync stops after getting error in all data log sync shards
    
    Reviewed-by: Casey Bodley <<EMAIL>>

commit f6e927cfe3e296ebd5e3b22c3f05d717f87cd4ed
Merge: d2e8692 93d7875
Author: Casey Bodley <<EMAIL>>
Date:   Mon Aug 15 17:18:06 2016 -0400

    Merge pull request #10524 from Abhishekvrshny/wip-16731-jewel
    
    jewel: failed to create bucket after upgrade from hammer to jewel
    
    Reviewed-by: Casey Bodley <<EMAIL>>

commit d2e869255869ae68ca419d5f0a3bbfbcf3a91710
Merge: 87e71aa dc96383
Author: Casey Bodley <<EMAIL>>
Date:   Mon Aug 15 17:16:41 2016 -0400

    Merge pull request #10523 from Abhishekvrshny/wip-16700-jewel
    
    jewel: rgw: segmentation fault on error_repo in data sync
    
    Reviewed-by: Casey Bodley <<EMAIL>>

commit 87e71aae7a2d73b7b5444e1a5ab283ece570e3fb
Merge: cc60230 fe57ace
Author: Casey Bodley <<EMAIL>>
Date:   Mon Aug 15 17:16:06 2016 -0400

    Merge pull request #10518 from Abhishekvrshny/wip-16272-jewel
    
    jewel: rgw ldap: fix ldap bindpw parsing
    
    Reviewed-by: Casey Bodley <<EMAIL>>

commit cc60230eef82173eafa52df3d2015c8c0473d236
Merge: 31264f4 489f8ce
Author: Loic Dachary <<EMAIL>>
Date:   Mon Aug 15 17:59:51 2016 +0200

    Merge pull request #10144 from dillaman/wip-fix-missing-return-jewel
    
    jewel: librbd: fix missing return statement if failed to get mirror image state
    
    Reviewed-by: Loic Dachary <<EMAIL>>

commit 31264f4126f79c26cff73b2a4d2bea77426c5cd9
Merge: 4ee9117 c6546bb
Author: Loic Dachary <<EMAIL>>
Date:   Mon Aug 15 17:55:42 2016 +0200

    Merge pull request #10552 from ceph/wip-16507-jewel
    
    jewel: expose buffer const_iterator symbols
    
    Reviewed-by: Loic Dachary <<EMAIL>>

commit 4ee9117d52d41a72444722afe81ce2074dbf58d3
Merge: 5b7899e 3d6d36a
Author: Loic Dachary <<EMAIL>>
Date:   Mon Aug 15 17:53:40 2016 +0200

    Merge pull request #10497 from Abhishekvrshny/wip-16586-jewel
    
    jewel: partprobe intermittent issues during ceph-disk prepare
    
    Reviewed-by: Loic Dachary <<EMAIL>>

commit 5b7899e89154eceb53625cd6f7ae33984298857f
Merge: c65536c bd70d6d
Author: Josh Durgin <<EMAIL>>
Date:   Mon Aug 15 08:35:57 2016 -0700

    Merge pull request #10716 from ceph/wip-16975
    
    qa: add rados test script for upgrades
    
    Reviewed-by: Sage Weil <<EMAIL>>

commit c65536cf4f3433fea2cc4683da930676f2def223
Merge: 40e3e2e 5ffdc34
Author: Jason Dillaman <<EMAIL>>
Date:   Mon Aug 15 08:28:32 2016 -0400

    Merge pull request #10732 from dillaman/wip-rbd-default-format-jewel
    
    doc: format 2 now is the default image format
    
    Reviewed-by: Jason Dillaman <<EMAIL>>

commit 5ffdc344401e91c4db89f9ed017adb15e535ebb4
Author: Chengwei Yang <<EMAIL>>
Date:   Fri Aug 12 17:44:16 2016 +0800

    doc: format 2 now is the default image format
    
    Signed-off-by: Chengwei Yang <<EMAIL>>
    (cherry picked from commit c8c92f8a2c2106d11b18c70c4c183c3724c2f3c2)

commit 40e3e2e075f21c2e4d36219747d6fdba530d5ed8
Merge: f58ca19 2c39d36
Author: Loic Dachary <<EMAIL>>
Date:   Mon Aug 15 11:14:58 2016 +0200

    Merge pull request #10217 from SUSE/wip-16636-jewel
    
    jewel: rgw: document multi tenancy
    
    Reviewed-by: Loic Dachary <<EMAIL>>

commit f58ca19f36179a64e50e2d14555b1efbb8fbfa79
Merge: 40fc75e 546141c
Author: Orit Wasserman <<EMAIL>>
Date:   Mon Aug 15 10:02:12 2016 +0200

    Merge pull request #9453 from wido/jewel-issue-15348
    
    jewel: rgw: Set Access-Control-Allow-Origin to a Asterisk if allowed in a rule
    Reviewed-by: Orit Wasserman <<EMAIL>>

commit 40fc75ef4fafada6aa3fac47963da060f50646b0
Merge: ec14cf5 3ff6e8f
Author: Orit Wasserman <<EMAIL>>
Date:   Fri Aug 12 17:41:07 2016 +0200

    Merge pull request #10693 from dreamhost/wip-15975-jewel
    
    rgw: Fallback to Host header for bucket name.
    Reviewed-by: Orit Wasserman <<EMAIL>>

commit bd70d6ddba1bc237a6ffafa0dadea16b2c65a39c
Author: Josh Durgin <<EMAIL>>
Date:   Thu Aug 11 20:39:20 2016 -0700

    qa: remove tmap_migrate tests from upgrade testing
    
    tmap_upgrade only exists in jewel
    
    Signed-off-by: Josh Durgin <<EMAIL>>

commit 49db733bad375645214ddb8f9b95b8817f9b41e1
Author: Josh Durgin <<EMAIL>>
Date:   Thu Aug 11 16:05:04 2016 -0700

    qa: add rados test script for upgrades
    
    Disable master tests that are not meant to work on jewel
    
    Signed-off-by: Josh Durgin <<EMAIL>>

commit 3ff6e8fa852346f4b69cd8c10b2f14ae4983d527
Author: Robin H. Johnson <<EMAIL>>
Date:   Thu May 26 15:41:20 2016 -0700

    rgw: Fallback to Host header for bucket name.
    
    RGW should fallback to using the Host header as the bucket name if valid &
    possible even when it is NOT a suffix match against the DNS names, or a match
    against the CNAME rule.
    
    This mirrors AWS S3 behavior for these cases (The AWS S3 does not do any DNS
    lookups regardless).
    
    Backport: jewel
    Fixes: http://tracker.ceph.com/issues/15975
    Signed-off-by: Robin H. Johnson <<EMAIL>>
    (cherry picked from commit 46aae19eeb91bf3ac78a94c9d4812a788d9042a8)

commit 90d29fc94a826b1f56ff99e5b5d81a735abbc4cd
Author: Jason Dillaman <<EMAIL>>
Date:   Mon Jul 11 21:58:45 2016 -0400

    rbd-mirror: include local pool id in resync throttle unique key
    
    Fixes: http://tracker.ceph.com/issues/16536
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 8ad36cab7cbf3492bfa972b43e4a5f75a110bfe6)

commit 03c76148ef9e10fad649a407374f4fc855529377
Author: Jason Dillaman <<EMAIL>>
Date:   Tue Jun 28 15:18:51 2016 -0400

    test: ensure unique template specializations per test case
    
    With optimizations disabled, there can be cross translation unit
    symbol leaking that is not present when inlining is enabled.
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 995a16ebcbe79fd14e6de3b25087373744238294)

commit ea9e031e2144ee32b3776cfe2237681f7065861b
Author: Jason Dillaman <<EMAIL>>
Date:   Tue Jun 28 14:59:17 2016 -0400

    test: missing template specialization for unittest_rbd_mirror
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 187e2c87b50d64e93736a42fcf449c55e8550125)

commit c2e6d08469ea733dac984770c30d8ab6356f3eca
Author: Jason Dillaman <<EMAIL>>
Date:   Wed Jun 15 17:49:54 2016 -0400

    qa/workunits/rbd: increase writes in large image count test
    
    This will help to test edge cases where the remote image does
    or does not own the exclusive lock when the sync starts.
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit d16698f96c65b9627cc1f64ef80b7f1e39d69d45)

commit 88a7c5c1b28c6a077d35577787757b0a98b7b2a5
Author: Jason Dillaman <<EMAIL>>
Date:   Wed Jun 15 17:42:59 2016 -0400

    rbd-mirror: prune sync points referencing missing snapshots
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 378914f6b8a8a8c5517fee8fa186268078fbcbfb)

commit 9a6bad3bd7eaa733042c0a23aaf48be4fa6f84ce
Author: Jason Dillaman <<EMAIL>>
Date:   Wed Jun 15 16:34:44 2016 -0400

    rbd-mirror: ignore empty snapshot sequence mapping
    
    This invalid condition will be gracefully detected and handled
    when the snapshot mappings are computed.
    
    Fixes: http://tracker.ceph.com/issues/16329
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 58ed8a18e91401333bc3f3f957ce5d715b6687b6)

commit c8089dea98fd40651664ae80b0e16834bf727540
Author: Jason Dillaman <<EMAIL>>
Date:   Mon Jun 27 13:01:13 2016 -0400

    qa/workunits/rbd: remove temporary image exports
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 45498d04990861a034e2aad69a93658e018c991a)

commit bdb2189c94badb6e389a3a05794ad8af3eceda1c
Author: Jason Dillaman <<EMAIL>>
Date:   Mon Jun 27 11:13:29 2016 -0400

    rbd-mirror: fix potential image replayer state transition race
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 3e224c6c03a6a5de0e179bd788387cfa3ff49e9d)

commit e7d7990958bdd8add7cfdcf037bcda5dd05c59d4
Author: Jason Dillaman <<EMAIL>>
Date:   Mon Jun 27 11:06:57 2016 -0400

    rbd-mirror: cancel image deletion callback on shut down
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit c3f1cb3a34e1b4d258877d519e683e25bf65c65a)

commit bba7811489170e01471ef7c086066a006f57fb6b
Author: Jason Dillaman <<EMAIL>>
Date:   Mon Jun 27 10:58:09 2016 -0400

    rbd-mirror: fixed potential leaking image deletion context callback
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 9d1cd90c220f95d23d2694b4f1fb6b2fed073ced)

commit b71efb090708e30225502d0cbf974b880def1eb8
Author: Jason Dillaman <<EMAIL>>
Date:   Mon Jun 27 09:45:25 2016 -0400

    rbd-mirror: ImageReplayer doesn't need image deleted callback
    
    The Replayer will detect that the ImageReplayer is stopped and will
    schedule the "wait for deleted" callback before attempting to
    restart the ImageReplayer.
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 7e5afc71dfd3ec3e7b0ddaca96f92ac6e0414006)

commit b657d1849a37bc4461edb18337078041a5460818
Author: Jason Dillaman <<EMAIL>>
Date:   Mon Jun 27 09:21:05 2016 -0400

    rbd-mirror: use async callback when deletion not in-progress
    
    Fixes: http://tracker.ceph.com/issues/16491
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit c136f443366fe804057a9c1f8c01c3f0a33a4d63)

commit c9c1216b3c06e49d3f0addeb7a4f6f5796c00d30
Author: Ricardo Dias <<EMAIL>>
Date:   Thu Jun 9 10:40:23 2016 +0100

    rbd-mirror: tests: ImageSyncThrottler unit tests
    
    Signed-off-by: Ricardo Dias <<EMAIL>>
    (cherry picked from commit 8ca9a84f36efa73ad17fd27f545dff3716cd798c)

commit c56d6ec4c1898e710aad307b5d3696b9b159ba0c
Author: Ricardo Dias <<EMAIL>>
Date:   Wed Jun 8 16:38:01 2016 +0100

    rbd-mirror: Usage of image-sync throttler in BootstrapRequest
    
    Fixes: http://tracker.ceph.com/issues/15239
    
    Signed-off-by: Ricardo Dias <<EMAIL>>
    (cherry picked from commit 6a91146255d84229688bd8b378732be5975a778b)

commit 5323ebd40a96484f5341574ed8a783c2e696f5dc
Author: Ricardo Dias <<EMAIL>>
Date:   Wed Jun 8 16:37:20 2016 +0100

    rbd-mirror: Implementation of image-sync throttler
    
    Signed-off-by: Ricardo Dias <<EMAIL>>
    (cherry picked from commit c4f926d6980d1efd95771885a11d8cc4ebd2e4c3)

commit 783bd686c451ae816ea9c7001b3c6a0ef1b5f554
Author: Ricardo Dias <<EMAIL>>
Date:   Tue Jun 14 16:43:19 2016 +0100

    rbd-mirror: tests: Support for inflight image sync point update
    
    Signed-off-by: Ricardo Dias <<EMAIL>>
    (cherry picked from commit cee543bd96bc99f219024aedbfafc5dcd99abb5a)

commit b878f5ffc75e70b1f09eee461f93b1cb5031586e
Author: Ricardo Dias <<EMAIL>>
Date:   Thu Jun 2 10:04:41 2016 +0100

    rbd-mirror: image-sync: Periodically update sync point object number
    
    Fixes: http://tracker.ceph.com/issues/15108
    
    Signed-off-by: Ricardo Dias <<EMAIL>>
    (cherry picked from commit c2eedf4d27b73d7eaf3fda54f9b0ee74e455bc1d)

commit 2ba61a73b1c616fab5acb1b92fe0a0b7ce7b64cb
Author: Ricardo Dias <<EMAIL>>
Date:   Mon Jun 27 11:07:41 2016 +0100

    rbd-mirror: image-replayer: Fix bug in resync listener remotion
    
    Fixes: http://tracker.ceph.com/issues/16488
    
    Signed-off-by: Ricardo Dias <<EMAIL>>
    (cherry picked from commit 11a5851e90bca2c1813156bf12dda0192965db37)

commit cbd1ab461d5d75baf6e8387788740a6bcb3520de
Author: Ricardo Dias <<EMAIL>>
Date:   Thu May 19 12:06:08 2016 +0100

    rbd-mirror: resync: Added unit tests
    
    Signed-off-by: Ricardo Dias <<EMAIL>>
    (cherry picked from commit 8953825956f2a560d17c7b651d1724a95f2f7d1d)

commit 5fe93a4383521b7b67d7f168e4f20735440bb8ca
Author: Ricardo Dias <<EMAIL>>
Date:   Thu May 12 18:12:33 2016 +0100

    rbd-mirror: image-replayer: Implementation of resync operation
    
    Fixes: http://tracker.ceph.com/issues/15670
    
    Signed-off-by: Ricardo Dias <<EMAIL>>
    (cherry picked from commit f2b114238103315197a9eefc8490786c484c8520)

commit c2b786ca92728ef6ceeb836133598a837cd18a2c
Author: Ricardo Dias <<EMAIL>>
Date:   Thu May 12 18:10:38 2016 +0100

    rbd: journal: Support for listening updates on client metadata
    
    Currently we only support listening for image resync requests.
    
    Signed-off-by: Ricardo Dias <<EMAIL>>
    (cherry picked from commit 0dd85739e9034912b86250ced2834dd7fb3d92fd)

commit 21d2ba5eefd1ef904360ce09b4335df77c952d95
Author: Ricardo Dias <<EMAIL>>
Date:   Thu May 12 18:09:09 2016 +0100

    journal: Support for registering metadata listeners in the Journaler
    
    Signed-off-by: Ricardo Dias <<EMAIL>>
    (cherry picked from commit c535eb0399cd649371db233ee1ed01cf5441a4b3)

commit fcc00f7ac2c9ed517d12ef9ebab735bdd0d1a162
Author: Mykola Golub <<EMAIL>>
Date:   Mon Jun 27 10:53:09 2016 +0300

    test: fsx: fix rbd-nbd daemon logging
    
    Signed-off-by: Mykola Golub <<EMAIL>>
    (cherry picked from commit 09710c46d3811e602250f858fe7292925c1e3c06)

commit 63fbed0f39da22374f6818030674c6b5e73f41dd
Author: Mykola Golub <<EMAIL>>
Date:   Fri Jun 3 21:11:21 2016 +0300

    qa/workunits/rbd: image resize test for rbd-nbd
    
    Signed-off-by: Mykola Golub <<EMAIL>>
    (cherry picked from commit fa58acbb6fcc79e5a9bfd4b86250a0e2b9c8efa1)

commit 3db1a3c8ea5a6829e1e44ecd0c96316c759bf048
Author: Mykola Golub <<EMAIL>>
Date:   Tue May 24 15:52:00 2016 +0300

    rbd-nbd: use librbd API method to watch image size update
    
    Fixes: http://tracker.ceph.com/issues/15715
    Signed-off-by: Mykola Golub <<EMAIL>>
    (cherry picked from commit 1e1d3b1609432ea80d6fd1c5b3df49f38ad579f1)

commit 8f94f5e3f7ba0cb3578952ffbca85c0dc6a772a0
Author: Mykola Golub <<EMAIL>>
Date:   Tue May 24 15:50:22 2016 +0300

    librbd: API: methods to watch image stat update
    
    Signed-off-by: Mykola Golub <<EMAIL>>
    (cherry picked from commit 9952b75a0c8c9bed9792cb77671fd5588f32630f)
    
    Conflicts:
            src/include/rbd/librbd.h: removed CG references
            src/tracing/librbd.tp: removed CG references

commit ec14cf5ce3e8d04eee3f13aadcfc2d7f22c5a633
Merge: c587e9e 393bf7e
Author: Orit Wasserman <<EMAIL>>
Date:   Thu Aug 11 11:31:40 2016 +0200

    Merge pull request #9790 from SUSE/wip-16381-jewel
    
    jewel: comparing return code to ERR_NOT_MODIFIED in rgw_rest_s3.cc (needs minus sign)
    Reviewed-by: Orit Wasserman <<EMAIL>>

commit 22c9df1d9bef3f48ab2a12357035fc54fbde141d
Author: Pritha Srivastava <<EMAIL>>
Date:   Fri Jul 1 14:15:42 2016 +0530

    rgw: Fix for using port 443 with pre-signed urls.
    
    Fixes http://tracker.ceph.com/issues/16548
    
    Signed-off-by: Pritha Srivastava <<EMAIL>>
    (cherry picked from commit 44b9ed37ac659d83fbd4bb99c69da6b9dec06b8d)

commit ce5e250405449c6035b805bbcdcca73ad4399d68
Author: Shilpa Jagannath <<EMAIL>>
Date:   Fri Jun 17 18:29:42 2016 +0530

    rgw: added zone rename to radosgw_admin
    
    Added zone rename option to radosgw-admin help
    
    Signed-off-by: Shilpa Jagannath <<EMAIL>>
    (cherry picked from commit da3235234eaf41f9dbd378fb0d9994d2c4096947)

commit ce986aa6e8b3ee54697802a361246c256e8097b4
Author: Casey Bodley <<EMAIL>>
Date:   Thu Jul 14 13:38:44 2016 -0400

    rgw: RGWMetaSyncCR holds refs to stacks for wakeup
    
    because RGWCoroutine::wakeup() calls RGWCoroutinesStack::wakeup(), the
    stack must also stay alive
    
    Fixes: http://tracker.ceph.com/issues/16666
    
    Signed-off-by: Casey Bodley <<EMAIL>>
    (cherry picked from commit e4bc16044e1b80636855dbc39da1d121a3508308)

commit 2485efc26d68b6848cda02d66b54dd58769fcbd8
Author: Casey Bodley <<EMAIL>>
Date:   Tue Jul 26 12:50:13 2016 -0400

    rgw: clear realm watch on failed watch_restart
    
    if the realm is deleted while the gateway has a watch, it's disconnected
    and watch_restart() will fail. this results in a watch handle of 0,
    which leads to a segfault on ~RGWRealmWatcher when we pass it to
    unwatch()
    
    this commit cleans up the watch when watch_restart() fails, so we don't
    try to unwatch() on destruction
    
    Fixes: http://tracker.ceph.com/issues/16817
    
    Signed-off-by: Casey Bodley <<EMAIL>>
    (cherry picked from commit e5b8b5b27502fdacdac38effd7a55fd719911e85)

commit fa2e42d4614619f761a5cf4c86e3db7d7e101e11
Author: Casey Bodley <<EMAIL>>
Date:   Wed Jul 27 12:16:57 2016 -0400

    rgw: use endpoints from master zone instead of zonegroup
    
    Fixes: http://tracker.ceph.com/issues/16834
    
    Signed-off-by: Casey Bodley <<EMAIL>>
    (cherry picked from commit d4872ec9f7554ff49f4be336fe59cdd2051ee2a5)

commit a865f26e68a991eaf55dc5f105b7fd7cb4634bc3
Author: Casey Bodley <<EMAIL>>
Date:   Thu Jul 28 09:55:05 2016 -0400

    radosgw-admin: zone[group] modify can change realm id
    
    allows the default zone and zonegroup (created with empty realm_id) to
    be later added to a realm. the 'modify' command now accepts either
    --realm_id=id or --rgw-realm=name
    
    Fixes: http://tracker.ceph.com/issues/16839
    
    Signed-off-by: Casey Bodley <<EMAIL>>
    (cherry picked from commit 064b7e953dd6ace2c32b94150e70959e95a01761)

commit c587e9e5c73cd8a990106708a43222fcb5a2a709
Merge: aacb793 3250c4d
Author: Loic Dachary <<EMAIL>>
Date:   Wed Aug 10 13:33:12 2016 +0200

    Merge pull request #10026 from SUSE/wip-16392-jewel
    
    jewel: master: build failures with boost > 1.58
    
    Reviewed-by: Loic Dachary <<EMAIL>>

commit f5db5a4b0bb52fed544f277c28ab5088d1c3fc79
Author: John Spray <<EMAIL>>
Date:   Tue May 17 17:53:56 2016 +0100

    mon: tolerate missing osd metadata
    
    Just because one OSD's metadata is missing,
    don't give up on outputting all the other
    OSDs' metadata.
    
    Signed-off-by: John Spray <<EMAIL>>
    (cherry picked from commit 2c7dd5f22288c4aee814573baa6af131421f36a4)

commit 36e5c86469eda59f7d5d15b16e15d0aab81a34fa
Author: John Spray <<EMAIL>>
Date:   Tue May 17 14:03:09 2016 +0100

    mon: fix metadata dumps for empty lists
    
    Because 'r' was set inside loop, these would
    fail to set it (leaving ENOSYS) if no
    metadata is present.
    
    Signed-off-by: John Spray <<EMAIL>>
    (cherry picked from commit 5fe4fe4d92f5b2d67896beac9b5b3b48b309a6b4)
    
    Conflicts:
            src/mon/Monitor.cc and src/mon/MDSMonitor.cc
            changes relate to features that are not backported to jewel

commit de99bd53f5efe84647e1a1d79a8db330887b7704
Author: Kefu Chai <<EMAIL>>
Date:   Sun May 15 10:32:28 2016 +0800

    mon: 'std::move` Metadata when updating it
    
    to avoid copying of the std::map
    
    Signed-off-by: Kefu Chai <<EMAIL>>
    (cherry picked from commit dd67eaab00698d747bf8669a3065c72ea68f7784)

commit 828df3995430ce89fe9be40c6c19c44fb6f14752
Author: John Spray <<EMAIL>>
Date:   Thu May 12 15:49:23 2016 +0100

    mon: fix 'mon metadata' for lone monitors
    
    Previously, writing to the store was only
    triggered when MMonMetadata was received
    from peers, so if you had a single mon then
    you would always get ENOENT from "mon metadata"
    
    Fixes: http://tracker.ceph.com/issues/15866
    Signed-off-by: John Spray <<EMAIL>>
    (cherry picked from commit 93ab00c77059e93b52ac4caa703e259944be1d1e)

commit 308f5143a9e9b121fb5c4df0a227ba30bda4636e
Author: runsisi <<EMAIL>>
Date:   Mon Jul 4 13:52:03 2016 +0800

    ceph.spec.in: fix rpm package building error as follows:
    
          error: Installed (but unpackaged) file(s) found:
          /usr/lib/udev/rules.d/60-ceph-by-parttypeuuid.rules
    
    Signed-off-by: runsisi <<EMAIL>>
    (cherry picked from commit 49dfad1f53d7abe0e9efc7817aaaf1735df5a9c9)

commit 727a704e79995c8a74f2b5768c63e90eecf9a883
Author: Loic Dachary <<EMAIL>>
Date:   Thu Jun 23 09:23:09 2016 +0200

    udev: always populate /dev/disk/by-parttypeuuid
    
    ceph-disk activate-all walks /dev/disk/by-parttypeuuid at boot time. It
    is not necessary when udev fires ADD event for each partition and
    95-ceph-osd.rules gets a chance to activate a ceph disk or journal.
    
    There are various reasons why udev ADD events may not be fired at
    boot (for instance Debian Jessi 8.5 never does it and CentOS 7.2 seems
    to be racy in that regard when a LVM root is being used).
    
    Populating /dev/disk/by-parttypeuuid fixes ceph-disk activate-all that
    would not work without it. And it guarantees disks are activated at boot
    time regardless of wether udev fires ADD events at the right time (or at
    all).
    
    The new udev file is a partial resurection of the
    60-ceph-partuuid-workaround-rules that was removed by
    9f77244b8e0782921663e52005b725cca58a8753. It is given a name that
    reflects its new purpose.
    
    Fixes http://tracker.ceph.com/issues/16351
    
    Signed-off-by: Loic Dachary <<EMAIL>>
    (cherry picked from commit 35004a628b2969d8b2f1c02155bb235165a1d809)

commit 9fc5b41598f09414572e89572e8980fd3d3c76e0
Author: Jason Dillaman <<EMAIL>>
Date:   Mon Jul 18 14:03:01 2016 -0400

    librbd: optimize away unnecessary object map updates
    
    Fixes: http://tracker.ceph.com/issues/16689
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit e5b4188635c4ee1ee0c4353cfc5ecd6e887d536b)

commit 81a2534742e5051fa92549029303cef3f3d187a6
Author: Jason Dillaman <<EMAIL>>
Date:   Mon Jul 18 13:37:37 2016 -0400

    rbd-replay: decode and replay discard IO operations
    
    Fixes: http://tracker.ceph.com/issues/16707
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit b7a4db213d0d4812aa6cd9c54e9646ff57d10411)

commit 5968664fd4ddf8e26989360ebbc048faa8431251
Author: Jason Dillaman <<EMAIL>>
Date:   Thu Jun 30 15:00:32 2016 -0400

    librbd: failed assertion after shrinking a clone image twice
    
    Fixes: http://tracker.ceph.com/issues/16561
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 34d2297eed040f694b4d1c5e2606af04752c9e1a)

commit 8316b6a6bad28343f8bade63f05b5fb93f4fe415
Author: Jason Dillaman <<EMAIL>>
Date:   Thu Jun 30 10:24:01 2016 -0400

    rbd-mirror: gracefully fail if object map is unavailable
    
    If the exclusive lock was lost due to a watch failure from an
    overloaded cluster, gracefully abort the image sync.
    
    Fixes: http://tracker.ceph.com/issues/16558
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 6f573ca2d63784e4a2b1e7a410044afdf58f6801)

commit aacb793948a69294e518f8458be6e670df23fe53
Merge: 954e978 5ae0e43
Author: Loic Dachary <<EMAIL>>
Date:   Tue Aug 9 17:09:42 2016 +0200

    Merge pull request #10625 from dachary/wip-16969-jewel
    
    jewel: src/script/subman fails with KeyError: 'nband'
    
    Reviewed-by: Kefu Chai <<EMAIL>>

commit 5ae0e43e7f75a3a6d96d9fc5f03c4b0a86c7481b
Author: Loic Dachary <<EMAIL>>
Date:   Tue Aug 9 09:13:17 2016 +0200

    subman: use replace instead of format
    
    Otherwise all {} are assumed to be substituted. Add a test.
    
    Fixes: http://tracker.ceph.com/issues/16961
    
    Signed-off-by: Loic Dachary <<EMAIL>>
    (cherry picked from commit b006c136761746e12704e349a672325b8b6e51f5)

commit 0ca27727173501856e9f17785b9f555684661709
Author: Ali Maredia <<EMAIL>>
Date:   Thu Apr 21 20:32:47 2016 -0400

    cmake: script that sets env vars for unit tests
    
    Signed-off-by: Ali Maredia <<EMAIL>>
    (cherry picked from commit 15a669435aaa78562746f6728bb185b1e99a4274)

commit 954e978bc8cd233ac149a2e490d8730d9782583a
Merge: d387bf4 cbc9636
Author: Loic Dachary <<EMAIL>>
Date:   Tue Aug 9 08:41:30 2016 +0200

    Merge pull request #10054 from dachary/wip-16484-jewel
    
    jewel: ExclusiveLock object leaked when switching to snapshot
    
    Reviewed-by: Mykola Golub <<EMAIL>>

commit d387bf44d405a3a8658697134e6bfc9ae077dfef
Merge: 14a4484 cf65ed9
Author: Loic Dachary <<EMAIL>>
Date:   Tue Aug 9 08:28:44 2016 +0200

    Merge pull request #10041 from dachary/wip-16315-jewel
    
    jewel: When journaling is enabled, a flush request shouldn't flush the cache
    
    Reviewed-by: Jason Dillaman <<EMAIL>>

commit 14a448466e9bda6b8d565f34b248a0ab6f9f90b3
Merge: fc39bd8 757babb
Author: Loic Dachary <<EMAIL>>
Date:   Tue Aug 9 08:27:50 2016 +0200

    Merge pull request #10055 from dachary/wip-16485-jewel
    
    jewel: Whitelist EBUSY error from snap unprotect for journal replay
    
    Reviewed-by: Mykola Golub <<EMAIL>>

commit fc39bd804e479fbaefa6f81befd899b8a1a82581
Merge: fc084a3 d9c3f28
Author: Loic Dachary <<EMAIL>>
Date:   Tue Aug 9 08:27:22 2016 +0200

    Merge pull request #10053 from dachary/wip-16483-jewel
    
    jewel: Close journal and object map before flagging exclusive lock as released
    
    Reviewed-by: Mykola Golub <<EMAIL>>

commit fc084a32ecf7c1868cbbbd808b273f2d095746f0
Merge: 543dd04 b1d9698
Author: Loic Dachary <<EMAIL>>
Date:   Tue Aug 9 08:27:08 2016 +0200

    Merge pull request #10052 from dachary/wip-16482-jewel
    
    jewel: Timeout sending mirroring notification shouldn't result in failure
    
    Reviewed-by: Mykola Golub <<EMAIL>>

commit 543dd0460228f778ca10e8ff238c8db3f1b239fe
Merge: 8fb4121 51defea
Author: Loic Dachary <<EMAIL>>
Date:   Tue Aug 9 08:26:50 2016 +0200

    Merge pull request #10051 from dachary/wip-16460-jewel
    
    jewel: Crash when utilizing advisory locking API functions
    
    Reviewed-by: Mykola Golub <<EMAIL>>

commit 8fb4121c53850f0f60eadb6e2a3bfc8c750e5935
Merge: 64d5ff9 db28ddc
Author: Loic Dachary <<EMAIL>>
Date:   Tue Aug 9 08:26:40 2016 +0200

    Merge pull request #10050 from dachary/wip-16459-jewel
    
    jewel: rbd-mirror should disable proxied maintenance ops for non-primary image
    
    Reviewed-by: Mykola Golub <<EMAIL>>

commit 64d5ff97ced5cc0cfa4f15b66b54cc2fb11b2c22
Merge: 4fe02a6 70bf746
Author: Loic Dachary <<EMAIL>>
Date:   Tue Aug 9 08:26:21 2016 +0200

    Merge pull request #10047 from dachary/wip-16426-jewel
    
    jewel: Possible race condition during journal transition from replay to ready
    
    Reviewed-by: Mykola Golub <<EMAIL>>

commit 4fe02a62b7ad2c6b4a32962741a73d8b1db9fb11
Merge: b86aec9 f3f4a4a
Author: Loic Dachary <<EMAIL>>
Date:   Tue Aug 9 08:26:11 2016 +0200

    Merge pull request #10046 from dachary/wip-16425-jewel
    
    jewel: rbd-mirror: potential race condition accessing local image journal
    
    Reviewed-by: Mykola Golub <<EMAIL>>

commit b86aec99e8375eb2de1d495338ab212e1880a0f7
Merge: 4a6e48e 0399958
Author: Loic Dachary <<EMAIL>>
Date:   Tue Aug 9 08:26:00 2016 +0200

    Merge pull request #10045 from dachary/wip-16424-jewel
    
    jewel: Journal needs to handle duplicate maintenance op tids
    
    Reviewed-by: Mykola Golub <<EMAIL>>

commit 4a6e48ed85914413b919e4da5a67ee4f95f34ae2
Merge: adcf5a7 6a28d63
Author: Loic Dachary <<EMAIL>>
Date:   Tue Aug 9 08:25:48 2016 +0200

    Merge pull request #10044 from dachary/wip-16423-jewel
    
    jewel: Journal duplicate op detection can cause lockdep error
    
    Reviewed-by: Mykola Golub <<EMAIL>>

commit adcf5a7fbf181505c6319c4399572cd3e9259a47
Merge: eab8994 1e85da9
Author: Loic Dachary <<EMAIL>>
Date:   Tue Aug 9 08:25:35 2016 +0200

    Merge pull request #10043 from dachary/wip-16371-jewel
    
    jewel: rbd-mirror: ensure replay status formatter has completed before stopping replay
    
    Reviewed-by: Mykola Golub <<EMAIL>>

commit eab89942c53ef5bff15cd837dea8ba42a228dc8f
Merge: 289c2d2 bf58eab
Author: Loic Dachary <<EMAIL>>
Date:   Tue Aug 9 08:25:11 2016 +0200

    Merge pull request #10042 from dachary/wip-16372-jewel
    
    jewel: Unable to disable journaling feature if in unexpected mirror state

commit 289c2d2a512723e039bc1465353dbb39084edb80
Merge: e01ebb9 dd635e4
Author: Loic Dachary <<EMAIL>>
Date:   Tue Aug 9 08:24:55 2016 +0200

    Merge pull request #10010 from dachary/wip-16486-jewel
    
    jewel: Object map/fast-diff invalidated if journal replays the same snap remove event
    
    Reviewed-by: Mykola Golub <<EMAIL>>

commit e01ebb9319d17fd71b83bd0ccf9cb0f231a1783e
Merge: 28575db db7ce96
Author: Loic Dachary <<EMAIL>>
Date:   Tue Aug 9 08:24:38 2016 +0200

    Merge pull request #10009 from dachary/wip-16514-jewel
    
    jewel: Image removal doesn't necessarily clean up all rbd_mirroring entries
    
    Reviewed-by: Mykola Golub <<EMAIL>>

commit 0ff096073f4c26bbccd9125a345372f090754d63
Author: Michel Normand <<EMAIL>>
Date:   Fri Aug 5 16:34:48 2016 +0200

    ExclusiveArch for suse_version
    
    for SLES supports only x86_64 and aarch64 targets
    for openSUSE (Tumbleweed and Leap) add ppc64/ppc64le targets.
    
    fixes: http://tracker.ceph.com/issues/16936
    Signed-off-by: Michel Normand <<EMAIL>>
    (cherry picked from commit a8db0f4d96d71223083bf6c2275acd892666a13e)

commit 28575db3fb1579cdfa85b14b0484363cc0634a2e
Merge: 3e36f8c 1f19dbd
Author: Loic Dachary <<EMAIL>>
Date:   Mon Aug 8 08:47:34 2016 +0200

    Merge pull request #9952 from dillaman/wip-fix-use-after-free-jewel
    
    jewel: librbd: potential use after free on refresh error
    
    Reviewed-by: Loic Dachary <<EMAIL>>

commit 3e36f8c66326d3de97a79859eab363e509dfc8c2
Merge: 5c98730 6b41d76
Author: Loic Dachary <<EMAIL>>
Date:   Mon Aug 8 08:45:58 2016 +0200

    Merge pull request #9752 from dillaman/wip-fix-task-finisher-jewel
    
    jewel: librbd: cancel all tasks should wait until finisher is done
    
    Reviewed-by: Loic Dachary <<EMAIL>>

commit 5c98730854f11b0efb3b3e03be426ce2b7a999af
Merge: f014619 7e1ba28
Author: David Zafman <<EMAIL>>
Date:   Fri Aug 5 12:46:10 2016 -0700

    Merge pull request #10561 from dzafman/wip-16672-jewel
    
    Reviewed-by: Samuel Just <<EMAIL>>

commit f0146196ccfbcfd923191f63d93e4e81219523b1
Merge: e400999 c025010
Author: Loic Dachary <<EMAIL>>
Date:   Fri Aug 5 15:30:52 2016 +0200

    Merge pull request #9562 from Abhishekvrshny/wip-16152-jewel
    
    jewel: client: fstat cap release
    
    Reviewed-by: John Spray <<EMAIL>>

commit e400999a2cb0972919e35dd8510f8d85f48ceace
Merge: 0cb5ca6 c3f6d82
Author: Loic Dachary <<EMAIL>>
Date:   Fri Aug 5 15:30:41 2016 +0200

    Merge pull request #9561 from Abhishekvrshny/wip-16136-jewel
    
    jewel: MDSMonitor fixes
    
    Reviewed-by: John Spray <<EMAIL>>

commit 0cb5ca6db018461c6275636f0d9cc1b1c8f56e53
Merge: 67f2eb0 19c1366
Author: Loic Dachary <<EMAIL>>
Date:   Fri Aug 5 15:29:33 2016 +0200

    Merge pull request #9557 from Abhishekvrshny/wip-15898-jewel
    
    jewel: Confusing MDS log message when shut down with stalled journaler reads
    
    Reviewed-by: John Spray <<EMAIL>>

commit 67f2eb01deff699016c16a312448d665cbef3922
Merge: 7ec15f6 eea0e91
Author: Loic Dachary <<EMAIL>>
Date:   Fri Aug 5 15:29:23 2016 +0200

    Merge pull request #9560 from Abhishekvrshny/wip-16135-jewel
    
    jewel: MDS: fix getattr starve setattr
    
    Reviewed-by: John Spray <<EMAIL>>

commit 7ec15f69f83e78ac862515c1068a64769caaff91
Merge: e51060a 2c7fc95
Author: Loic Dachary <<EMAIL>>
Date:   Fri Aug 5 15:29:08 2016 +0200

    Merge pull request #9559 from Abhishekvrshny/wip-16041-jewel
    
    jewel: mds/StrayManager.cc: 520: FAILED assert(dnl->is_primary())
    
    Reviewed-by: John Spray <<EMAIL>>

commit e51060ad8190bddf2b9b367c4d0a67e8cce7daef
Merge: fda98f7 edd3f79
Author: Loic Dachary <<EMAIL>>
Date:   Fri Aug 5 15:17:22 2016 +0200

    Merge pull request #9996 from dachary/wip-16437-jewel
    
    jewel: async messenger mon crash
    
    Reviewed-by: Samuel Just <<EMAIL>>
    Reviewed-by: Haomai Wang <<EMAIL>>

commit fda98f780134c26e491ce15fca0890dc240d67cf
Merge: 56bbcb1 18fdc1c
Author: Loic Dachary <<EMAIL>>
Date:   Fri Aug 5 15:10:27 2016 +0200

    Merge pull request #9997 from dachary/wip-16431-jewel
    
    jewel: librados,osd: bad flags can crash the osd
    
    Reviewed-by: Samuel Just <<EMAIL>>

commit 56bbcb1aa11a2beb951de396b0de9e3373d91c57
Merge: 7586d69 a826bb8
Author: Loic Dachary <<EMAIL>>
Date:   Fri Aug 5 15:10:08 2016 +0200

    Merge pull request #9998 from dachary/wip-16429-jewel
    
    jewel: OSDMonitor: drop pg temps from not the current primary
    
    Reviewed-by: Samuel Just <<EMAIL>>

commit 7586d693e1c4f08a17e60f89fda5abcb31d2f80d
Merge: 3b28428 6554d46
Author: Loic Dachary <<EMAIL>>
Date:   Fri Aug 5 15:09:52 2016 +0200

    Merge pull request #10001 from dachary/wip-16427-jewel
    
    jewel: prepare_pgtemp needs to only update up_thru if newer than the existing one
    
    Reviewed-by: Samuel Just <<EMAIL>>

commit 3b28428f901c1a6ed08b6c23b4f9991b87fe0960
Merge: 6996b76 762db30
Author: Casey Bodley <<EMAIL>>
Date:   Fri Aug 5 09:09:23 2016 -0400

    Merge pull request #9743 from vumrao/wip-vumrao-16339
    
    jewel : rgw: support size suffixes for --max-size in radosgw-admin command
    
    Reviewed-by: Casey Bodley <<EMAIL>>

commit 8b9954d0ccab9694c7519002209be8cb9dc03362
Author: Robin H. Johnson <<EMAIL>>
Date:   Thu Aug 4 17:15:18 2016 -0700

    rgw: Fix civetweb IPv6
    
    Commit c38e3cbb6f7c6221209f2b512cba18c564c895a4 introduced a second
    compiled version of src/civetweb/src/civetweb.c, but did not pass the
    configuration header (civetweb/include/civetweb_conf.h).
    
    As a result, USE_IPV6 was not defined when it was compiled, and that
    copy was included into the radosgw binary. This caused breakage for the
    civetweb frontend when used with IPv6:
      rgw frontends = civetweb port=[::]:7480
    
    Reintroduce the header so that civetweb is compiled correctly again.
    
    Fixes: http://tracker.ceph.com/issues/16928
    Backport: jewel
    Signed-off-by: Robin H. Johnson <<EMAIL>>
    (cherry picked from commit 921c556b65eafe2136f2afcc8234681711aa7348)

commit 6996b76d35b20a558d13d35fafaaad47fe331434
Merge: 8586e9b 74dd035
Author: Loic Dachary <<EMAIL>>
Date:   Thu Aug 4 14:45:05 2016 +0200

    Merge pull request #10007 from dachary/wip-15806-jewel
    
    jewel: New pools have bogus stuck inactive/unclean HEALTH_ERR messages until they are first active and clean
    
    Reviewed-by: xie xingguo <<EMAIL>>

commit 8586e9bd49d1fc083a7c37a2b1865744bbcce26a
Merge: e85b58b a2e8ae6
Author: Loic Dachary <<EMAIL>>
Date:   Thu Aug 4 14:43:36 2016 +0200

    Merge pull request #10006 from dachary/wip-16249-jewel
    
    jewel: sparse_read on ec pool should return extends with correct offset
    
    Reviewed-by: Kefu Chai <<EMAIL>>

commit e85b58bbe6aa217e4f9c5cef052aabedf728b9fe
Merge: f05eaae f8e4911
Author: Loic Dachary <<EMAIL>>
Date:   Thu Aug 4 14:43:22 2016 +0200

    Merge pull request #9740 from vumrao/wip-vumrao-16338
    
    jewel : rados: Add cleanup message with time to rados bench output
    
    Reviewed-by: Kefu Chai <<EMAIL>>

commit f05eaaea3f39159cc5e1a127a7d9913f479a7bf4
Merge: 093f30d b8f7aa2
Author: Loic Dachary <<EMAIL>>
Date:   Thu Aug 4 14:42:53 2016 +0200

    Merge pull request #10004 from dachary/wip-16374-jewel
    
    jewel: AsyncConnection::lockmsg/async lockdep cycle: AsyncMessenger::lock, MDSDaemon::mds_lock, AsyncConnection::lock
    
    Reviewed-by: Haomai Wang <<EMAIL>>

commit 093f30d1788b4a7225e3ab1c3345db14e40a5237
Merge: 648442b 3dbb08a
Author: Loic Dachary <<EMAIL>>
Date:   Thu Aug 4 14:42:32 2016 +0200

    Merge pull request #10003 from dachary/wip-16380-jewel
    
    jewel: msg/async: connection race hang
    
    Reviewed-by: Haomai Wang <<EMAIL>>

commit 648442bfea4ea72c04fad441fc933d03d980f80c
Merge: a35e2fa caad884
Author: Loic Dachary <<EMAIL>>
Date:   Thu Aug 4 12:35:14 2016 +0200

    Merge pull request #10036 from liewegas/wip-16297-jewel
    
    mon: Monitor: validate prefix on handle_command()
    
    Reviewed-by: Loic Dachary <<EMAIL>>

commit a35e2fa5f1a22ea5798c559196d50d1d38d7679e
Merge: e499d1c 7b0318a
Author: Loic Dachary <<EMAIL>>
Date:   Thu Aug 4 09:53:10 2016 +0200

    Merge pull request #9547 from jcsp/wip-jewel-15705
    
    jewel backport: mds: fix mdsmap print_summary with standby replays
    
    Reviewed-by: Loic Dachary <<EMAIL>>

commit 7e1ba28b1e218deb7d54ad6bef8853c9d19411a0
Author: David Zafman <<EMAIL>>
Date:   Tue Aug 2 22:32:02 2016 -0700

    os: Fix HashIndex::recursive_remove() to remove everything but original path
    
    Fixes: http://tracker.ceph.com/issues/16672
    
    Signed-off-by: David Zafman <<EMAIL>>
    (cherry picked from commit ba88a3aac0d3b620986e32ed718237513d6592f6)

commit cb1f17f05422ac802378df0de698ce20a1f2976e
Author: David Zafman <<EMAIL>>
Date:   Tue Aug 2 23:26:41 2016 -0700

    filestore: Clear objects before calling collection_list() again
    
    Signed-off-by: David Zafman <<EMAIL>>
    (cherry picked from commit 892303cd02b64b622669183c2acffcdd7a8f3547)

commit bdcfcaf821add71c518b01d6fb3dccb7c2e074a7
Author: David Zafman <<EMAIL>>
Date:   Tue Aug 2 23:24:12 2016 -0700

    filestore: Improve logging
    
    Signed-off-by: David Zafman <<EMAIL>>
    (cherry picked from commit b78c457785553e3a6e4f901bfebb41fc9c4d9bd5)

commit e499d1c8fb216b490a739f2712b3c733623d45d1
Merge: 269d742 8981f3b
Author: Casey Bodley <<EMAIL>>
Date:   Tue Aug 2 10:28:00 2016 -0400

    Merge pull request #10293 from theanalyst/wip-16589-jewel
    
    jewel: multisite sync races with deletes
    
    Reviewed-by: Casey Bodley <<EMAIL>>
    Reviewed-by: Yehuda Sadeh <<EMAIL>>

commit 269d7427dfa9de7b1a288da0532505810bdf283b
Merge: 894a5f8 e1eb8af
Author: John Spray <<EMAIL>>
Date:   Tue Aug 2 12:31:18 2016 +0100

    Merge pull request #10453 from ajarr/wip-manila-backports-jewel
    
    jewel: essential backports for OpenStack Manila
    
    Reviewed-by: John Spray <<EMAIL>>

commit e1eb8afea9f202947eef33e8361a0aac0e955eea
Author: Ramana Raja <<EMAIL>>
Date:   Thu Jul 7 17:15:13 2016 +0530

    ceph_volume_client: version on-disk metadata
    
    Version on-disk metadata with two attributes,
    'compat version', the minimum CephFSVolume Client
    version that can decode the metadata, and
    'version', the version that encoded the metadata.
    
    Signed-off-by: Ramana Raja <<EMAIL>>
    (cherry picked from commit 1c1d65a45f4574ca5f33cc9d949089a5c956e363)

commit e8dd1eeec7ee2fd11a2985843825a3ec0f522b0c
Author: Ramana Raja <<EMAIL>>
Date:   Wed Jul 6 15:33:06 2016 +0530

    ceph_volume_client: add versioning
    
    Add class attributes to CephFSVolumeClient to version
    its capabilities.
    
    'version' attribute stores the current version number
    of CephFSVolumeClient.
    
    'compat_version' attribute stores the earliest version
    number of CephFSVolumeClient that the current version is
    compatible with.
    
    Fixes: http://tracker.ceph.com/issues/15406
    
    Signed-off-by: Ramana Raja <<EMAIL>>
    (cherry picked from commit 46876fb2ceb22082c0a1703fe77ad1694b508ad8)

commit a0ffc859f5763850342a165ec4c49b3b1e83bb95
Author: Ramana Raja <<EMAIL>>
Date:   Thu Jun 23 23:22:12 2016 +0530

    ceph_volume_client: disallow tenants to share auth IDs
    
    Restrict an auth ID to a single OpenStack tenant to enforce
    strong tenant isolation of shares.
    
    Signed-off-by: Ramana Raja <<EMAIL>>
    (cherry picked from commit 82445a20a258a4c8800f273dc5f2484aace0e413)

commit d3e22db9841404d6c305012032427d6bb5311253
Author: Ramana Raja <<EMAIL>>
Date:   Thu Jun 23 17:11:33 2016 +0530

    ceph_volume_client: cleanup auth meta files
    
    Remove auth meta files on last rule for an auth ID deletion
    
    Signed-off-by: Ramana Raja <<EMAIL>>
    (cherry picked from commit ec2e6e37d01c961d269d83661d0b95ada6a8449e)

commit 2e9c37adfb7b9f7e67801ffc2e1fa6c85674c9c1
Author: Ramana Raja <<EMAIL>>
Date:   Thu Jun 23 16:55:30 2016 +0530

    ceph_volume_client: fix log messages
    
    Log the path of the volume during creation and deletion of volumes.
    
    Signed-off-by: Ramana Raja <<EMAIL>>
    (cherry picked from commit 7731287761f91e1fdee0e6306d9ecf9b04ad363c)

commit 7e93d3d0e631f375a496673174667934d5ecc08e
Author: Ramana Raja <<EMAIL>>
Date:   Thu Jun 23 16:31:23 2016 +0530

    ceph_volume_client: create/delete VMeta for create/delete volume
    
    Create and delete volume meta files during creation and deletion of
    volumes.
    
    Signed-off-by: Ramana Raja <<EMAIL>>
    (cherry picked from commit 37fbfc7aa8f10d37f5202603a906425507271ff5)

commit 7cea0eee455c83fbdd64fff192c6aa10235a2c40
Author: Ramana Raja <<EMAIL>>
Date:   Thu Jun 23 16:06:53 2016 +0530

    ceph_volume_client: modify locking of meta files
    
    File locks are applied on meta files before updating the meta
    file contents. These meta files would need to be cleaned up
    sometime, which could lead to locks being held on unlinked meta
    files. Prevent this by checking whether the file had been deleted
    after lock was acquired on it.
    
    Signed-off-by: Ramana Raja <<EMAIL>>
    (cherry picked from commit f7c037229bcf3f5a3d06897ec7fe0c5419dd7143)

commit e77684fc68cfd3dfb33a80e7c5604356921a40b0
Author: Ramana Raja <<EMAIL>>
Date:   Thu Jun 23 17:39:32 2016 +0530

    cephfs.pyx: implement python bindings for fstat
    
    Signed-off-by: Ramana Raja <<EMAIL>>
    (cherry picked from commit f58403f3d19e22edeb8f91b6f87a0b7947b0ff21)

commit d2792ef1babd452d76528ce5ca511106d8c836b4
Author: Ramana Raja <<EMAIL>>
Date:   Wed Jun 8 16:57:01 2016 +0530

    ceph_volume_client: restrict volume group names
    
    Prevent craftily-named volume groups from colliding with meta files.
    
    Signed-off-by: Ramana Raja <<EMAIL>>
    (cherry picked from commit 7f7d2a76ae9b556c1de418f0eab8461c538f91d9)

commit 4f874dd9a9c4ce58f623c635c31bb67012a72199
Author: Ramana Raja <<EMAIL>>
Date:   Wed Jun 8 16:34:56 2016 +0530

    ceph_volume_client: use fsync instead of syncfs
    
    Signed-off-by: Ramana Raja <<EMAIL>>
    (cherry picked from commit 27eb51baab4cda6b385aef53fc7c3962a9debae5)

commit bde2c8f111d8276b026a3b1e37a5c9081a498ec9
Author: Xiaoxi Chen <<EMAIL>>
Date:   Fri Apr 29 14:47:42 2016 -0500

    pybind: expose fsync in cephfs binding.
    
    So we don't necessary to syncfs when want to persistent
    some file.
    
    Signed-off-by: Xiaoxi Chen <<EMAIL>>
    (cherry picked from commit 1c952fbaf0fd393ef2dcb83a3db721a077b4274e)

commit 0c13bf2d028eab4cd062209be7ed985cdf28960c
Author: Ramana Raja <<EMAIL>>
Date:   Wed Jun 8 00:42:18 2016 +0530

    ceph_volume_client: recover from dirty auth and auth meta updates
    
    Check dirty flag after locking something and call recover() if we are
    opening something dirty (racing with another instance of the driver
    restarting after failure) -- only required if someone running multiple
    manila-share instances with Ceph loaded.
    
    Signed-off-by: Ramana Raja <<EMAIL>>
    (cherry picked from commit 647a2447f0c4354dc21d1083043591d2b6f6f94f)

commit 8f7defb5d084f8e781fa44e9c21edf6c472cfb4a
Author: Ramana Raja <<EMAIL>>
Date:   Tue Jun 21 12:14:56 2016 +0530

    ceph_volume_client: modify data layout in meta files
    
    Notable changes to data layout in auth meta and volume meta files:
    
    In the auth meta files, add a 'dirty' flag to track the status of auth
    updates to a single volume.
    
    In the volume meta file, make the 'dirty' flag track the status of
    auth updates for a single ID.
    
    Optimize the recovery of partial auth update changes to auth meta,
    volume meta, and the Ceph backend, facilitated by changes in the
    data layout in the meta files.
    
    Signed-off-by: Ramana Raja <<EMAIL>>
    (cherry picked from commit 7c8a28a7e817d030a4d419b0cc627c30c1064270)

commit 748a5a08ecc1e212e4bd10e0c8eff0a805fc931f
Author: John Spray <<EMAIL>>
Date:   Wed Mar 2 12:30:45 2016 +0000

    pybind: ceph_volume_client authentication metadata
    
    Store a two-way mapping between auth IDs and volumes.
    
    Enables us to record some metadata on auth ids (which
    openstack tenant created it) so that we can avoid exposing
    keys to other tenants who try to use the same ceph
    auth id.
    
    Enables us to expose the list of which auth ids have access
    to a volume, so that Manila's update_access() can be
    implemented efficiently.
    
    DNM: see TODOs inline.
    
    Fixes: http://tracker.ceph.com/issues/15615
    
    Signed-off-by: John Spray <<EMAIL>>
    (cherry picked from commit d2e9eb55ca6ed5daa094cf323faf143615b9380b)

commit e14dc25e1fca7fd74971cbda74d1fcadff5be3b4
Author: John Spray <<EMAIL>>
Date:   Mon Mar 7 13:06:41 2016 +0000

    pybind: enable integer flags to libcephfs open
    
    The 'rw+' style flags are handy and convenient, but
    they don't capture all possibilities.  Change to
    optionally accept an integer here for advance users
    who want to specify arbitrary combinations of
    flags.
    
    Signed-off-by: John Spray <<EMAIL>>
    (cherry picked from commit 5678584f4176d07301acd7f57acc4efd7fb20e43)

commit c6546bb29b25fb96f270257b959426f949eb9418
Author: Noah Watkins <<EMAIL>>
Date:   Wed Mar 16 14:12:05 2016 -0700

    buffer: fix iterator_impl visibility through typedef
    
    The following program doesn't compile because of symbol visibility issues.
    While bufferlist::iterator is a class implementation with visibility specified,
    it is unclear after google-fu how to do the same through typedef.
    
    int main()
    {
     ceph::bufferlist bl;
     ceph::bufferlist::const_iterator it = bl.begin();
     (void)it;
     return 0;
    }
    
    [nwatkins@bender ~]$ g++ -Wall -std=c++11 -Iinstall/include -Linstall/lib -o test test.cc -lrados
    /tmp/cciR9MUj.o: In function `main':
    test.cc:(.text+0x43): undefined reference to `ceph::buffer::list::iterator_impl<true>::iterator_impl(ceph::buffer::list::iterator const&)'
    /usr/bin/ld: test: hidden symbol `_ZN4ceph6buffer4list13iterator_implILb1EEC1ERKNS1_8iteratorE' isn't defined
    /usr/bin/ld: final link failed: Bad value
    collect2: error: ld returned 1 exit status
    
    Signed-off-by: Noah Watkins <<EMAIL>>
    (cherry picked from commit 16bc3e59325d4057d36cd63a57111ab36fbc50fd)

commit e825dd2f3fc9048995062bfe96e32e6df7ef37d9
Author: Abhishek Lekshmanan <<EMAIL>>
Date:   Fri Jul 22 10:57:45 2016 +0200

    rgw multisite: preserve zone's extra pool
    
    In current multisite scenarios,if a bucket is created in master, we end
    up storing multipart metadata in `$source-zone.rgw.buckets.non-ec` pool
    instead of the zone's own non-ec pool, so we end up additionally
    creating this pool and storing multipart metadata entries in it. Also if
    a bucket is created in a secondary zone, and we initiate a multipart
    upload, before mdlog sync with master, we end up getting errors during
    complete multipart requests as omap entries are partly stored in the
    `$zone.rgw.buckets.non-ec` as well as `$source-zone.rgw.buckets.non-ec`
    pools which leads to total number of parts mismatch.
    
    Fixes: http://tracker.ceph.com/issues/16712
    
    Signed-off-by: Abhishek Lekshmanan <<EMAIL>>
    (cherry picked from commit d4ecc956f603a567bd4043c2be39b3e7e3402c75)

commit 059ed62ff937ce63b7255d72bf9b3cf687c4b532
Author: Orit Wasserman <<EMAIL>>
Date:   Mon Jul 4 15:01:51 2016 +0200

    rgw: remove bucket index objects when deleting the bucket
    
    Fixes: http://tracker.ceph.com/issues/16412
    Signed-off-by: Orit Wasserman <<EMAIL>>
    (cherry picked from commit 3ae276390641ad5fc4fef0c03971db95948880b4)

commit 93d787557939d77c6a15854520a42f3d0f358370
Author: Orit Wasserman <<EMAIL>>
Date:   Fri Jul 8 10:41:59 2016 +0200

    rgw: add missing master_zone when running with old default region config
    
    Fixes: http://tracker.ceph.com/issues/16627
    
    Signed-off-by: Orit Wasserman <<EMAIL>>
    (cherry picked from commit a0420741d446341a4979d78db7e2e58f396fdc4b)

commit dc963833af2d81984c7fd023f718f5b7e58a37dc
Author: Casey Bodley <<EMAIL>>
Date:   Wed Jul 6 09:15:29 2016 -0400

    rgw: fix error_repo segfault in data sync
    
    RGWDataSyncShardCR will only allocate an error_repo if it's doing
    incremental sync, so RGWDataSyncSingleEntryCR needs to guard against a
    null error_repo
    
    also, RGWDataSyncShardCR::stop_spawned_services() was dropping the last
    reference to the error_repo before calling drain_all(), which meant that
    RGWDataSyncSingleEntryCR could still be holding a pointer. now uses a
    boost::intrusive_ptr in RGWDataSyncSingleEntryCR to account for its
    reference
    
    Fixes: http://tracker.ceph.com/issues/16603
    
    Signed-off-by: Casey Bodley <<EMAIL>>
    (cherry picked from commit 28609029cf1be2fc9f8c8e3f47320636db29014a)

commit 67eb961927664cbded6b01c5f76bfd9db8481f8c
Author: Matt Benjamin <<EMAIL>>
Date:   Mon Jan 18 16:06:15 2016 -0500

    rgw: add line space between inl. member function defns (rgw_user.h)
    
    This is a cosmetic change only.
    
    Signed-off-by: Matt Benjamin <<EMAIL>>
    (cherry picked from commit 5a7f7f5560e57f8dc64a3c84c1e668be834d81e0)

commit 3f8298a049e255f542a012688b4148456d465d8d
Author: Matt Benjamin <<EMAIL>>
Date:   Mon Jan 18 15:58:07 2016 -0500

    rgw-admin: return error on email address conflict (add user)
    
    Fixes the error return inconsistency documented in upstream
    Ceph issue #13598
    
    Revised after upstream review, permits changing email address as
    originally (but use case-insensitive comp).
    
    Signed-off-by: Matt Benjamin <<EMAIL>>
    (cherry picked from commit 90c143efa2156e55dc017ebe81005315cacaf2c5)

commit 962e7dc47dcaa64f4774a197ae21d0ee59f7854f
Author: Matt Benjamin <<EMAIL>>
Date:   Mon Jan 18 15:06:19 2016 -0500

    rgw-admin: convert user email addresses to lower case
    
    Fixes the email address inconsistency documented in upstream
    Ceph BUG #13598
    
    Signed-off-by: Matt Benjamin <<EMAIL>>
    (cherry picked from commit 4c438dbbc0e6eda6b9a3018d60019a1a780d6f65)

commit 461782ee6b755b14f6ae90ea56ebcb656e305b94
Author: Boris Ranto <<EMAIL>>
Date:   Mon Jun 13 12:34:39 2016 +0200

    selinux: allow chown for self and setattr for /var/run/ceph
    
    Fixes: http://tracker.ceph.com/issues/16126
    
    Signed-off-by: Boris Ranto <<EMAIL>>
    (cherry picked from commit 2a6c738abda35f540af6f9398406d4e49337c34d)

commit fe57aceeb02ad9163feb2d196589b5927cedfa0f
Author: Matt Benjamin <<EMAIL>>
Date:   Mon Jun 6 16:19:17 2016 -0400

    rgw ldap: fix ldap bindpw parsing
    
    Also add additional LDAP debugging output at 0, 10, and 15 to make
    troubleshooting easier.
    
    Fixes DN search issue using QE configuration of MS AD.
    
    Signed-off-by: Matt Benjamin <<EMAIL>>
    (cherry picked from commit 72b2f3e8dcb879be3dac7ac03142fd4a13ff28ac)

commit 429e9c03f3f47e3804f54cac380db0add18584f2
Author: Javier M. Mellid <<EMAIL>>
Date:   Tue Jun 14 11:04:23 2016 +0200

    rgw: aws4: fix buffer sharing issue with chunked uploads
    
    Signed-off-by: Javier M. Mellid <<EMAIL>>
    (cherry picked from commit dd9f53115a452e1e4268a1036f8fb69f9c1b86f7)

commit 7c2eab19e2c088e97de924f7f1a633a9f291439d
Author: Patrick Donnelly <<EMAIL>>
Date:   Fri Jul 1 21:06:17 2016 -0400

    mds: move Finisher to unlocked shutdown
    
    This commit resolves a deadlock reported in i16042 where the thread calling
    MDSRankDispatcher::shutdown would hold the mds_lock while asynchronous
    callbacks in the Finisher would attempt to lock mds_lock.
    
    For simplicity, I merged the finisher stop with the messenger shutdown as both
    need the mds_lock dropped.
    
    Fixes: http://tracker.ceph.com/issues/16042
    
    Signed-off-by: Patrick Donnelly <<EMAIL>>
    (cherry picked from commit b1d6689b6cae579d5f1b2047fd3c653cda422454)
    
    Conflicts:
            src/mds/MDSRank.cc
                    trivial conflict in MDSRankDispatcher::shutdown()

commit 28697fc5101395be2a1bd778445983e61a2dc089
Author: Douglas Fuller <<EMAIL>>
Date:   Mon Jun 27 12:30:11 2016 -0700

    mds: Kill C_SaferCond in evict_sessions()
    
    MDSRankDispatcher::evict_sessions waits on a C_SaferCond for
    kill_session to complete on each of its victims. Change the
    command handling flow to pass command messages all the way down
    to MDSRankDispatcher. Extract the MDSDaemon's reply path into a
    static function callable from a new context in the MDSRankDispatcher.
    
    See: http://tracker.ceph.com/issues/16288
    Signed-off-by: Douglas Fuller <<EMAIL>>
    (cherry picked from commit 3a4d63ee67765010a8e53af5a89aef4f49fafd56)

commit 1d3a816814c6cb87f645f205362de800cc84d484
Author: Zhi Zhang <<EMAIL>>
Date:   Mon Jun 27 13:00:26 2016 +0800

    mds: fix shutting down mds timed-out due to deadlock
    
    Signed-off-by: Zhi Zhang <<EMAIL>>
    (cherry picked from commit ca069149654ecd10778cd5327bf59b9643e23967)

commit 49a1ce936d76b4bbf7a3b46be300ad5ebb6d301e
Author: Zhi Zhang <<EMAIL>>
Date:   Mon Jun 27 12:59:08 2016 +0800

    msg/async: remove the unnecessary checking to wakup event_wait
    
    Signed-off-by: Zhi Zhang <<EMAIL>>
    (cherry picked from commit 560dc32771be00664c4b22a0ebca5190cd246e0a)

commit 2beb56a10a0319b6d061caf1da769bb3c9f5f90b
Author: Yan, Zheng <<EMAIL>>
Date:   Thu Jun 2 17:03:05 2016 +0800

    mds: fix SnapRealm::have_past_parents_open()
    
    the '!' got delete accidentally in commit f7fb2cb52c (mds: fix open
    snap parents tracking)
    
    Signed-off-by: Yan, Zheng <<EMAIL>>
    (cherry picked from commit f28f72635c7d11f0db4f156a6d108a480295056d)

commit 3d6d36a12bd4823352dc58e2135d03f261d18dbe
Author: Loic Dachary <<EMAIL>>
Date:   Thu May 26 09:38:47 2016 +0200

    ceph-disk: partprobe should block udev induced BLKRRPART
    
    Wrap partprobe with flock to stop udev from issuing BLKRRPART because
    this is racy and frequently fails with a message like:
    
        Error: Error informing the kernel about modifications to partition
        /dev/vdc1 -- Device or resource busy.  This means Linux won't know about
        any changes you made to /dev/vdc1 until you reboot -- so you shouldn't
        mount it or use it in any way before rebooting.
    
    Opening a device (/dev/vdc for instance) in write mode indirectly
    triggers a BLKRRPART ioctl from udev (starting version 214 and up)
    when the device is closed (see below for the udev release note).
    
    However, if udev fails to acquire an exclusive lock (with
    flock(fd, LOCK_EX|LOCK_NB); ) the BLKRRPART ioctl is not issued.
    
    https://github.com/systemd/systemd/blob/045e00cf16c47bc516c0823d059b7548f3ce9c7c/src/udev/udevd.c#L1042
    
    Acquiring an exclusive lock before running the process that opens the
    device in write mode is therefore an effective way to control this
    behavior.
    
    git clone git://anonscm.debian.org/pkg-systemd/systemd.git
    systemd/NEWS:
    CHANGES WITH 214:
    
      * As an experimental feature, udev now tries to lock the
           disk device node (flock(LOCK_SH|LOCK_NB)) while it
           executes events for the disk or any of its partitions.
           Applications like partitioning programs can lock the
           disk device node (flock(LOCK_EX)) and claim temporary
           device ownership that way; udev will entirely skip all event
           handling for this disk and its partitions. If the disk
           was opened for writing, the close will trigger a partition
           table rescan in udev's "watch" facility, and if needed
           synthesize "change" events for the disk and all its partitions.
           This is now unconditionally enabled, and if it turns out to
           cause major problems, we might turn it on only for specific
           devices, or might need to disable it entirely. Device Mapper
           devices are excluded from this logic.
    
    Fixes: http://tracker.ceph.com/issues/15176
    
    Signed-off-by: Marius Vollmer <marius.vollmer@redhat com>
    Signed-off-by: Loic Dachary <<EMAIL>>
    (cherry picked from commit 8519481b72365701d01ee58a0ef57ad1bea2c66c)

commit 8cd7f44d8066a796cccd872dd2593582ac061331
Author: Javier M. Mellid <<EMAIL>>
Date:   Fri Jun 3 17:34:10 2016 +0200

    rgw: aws4: add STREAMING-AWS4-HMAC-SHA256-PAYLOAD support
    
    When authenticating requests using the Authorization header in AWS4, you have
    the option of uploading the payload in chunks. You can send data in fixed size
    or variable size chunks.
    
    This patch enables streaming mode and signed headers support with chunked
    uploads.
    
    Fixes: http://tracker.ceph.com/issues/16146
    
    Signed-off-by: Javier M. Mellid <<EMAIL>>
    (cherry picked from commit 5de5876a535537f7878615898bb9cf7887204cb1)
    
    Conflicts:
            src/rgw/rgw_rest_s3.cc
    
    No change required to resolve the conflict. Manual merge was enough.

commit 276ec72e85d6650782aef2c96f4b1fbc47918f42
Author: Radoslaw Zarzynski <<EMAIL>>
Date:   Fri Mar 11 17:34:33 2016 +0100

    rgw: use std::unique_ptr for rgw_aws4_auth management.
    
    This is purely for code maintainability.
    
    Signed-off-by: Radoslaw Zarzynski <<EMAIL>>
    (cherry picked from commit 6f273eb52c07b27c7aa2d69be3e5ff5d6578558c)

commit 2c422e306989f65d9112f95a131ff29479b2d0f0
Author: Radoslaw Zarzynski <<EMAIL>>
Date:   Fri Mar 11 17:33:47 2016 +0100

    rgw: add handling of memory allocation failure in AWS4 auth.
    
    Signed-off-by: Radoslaw Zarzynski <<EMAIL>>
    (cherry picked from commit ddbb0ab9d5046672795ec876221de9ebd466364f)

commit 2cd3ed8a59786be28b55a0983a438af1aab226ea
Author: Ramana Raja <<EMAIL>>
Date:   Wed Apr 27 23:26:44 2016 +0530

    ceph_volume_client: allow read-only authorization for volumes
    
    Allow clients to be restricted to read-only mount of the volume
    by restricting their ceph auth ID's MDS and OSD caps to read-only.
    
    Fixes: http://tracker.ceph.com/issues/15614
    
    Signed-off-by: Ramana Raja <<EMAIL>>
    (cherry picked from commit 011ea5e7fb35ee07848e0c3abac24702a778ad63)

commit 46246e39b8cadc14aede930559eddd09059301fc
Author: Kefu Chai <<EMAIL>>
Date:   Wed Jul 6 19:41:54 2016 +0800

    osd: increment stas on recovery pull also
    
    PGMap::recovery_rate_summary() summaries the recovery progress from
    a pool's
    pool_stat_t.stats.sum.num_{objects_recovered,bytes_recovered,keys_recovered},
    now we only increment stats on completion of recovery push, but there
    are chances that a PG recovers by pulling data from replicas to primary.
    in that case, teuthology will erroneously consider recovery hung: a zero
    recovering_keys_per_sec or recovering_bytes_per_sec or
    recovering_objects_per_sec. so we should increment stats on pull as
    well.
    
    Fixes: http://tracker.ceph.com/issues/16277
    Signed-off-by: Kefu Chai <<EMAIL>>
    (cherry picked from commit 9c6c4d8fef67380e2a6d93f6c101744c926d8b9b)

commit 3da251fe7f3bafa9a936df209b1906824764e5f6
Author: Kefu Chai <<EMAIL>>
Date:   Fri Jul 22 15:55:16 2016 +0800

    pybind/ceph_argparse: handle non ascii unicode args
    
    we raise UnicodeDecodeError at seeing non-ascii args if we fail to match
    it with any command signatures. instead, we should use a unicode string
    for representing the error in that case. please note, the exception is
    not printed at all in real-world. =)
    
    Fixes: http://tracker.ceph.com/issues/12287
    Signed-off-by: Kefu Chai <<EMAIL>>
    (cherry picked from commit 5864626d275c423cdb8d3e52a91fe4cc6b8e6f90)

commit b01af21ceeb83e3b2fb7cee608f2a9229e6eed7e
Author: Oleh Prypin <<EMAIL>>
Date:   Wed Jun 15 00:50:20 2016 +0300

    Fix tabs->whitespace in ceph_argparse
    
    Signed-off-by: Oleh Prypin <<EMAIL>>
    (cherry picked from commit bef2c83c1da7dcc32fc2ff98cf214fc93bef75b8)

commit bb2dc9537612b56a79f1237618126faecbeccc9e
Author: Oleh Prypin <<EMAIL>>
Date:   Wed Jun 15 00:54:08 2016 +0300

    Make usage of builtins in ceph_argparse compatible with Python 3
    
    Signed-off-by: Oleh Prypin <<EMAIL>>
    (cherry picked from commit ddf06041ef80ac9606da8d57f048d2e23233d122)

commit f80d10c82034fcd9d53b3890afe62a4dbfeb1799
Author: Nathan Cutler <<EMAIL>>
Date:   Wed Jul 20 10:23:48 2016 +0200

    rpm: move libatomic_ops-devel to non-distro-specific section
    
    Signed-off-by: Nathan Cutler <<EMAIL>>
    (cherry picked from commit 94e4f2190b6ab3ff7b57b0b6095fea6d9228d917)

commit a28810c69b356d1865d1ce9e989bcc9e8a66017a
Author: Nathan Cutler <<EMAIL>>
Date:   Wed Jul 20 10:22:37 2016 +0200

    rpm: move gperftools-devel to non-distro-specific section
    
    Signed-off-by: Nathan Cutler <<EMAIL>>
    (cherry picked from commit 06940e29b702c7437dc9f8df365fcc383cb882e1)

commit e6b7a4ba8a1a8ea13d6f7178bd69432a3705754b
Author: Nathan Cutler <<EMAIL>>
Date:   Sat Jul 9 22:55:54 2016 +0200

    rpm: use new name of libatomic_ops-devel
    
    The previous form, libatomic-ops-devel, has been deprecated since May 23, 2012.
    
    Signed-off-by: Nathan Cutler <<EMAIL>>
    (cherry picked from commit c835f6ddf5839c418d29db1c2f46da32fbf05d2c)

commit 9bbf2e8d874201460c3cf1dfd14cfd17752d397d
Author: Dan Horák <<EMAIL>>
Date:   Fri May 6 13:29:03 2016 +0200

    fix tcmalloc handling in spec file
    
    - there is no gperftools/tcmalloc on s390(x) yet
    - explicitly disable tcmalloc when built without
    
    Signed-off-by: Dan Horák <<EMAIL>>
    (cherry picked from commit efa7f7b365d27797573bf4e5a9878f94f41aede2)

commit 894a5f8d878d4b267f80b90a4bffce157f2b4ba7
Merge: b15cf42 2538b77
Author: John Spray <<EMAIL>>
Date:   Thu Jul 21 13:58:16 2016 +0100

    Merge pull request #10373 from ceph/jewel-mds-snap-failover
    
    Jewel mds snap failover
    
    Reviewed-by: John Spray <<EMAIL>>

commit b26acc03ab42ba7eacb804ef8dc08c03a6e64e2d
Author: Nathan Cutler <<EMAIL>>
Date:   Mon Jun 13 10:27:18 2016 +0200

    ceph-osd-prestart.sh: drop Upstart-specific code
    
    Before this patch, the ceph-osd-prestart.sh script would do Upstart-specific
    things if the journal device failed to appear after a 5-second timeout.
    
    Fixes: http://tracker.ceph.com/issues/15984
    Signed-off-by: Nathan Cutler <<EMAIL>>
    (cherry picked from commit 47a06e608b7f686252d42c5a879373d8ebb65d9d)

commit 1e622a5d3b79a89a6affb56f282e1305c48ec835
Author: Ricardo Dias <<EMAIL>>
Date:   Tue Jul 19 15:40:36 2016 +0100

    rpm: Fix creation of mount.ceph symbolic link for SUSE distros
    
    Signed-off-by: Ricardo Dias <<EMAIL>>
    (cherry picked from commit 976c9d4f31793712b2af334e4a7a58745af8c099)

commit 89cb116ca14abf5764edf07373d045bb127ca7eb
Author: Nathan Cutler <<EMAIL>>
Date:   Mon Jul 18 19:09:54 2016 +0200

    build/ops: build mount.ceph and mount.fuse.ceph as client binaries
    
    Signed-off-by: Nathan Cutler <<EMAIL>>
    (cherry picked from commit b2675ff82445cda9873292ac2be8186e054098ed)

commit 84b45b75c47e6234aac9d3ee1cf1c6844b898ae9
Author: Nathan Cutler <<EMAIL>>
Date:   Mon Jul 18 16:16:11 2016 +0200

    rpm: move mount.ceph from ceph-base to ceph-common
    
    Ceph clients use mount.ceph to mount CephFS filesystems, and
    ceph-base is not expected to be installed on client systems.
    
    Signed-off-by: Nathan Cutler <<EMAIL>>
    (cherry picked from commit b8c24bf2f8c6a6b125778ca92d68d8e64ccf9ae0)

commit e028cd140840b15a8f99962dcf8ec646da3f1f60
Author: Nathan Cutler <<EMAIL>>
Date:   Wed Jul 6 10:13:03 2016 +0200

    rpm: create mount.ceph symlink in /sbin (SUSE only)
    
    Fixes: http://tracker.ceph.com/issues/16598
    Signed-off-by: Nathan Cutler <<EMAIL>>
    (cherry picked from commit b090e9da326a72068260682599c886c2d11f06b3)

commit ad67d1c15f49233fe1108258d9b968c8b2301330
Author: Kefu Chai <<EMAIL>>
Date:   Thu Jun 2 02:08:45 2016 +0800

    makefile: install mount.fuse.ceph,mount.ceph into /usr/sbin
    
    Signed-off-by: Kefu Chai <<EMAIL>>
    (cherry picked from commit 52021ff9f0b23c36a012f19c7388dcfb2281b523)
    
    Conflicts:
            src/CMakeLists.txt (Install mount.ceph to ${CMAKE_INSTALL_SBINDIR}
            instead of sbin; install mount.fuse.ceph to ${CMAKE_INSTALL_SBINDIR} as
            well)

commit 2538b77a2f219a76c19289ab3e675927f6914149
Author: Yan, Zheng <<EMAIL>>
Date:   Mon Jul 11 15:32:41 2016 +0800

    client: fix MetaRequest::set_other_inode()
    
    Signed-off-by: Yan, Zheng <<EMAIL>>
    (cherry picked from commit 265f96bda7e106883063f074133450ce39ee262c)

commit ffcfe69ef7abaaa3c663de86fb04ea986dc90b15
Author: Yan, Zheng <<EMAIL>>
Date:   Mon Jul 11 11:25:37 2016 +0800

    client: close directory's snapdir when deleting directory
    
    Signed-off-by: Yan, Zheng <<EMAIL>>
    (cherry picked from commit 3099cabd11a16d22c6707631861bef0aa176ed02)

commit b9007025f3dd49532a45267d7478dcdc5308ce3a
Author: Yan, Zheng <<EMAIL>>
Date:   Mon Jul 11 11:07:01 2016 +0800

    client: invalidate snap inodes after removing snapshot
    
    Signed-off-by: Yan, Zheng <<EMAIL>>
    (cherry picked from commit f180ad149ab510626fcd6cbd8221f550f9858126)

commit d9f957a5cda55adcdba5db28689aeb344d10e413
Author: Yan, Zheng <<EMAIL>>
Date:   Mon Jul 11 10:51:13 2016 +0800

    mds: fix incorrect "unconnected snaprealm xxx" warning
    
    If a snaprealm has no child/parent snaprelam, and the snaprealm inode
    is not in the cache while client reconnects. The snaprealm does not
    get properly removed from MDCache::reconnected_snaplrealm. This causes
    incorrect "unconnected snaprealm xxx" warning
    
    Signed-off-by: Yan, Zheng <<EMAIL>>
    (cherry picked from commit 85e687de876ca7e0174734ba81130949c4ab6a40)

commit 3e745ef3172aeef07038c9156d4668ca08078f18
Author: Yan, Zheng <<EMAIL>>
Date:   Thu Jun 30 16:05:57 2016 +0800

    qa/workunits/fs: fix expect_failure function in test scripts
    
    The origin expect_failure function return 0 regardness of command's
    return value.
    
    Signed-off-by: Yan, Zheng <<EMAIL>>
    (cherry picked from commit 16f7d7c71e65cd68cecde3e5b89d189ab5950548)

commit 37157d146f1983b94588135e4b0468a9c59c3ead
Author: Yan, Zheng <<EMAIL>>
Date:   Wed Jun 29 20:49:40 2016 +0800

    client: make sure snapflush is sent before normal cap message
    
    MDS does null snapflush when it receives normal cap message. So client
    must send snapflush first.
    
    Signed-off-by: Yan, Zheng <<EMAIL>>
    (cherry picked from commit d3916717e2edc8000400f678fa7134ca1406a074)

commit 326d46b54280ff5612ee571671cc4956fcb7e8eb
Author: Yan, Zheng <<EMAIL>>
Date:   Tue Jun 28 20:39:08 2016 +0800

    client: unify cap flush and snapcap flush
    
    This patch includes following changes
    - assign flush tid to snapcap flush
    - remove session's flushing_capsnaps list. add inode with snapcap
      flushes to session's flushing_caps list instead.
    - when reconnecting to MDS, re-send one inode's snapcap flushes and
      cap flushes at the same time.
    
    Signed-off-by: Yan, Zheng <<EMAIL>>
    (cherry picked from commit a05e996b2a2c36496abd8538829ac4897392f6eb)

commit 5c2ff04061cc686c8ece37cee3393365769d2bf1
Author: Yan, Zheng <<EMAIL>>
Date:   Wed Jun 29 17:15:01 2016 +0800

    mds: handle partly purged directory
    
    For a snapshoted direcotry whose snaprealm parents are being opened,
    MDS does not know if the directory is purgeable. So MDS can't skip
    committing dirfrags of the directory. But if the direcotry is purgeale,
    some dirfrags could have already been deleted during MDS failover.
    Committing them could return -ENOENT.
    
    Signed-off-by: Yan, Zheng <<EMAIL>>
    (cherry picked from commit bc50e0309280c08c3ca79dfa5514ac3a15f81a23)

commit 57b39f04087f3ba855248950723c949e3b7dcd7c
Author: Yan, Zheng <<EMAIL>>
Date:   Wed Jun 29 11:42:42 2016 +0800

    mds: do files recovery after processing cap flushes
    
    File recovery may update inode and trigger inode COW. MDS relies on
    client caps to setup CInode::client_need_snapflush. But for a given
    client, the reconnected caps may not include the flushing caps.
    (Before MDS failover, client released and flushed some caps at the
    same time. When MDS recovers, client re-send the cap flush and send
    cap reconnect to the MDS.) This may cause later snapflush to get
    dropped.
    
    Signed-off-by: Yan, Zheng <<EMAIL>>
    (cherry picked from commit dd98448d3d0bbb7d338f03f7ad1e40f217eebe0d)

commit bace1c8c7ce7d29676b9ed6925cdee41af8a8425
Author: Yan, Zheng <<EMAIL>>
Date:   Wed Jun 29 11:25:12 2016 +0800

    mds: combine MDCache::{reconnected_caps,cap_imports_dirty}
    
    Signed-off-by: Yan, Zheng <<EMAIL>>
    (cherry picked from commit 57067e032e84bbdb69c499aa7ea93ca68082569b)

commit 365bda18e3fe960be69d81882e11dcb9932a002c
Author: Yan, Zheng <<EMAIL>>
Date:   Fri Jun 24 17:09:34 2016 +0800

    mds: remove CEPH_LOCK_IFLOCKL from cinode_lock_info
    
    Currently we don't support dirty CEPH_CAP_FLOCK_EXCL
    
    Signed-off-by: Yan, Zheng <<EMAIL>>
    (cherry picked from commit cfc3ec17b6f245e6d8a0be4fdf6cfa64d2fb725f)

commit 55367c5cda09167778839eb8474f86903857f53a
Author: Yan, Zheng <<EMAIL>>
Date:   Thu Jun 23 16:59:46 2016 +0800

    mds: rebuild the internal states that tracking pending snapflush
    
    Signed-off-by: Yan, Zheng <<EMAIL>>
    (cherry picked from commit 1b7d198f633a8608b704f82f01a3f4a4a3a4892b)

commit 0897fc4f15804a25a667bf7e495dc4ff1209127b
Author: Yan, Zheng <<EMAIL>>
Date:   Wed Jun 22 20:34:41 2016 +0800

    mds: using cap_reconnect_t to track cap recconect
    
    Previous commit extended cap_reconnect_t to include snapflush
    related information. This information are needed in various
    places
    
    Signed-off-by: Yan, Zheng <<EMAIL>>
    (cherry picked from commit 25b6011dcf07c1fc663cbe29ffd119b66545a0ac)

commit ac508dc9202ebdb8f39bf1304bb459637cae1cb9
Author: Yan, Zheng <<EMAIL>>
Date:   Wed Jun 22 15:56:29 2016 +0800

    mds: add 'follows' of first pending snapflush to cap reconnect message
    
    This helps the recovering MDS to reconstruct the internal states that
    tracking pending snapflush.
    
    Signed-off-by: Yan, Zheng <<EMAIL>>
    (cherry picked from commit dd3963a878d4bb2fb5992278ccbc9f7633fb8786)

commit c258f52afa68bf991b4d6c76d36271333c3e49bf
Author: Yan, Zheng <<EMAIL>>
Date:   Tue Jun 21 17:17:56 2016 +0800

    mds: journal snap inodes that need flush when expiring log segment
    
    Treat snap inodes that need flush in the same way as open files.
    When MDS recovers, this make sure that journal replay bring snap
    inodes that need flush into the cache
    
    Signed-off-by: Yan, Zheng <<EMAIL>>
    (cherry picked from commit 306153b3d012832bdfa20402077fa60a9a5d626c)

commit 42dd72df1a514175be05d5d613d9b00296cb1b1e
Author: Yan, Zheng <<EMAIL>>
Date:   Tue Jun 21 16:20:58 2016 +0800

    mds: properly update client_snap_caps when splitting snap inode
    
    update the new snap inode's client_snap_caps according to the old
    snap inode.
    
    Signed-off-by: Yan, Zheng <<EMAIL>>
    (cherry picked from commit 4883779935219817c7e391940a025be1679daeb5)

commit b15cf42a4be7bb290e095cd5027d7f9ac604a97d
Author: Oleh Prypin <<EMAIL>>
Date:   Thu Jul 14 01:06:51 2016 +0300

    install-deps: Get the latest virtualenv in a temporary folder
    to work around a bug in old virtualenv
    
    Signed-off-by: Oleh Prypin <<EMAIL>>
    (cherry picked from commit 2699b6d89b8c1c193fd86b5233d1ea86458753a0)

commit 00f3fd4a39f35780e602acfa4023bf59031b3617
Author: Yan, Zheng <<EMAIL>>
Date:   Tue Jul 12 16:07:53 2016 +0800

    ceph-fuse: link to libtcmalloc or jemalloc
    
    Fixes: http://tracker.ceph.com/issues/16655
    Signed-off-by: Yan, Zheng <<EMAIL>>
    (cherry picked from commit e644f6eb64037b1e21ce55d4dcffa43571ac1327)

commit d794e295786599102d5ea1a4c10002f4f8d85649
Merge: bb94997 7178f23
Author: Patrick Donnelly <<EMAIL>>
Date:   Thu Jul 14 12:10:11 2016 -0400

    Merge remote-tracking branch 'upstream/pull/10298/head' into jewel
    
    * upstream/pull/10298/head:
      doc: fix standby replay config

commit 7178f23c1bcb800bec2c7bec138ac02b22dbad2b
Author: Patrick Donnelly <<EMAIL>>
Date:   Tue Jul 12 15:43:23 2016 -0400

    doc: fix standby replay config
    
    I tried using these settings in tests without success. The correct config names
    are prefixed with "mds".
    
    Fixes: http://tracker.ceph.com/issues/16664
    
    Signed-off-by: Patrick Donnelly <<EMAIL>>

commit 8981f3beb5732f51197a5be84df18edde64217ca
Author: Casey Bodley <<EMAIL>>
Date:   Tue Jun 21 15:09:53 2016 -0400

    rgw: add pg_ver to tombstone_cache
    
    a tombstone cache was added to remember the mtime of deleted objects for
    use with the HTTP_IF_MODIFIED_SINCE header, but the comparison was still
    failing because of a missing pg_ver. added pg_ver to the tombstone cache
    so it can be passed with HTTP_DEST_PG_VER
    
    Signed-off-by: Casey Bodley <<EMAIL>>
    (cherry picked from commit adb529f2fb983df671a1db58a0b17862a29762f0)

commit 6c32fe8a75f535f21d3bec089bf06a82db5ec876
Author: Yehuda Sadeh <<EMAIL>>
Date:   Fri Jun 10 14:35:01 2016 -0700

    rgw: add obj tombstone cache
    
    The obj tombstone cache is used in multi-zone environmet to keep
    track of removed objects' mtime. This is then used to fetch remote
    object only if its newer than the object that was removed, otherwise
    we're just fetching ghost of the past.
    
    Signed-off-by: Yehuda Sadeh <<EMAIL>>
    (cherry picked from commit eb10214920c23b24edd94ca53d0f36c85404644d)

commit f651a8f0cc2a11eb1a037338e35de3cf165f5ac2
Author: Yehuda Sadeh <<EMAIL>>
Date:   Fri Jun 10 14:34:41 2016 -0700

    rgw: rgw_obj comparison also accounts for bucket id
    
    Signed-off-by: Yehuda Sadeh <<EMAIL>>
    (cherry picked from commit a7f32c4b1bd951b1a18b7d13013dc8e2822d6ffc)

commit 354e81c1800066e4e8f525706214c41d90816b9d
Author: Yehuda Sadeh <<EMAIL>>
Date:   Thu Jun 9 14:30:42 2016 -0700

    cls/rgw: reduce tag_timeout significantly
    
    The tag timeout is used for applying dir_suggest changes. Shorten it
    so that changes will be reported early and can be used in data sync.
    
    Signed-off-by: Yehuda Sadeh <<EMAIL>>
    (cherry picked from commit 99efdc673b7aed439eebdaa92ff117ba9646dd7c)

commit 24d483119035a78973a1ee3827f5f7c8cb20ce5a
Author: Yehuda Sadeh <<EMAIL>>
Date:   Thu Jun 9 14:22:07 2016 -0700

    cls_rgw: dir_suggest entries bucket index logging
    
    Fixes: http://tracker.ceph.com/issues/16222
    
    Log entries that were set through the dir_suggest mechanism in
    the bucket index log.
    
    Signed-off-by: Yehuda Sadeh <<EMAIL>>
    (cherry picked from commit ae00c5529219d81987425160dbb2a4e937661a6c)

commit bdef8b23714eaff933992ed8c02fb35cd8d11818
Author: Yehuda Sadeh <<EMAIL>>
Date:   Thu Jun 9 12:59:35 2016 -0700

    cls/rgw: fix timespan comparison
    
    Fixes: http://tracker.ceph.com/issues/16220
    
    Signed-off-by: Yehuda Sadeh <<EMAIL>>
    (cherry picked from commit e0a07f70f7740a66ffa2646e0a57e8bdc0285373)

commit c34dbd4b19dcc35483306f95932b3ed6ed8332fd
Author: Yehuda Sadeh <<EMAIL>>
Date:   Sat Jun 4 05:47:50 2016 -0700

    rgw: data sync squash, prioritize object removal
    
    Signed-off-by: Yehuda Sadeh <<EMAIL>>
    (cherry picked from commit 2fcd8b1d49aae2fd03b986dd10bb3f98d3b8f32e)

commit 09eee3be8fcd79ef46ecfbd277e8cc2bf4f28d93
Author: Yehuda Sadeh <<EMAIL>>
Date:   Sat Jun 4 03:29:12 2016 -0700

    rgw: squash bi complete bi log entries
    
    only process the newest complete operation in a bilog listing.
    
    Signed-off-by: Yehuda Sadeh <<EMAIL>>
    (cherry picked from commit f69db8e455f07c5594363c5beac329cb964be1ff)

commit 571a13255aff4ca072ff88bb4ce54da086fbad86
Author: Yehuda Sadeh <<EMAIL>>
Date:   Fri Jun 3 02:03:13 2016 -0700

    rgw: stop bucket lease only after draining object sync operations
    
    Fixes: http://tracker.ceph.com/issues/16143
    
    Signed-off-by: Yehuda Sadeh <<EMAIL>>
    (cherry picked from commit be6ad9a76c3008ea25a737c0de718faab8fca611)

commit 7af0306de5778340fc7c9395b237bf4e73716d0a
Author: Casey Bodley <<EMAIL>>
Date:   Tue May 24 12:57:19 2016 -0400

    rgw: don't leak RGWRados::cr_registry on error
    
    Signed-off-by: Casey Bodley <<EMAIL>>
    (cherry picked from commit 53a7f1a9951fa21cb46b5fb3914db3547b568aa5)

commit 9591e505c1d2c7b66b4c17421f94b6fc7e68913f
Author: Casey Bodley <<EMAIL>>
Date:   Tue May 24 10:40:25 2016 -0400

    rgw: dont need atomic RGWRados::next_rados_handle
    
    next_rados_handle is only accessed under an exclusive handle_lock
    
    Signed-off-by: Casey Bodley <<EMAIL>>
    (cherry picked from commit 1adff94b720d01240040fdffebdbf53efdd528a5)

commit 047379c2521434f2dbbe67ebbf53e59ed92654f0
Author: Casey Bodley <<EMAIL>>
Date:   Tue May 24 10:23:26 2016 -0400

    rgw: remove unneeded RGWRados::num_rados_handles
    
    Signed-off-by: Casey Bodley <<EMAIL>>
    (cherry picked from commit 72d5a485e41ac5824c30556b6cfe659094cd303c)

commit 7848482988711406c9cde48d828a0d118f764ad1
Author: Casey Bodley <<EMAIL>>
Date:   Tue May 3 10:35:41 2016 -0400

    rgw: use vector for librados handles
    
    using a vector instead of an array of pointers cleans up our
    initialization/shutdown logic
    
    Signed-off-by: Casey Bodley <<EMAIL>>
    (cherry picked from commit 02fb39d7f5835ada4d6304f318203444dc7eedc9)

commit bb94997bf4548a430b686a7ceb98052fdb051223
Merge: 6ae4d13 a40cfe4
Author: Casey Bodley <<EMAIL>>
Date:   Tue Jul 12 10:09:34 2016 -0400

    Merge pull request #9268 from theanalyst/wip-15992-jewel
    
    jewel: radosgw-admin: EEXIST messages for create operations
    
    Reviewed-by: Casey Bodley <<EMAIL>>

commit 6ae4d13e81d4b98e34d7094e939a8eab5820f608
Merge: bf73c9a 21f0216
Author: Loic Dachary <<EMAIL>>
Date:   Sun Jul 10 23:03:16 2016 +0200

    Merge pull request #8497 from sabaini/jewel
    
    ceph-disk: Accept bcache devices as data disks
    
    Reviewed-by: Loic Dachary <<EMAIL>>

commit 2c39d3652acbab77e8da50fa1b7ef537374ac109
Author: Pete Zaitcev <<EMAIL>>
Date:   Tue Jun 7 17:44:20 2016 -0600

    rgw: Add documentation for the Multi-tenancy feature
    
    Signed-off-by: Pete Zaitcev <<EMAIL>>
    (cherry picked from commit 0e622020fa2a185eaf0546d93a20b06d44e7f691)

commit 9e5a3ae3cd35ccbd7a9f48c555fb93d5beee57cb
Author: Feng Guo <<EMAIL>>
Date:   Fri Jun 24 09:26:16 2016 +0800

    RGW:add socket backlog setting for via ceph.conf
    http://tracker.ceph.com/issues/16406
    
    Signed-off-by: Feng Guo <<EMAIL>>
    (cherry picked from commit 3e4df832d34c782795a32b5a30f5c7414a1c3fa9)

commit aa3936131d6525e656cd56315947bbf9ddc508ce
Author: Yan, Zheng <<EMAIL>>
Date:   Fri Apr 15 20:15:14 2016 +0800

    mds: disallow 'open truncate' non-regular inode
    
    Signed-off-by: Yan, Zheng <<EMAIL>>
    (cherry picked from commit 0e4b6f2332bb4822cf324587a94144f1c98e4b97)

commit 3af7b422a3a97c0e89dbff757c5ed0f4557a6602
Author: Yan, Zheng <<EMAIL>>
Date:   Fri Apr 15 19:45:23 2016 +0800

    mds: only open non-regular inode with mode FILE_MODE_PIN
    
    ceph_atomic_open() in kernel client does lookup and open at the same
    time. So it can open a symlink inode with mode CEPH_FILE_MODE_WR.
    Open a symlink inode with mode CEPH_FILE_MODE_WR triggers assertion
    in Locker::check_inode_max_size();
    
    Signed-off-by: Yan, Zheng <<EMAIL>>
    (cherry picked from commit 4d15eb12298e007744486e28924a6f0ae071bd06)

commit 8655803d8fb9ac21f75b636fb6d666f387462934
Author: Robin H. Johnson <<EMAIL>>
Date:   Thu Jul 7 14:14:36 2016 -0700

    rgw: fix multi-delete query param parsing.
    
    Multi-delete is triggered by a query parameter on POST, but there are
    multiple valid ways of representing it, and Ceph should accept ANY way
    that has the query parameter set, regardless of what value or absence of
    value.
    
    This caused the RubyGem aws-sdk-v1 to break, and has been present since
    multi-delete was first added in commit 0a1f4a97da, for the bobtail
    release.
    
    Fixes: http://tracker.ceph.com/issues/16618
    Signed-off-by: Robin H. Johnson <<EMAIL>>
    (cherry picked from commit a7016e1b67e82641f0702fda4eae799e953063e6)

commit bf73c9ad08ed2cc8db821c08694b11461549fb26
Merge: f672ddf f858f94
Author: Matt Benjamin <<EMAIL>>
Date:   Wed Jul 6 14:18:44 2016 -0400

    Merge pull request #9545 from Abhishekvrshny/wip-16117-jewel
    
    jewel: rgw: aws4 parsing issue

commit 546141c94a1c5e45dcb70e2d5fd06fe1ac0b1599
Author: Wido den Hollander <<EMAIL>>
Date:   Tue Apr 5 11:14:16 2016 +0200

    rgw: Set Access-Control-Allow-Origin to a Asterisk if allowed in a rule
    
    Before this patch the RGW would respond with the Origin send by the client in the request
    if a wildcard/asterisk was specified as a valid Origin.
    
    This patch makes sure we respond with a header like this:
    
      Access-Control-Allow-Origin: *
    
    This way a resource can be used on different Origins by the same browser and that browser
    will use the content as the asterisk.
    
    We also keep in mind that when Authorization is send by the client different rules apply.
    In the case of Authorization we may not respond with an Asterisk, but we do have to
    add the Vary header with 'Origin' as a value to let the browser know that for different
    Origins it has to perform a new request.
    
    More information: https://developer.mozilla.org/en-US/docs/Web/HTTP/Access_control_CORS
    
    Fixes: #15348
    
    Signed-off-by: Wido den Hollander <<EMAIL>>
    (cherry picked from commit 0021e224480c7164330eaa7cc1078bb8795169bf)

commit f672ddfc5ad6ac86051987985067318436f0667b
Merge: aa211cb ec884a3
Author: Loic Dachary <<EMAIL>>
Date:   Wed Jul 6 11:55:13 2016 +0200

    Merge pull request #9568 from dreamhost/wip-16182
    
    jewel: rgw: backport merge of static sites fixes
    
    Reviewed-by: Yehuda Sadeh <<EMAIL>>

commit 2afc176a2792a9ef389f34c7de1aba697fca6a31
Author: Albert Tu <<EMAIL>>
Date:   Tue Jun 28 10:38:07 2016 +0800

    rgw: fix double counting in RGWRados::update_containers_stats()
    
    Fixes: http://tracker.ceph.com/issues/16188
    Signed-off-by: Albert Tu <<EMAIL>>
    (cherry picked from commit 5dd825aed25588843dc4834be3f5fdf10d93bc68)

commit 489f8ce48b30c708879a002a55aecc080421b5a1
Author: runsisi <<EMAIL>>
Date:   Tue Jul 5 16:08:40 2016 +0800

    librbd: fix missing return statement if failed to get mirror image state
    
    Signed-off-by: runsisi <<EMAIL>>
    (cherry picked from commit ea775178b61ba38237343b07a90d19802f0b7dac)

commit a7987f060479f81d6181f8949e8ed2c8b3029dfd
Author: Yan, Zheng <<EMAIL>>
Date:   Wed May 25 18:33:49 2016 +0800

    MDSMonitor.cc: fix mdsmap.<namespace> subscriptions
    
    Signed-off-by: Yan, Zheng <<EMAIL>>
    (cherry picked from commit 8f09dd15cb07597c57d0a6ae981f15a47de11bb9)

commit d244b7a0c6eb4a57a424297d4293184dff28b94c
Author: Patrick Donnelly <<EMAIL>>
Date:   Fri Jun 17 11:53:32 2016 -0400

    mds: add maximum fragment size constraint
    
    This commit adds a new config option
    
        mds_bal_fragment_size_max = 10000*10
    
    which is an order of magnitude larger than mds_bal_split_size.
    
    This limit prevents a fragment from getting too large which results in large
    omap directories.
    
    Right now the limit is enforced only in the RPC paths and in stray directory
    entry creation.
    
    Fixes http://tracker.ceph.com/issues/16164
    
    Signed-off-by: Patrick Donnelly <<EMAIL>>
    (cherry picked from commit 60af83c80910070d8fb10ac7a4f6f24d49521c1b)

commit 64d99b160d9d6a8758b7a9b3783bd1d153e15c9a
Author: Yan, Zheng <<EMAIL>>
Date:   Fri Jun 17 11:58:13 2016 +0800

    mds: fix Session::check_access()
    
    It calls CInode::make_path_string(...) with the second argument is
    false. The second argument makes the third argument useless. For
    newly created inode, the path string is something like #1xxxxxxxxx.
    This can cause the access check to fail.
    
    Fixes: http://tracker.ceph.com/issues/16358
    Signed-off-by: Yan, Zheng <<EMAIL>>
    (cherry picked from commit a94ef56523a383c44c7a52e473e37a43fa0cb6a2)

commit bce5646cd7c9bc9c753bfcefeff37991763b9066
Author: Yan, Zheng <<EMAIL>>
Date:   Mon Jun 6 16:41:49 2016 +0800

    client: skip executing async invalidates while umounting
    
    Fixes: http://tracker.ceph.com/issues/16137
    Signed-off-by: Yan, Zheng <<EMAIL>>
    (cherry picked from commit b95e603a3f9568debeb76fc49aae9a6ee4b75c6b)

commit fd7ff96cbc4a2e9b38d805f36cd4e72a32f04925
Author: Yan, Zheng <<EMAIL>>
Date:   Mon Aug 10 15:15:48 2015 +0800

    ceph-fuse: add option to disable kernel pagecache
    
    When multiple clients read/write to a file at the same time, we need
    to disable cache and let reads/writes go to OSDs. In ceph-fuse case,
    there are two caches, one is in ceph-fuse, another one is in kernel.
    We can't disable/enable the kernel cache dynamically. So adding an
    config option to completely disable kernel cache. Disabling kernel
    cache may reduce performance, so option is disabled by default.
    
    Fixes: 12653
    Signed-off-by: Yan, Zheng <<EMAIL>>
    (cherry picked from commit 0f11ec237d4692d313a038ed61aa07a3f40555ba)
    
    Conflicts:
            src/common/config_opts.h
               follow the content in patchset, set fuse_use_invalidate_cb
               to True. Which is also the backport
               for http://tracker.ceph.com/issues/15634

commit 3f76e4acf243dda26c44ae95919b3d4444bd7d88
Author: Yehuda Sadeh <<EMAIL>>
Date:   Wed Jun 29 14:50:12 2016 -0700

    rgw: finish error_repo cr in stop_spawned_services()
    
    Fixes: http://tracker.ceph.com/issues/16530
    
    Need to call finish, otherwise drain_all() wouldn't be able to
    drain it.
    
    Signed-off-by: Yehuda Sadeh <<EMAIL>>
    (cherry picked from commit 492bb608733c83a5d85319cf47c4d86402344376)

commit a5f5513ede87d7d77d83a9327e8d178767f734ea
Author: Greg Farnum <<EMAIL>>
Date:   Tue Jun 28 14:39:46 2016 -0700

    test: fix CMake build of ceph_test_objectcacher_stress
    
    Signed-off-by: Greg Farnum <<EMAIL>>
    (cherry picked from commit 2ee3d02c8f5de8d2a0ae3e22f3029b269fe4a212)

commit 17f1bffdb891a155532d966b7d3ab4983c9016cf
Author: Greg Farnum <<EMAIL>>
Date:   Mon May 23 15:14:21 2016 -0700

    ObjectCacher: fix bh_read_finish offset logic
    
    If we have an incoming read split across multiple BufferHeads, we want to
    line up the BufferHead's bl with the incoming OSDOp's bl at the right offset. We
    were erroneously using this nonsense calculation (always equal to zero!) when
    a much simpler comparison of the BufferHead's logical object offset to the
    incoming OSDOp's logical offset will do the trick nicely.
    
    Fixes: http://tracker.ceph.com/issues/16002
    
    Signed-off-by: Greg Farnum <<EMAIL>>
    (cherry picked from commit 9ec6e7f608608088d51e449c9d375844631dcdde)

commit 73bc6d11745246df45ea6c4b5fbf9be3b9e91c09
Author: Greg Farnum <<EMAIL>>
Date:   Thu Jun 23 14:23:51 2016 -0700

    osd: provide some contents on ObjectExtent usage in testing
    
    Signed-off-by: Greg Farnum <<EMAIL>>
    (cherry picked from commit a555d9a0c7d3b6c2206520cf78445234b0834639)

commit 3446fa498266ec2902a1d4d9215de4e4b1d9c455
Author: Greg Farnum <<EMAIL>>
Date:   Fri Jun 10 17:01:09 2016 -0700

    test: build a correctness test for the ObjectCacher
    
    For now it's very specifically designed to reproduce
    http://tracker.ceph.com/issues/16002, but it can
    be extended to other patterns in the future.
    
    Signed-off-by: Greg Farnum <<EMAIL>>
    (cherry picked from commit 0fd55a9886dd8da344c23a5e9898ee5c5061e8f9)

commit b6684914e29fdc375d91be7c80cdf9615d637c8c
Author: Greg Farnum <<EMAIL>>
Date:   Tue May 31 16:18:19 2016 -0700

    test: split objectcacher test into 'stress' and 'correctness'
    
    Signed-off-by: Greg Farnum <<EMAIL>>
    (cherry picked from commit cc9aab1b0a22c3f7320046b97f75dccf2b86cc6d)

commit 74f59203a13da4ec5ca673a921ed3d07e8d5fc9b
Author: Greg Farnum <<EMAIL>>
Date:   Thu Jun 23 13:41:46 2016 -0700

    test: add a data-storing MemWriteback for testing ObjectCacher
    
    Signed-off-by: Greg Farnum <<EMAIL>>
    (cherry picked from commit ed5801492bf2850e80a328ce5a61cb1ff2709215)

commit aa211cb4ff0a7e9cac4779187848b3ba00c39bf9
Merge: e3ccf0f d48a1ed
Author: Orit Wasserman <<EMAIL>>
Date:   Thu Jun 30 10:36:20 2016 +0200

    Merge pull request #9099 from Abhishekvrshny/wip-15841-jewel
    
    jewel: s3website: x-amz-website-redirect-location header returns malformed HTTP response
    Reviewed-by: Orit Wasserman <<EMAIL>>

commit e3ccf0f9d31c9e35edb551dc24da7af7265799c1
Merge: 448deed a08caa6
Author: Orit Wasserman <<EMAIL>>
Date:   Thu Jun 30 10:35:23 2016 +0200

    Merge pull request #9265 from Abhishekvrshny/wip-15965-jewel
    
    jewel: No Last-Modified, Content-Size and X-Object-Manifest headers if no segments in DLO manifest
    Reviewed-by: Orit Wasserman <<EMAIL>>

commit 448deed8484b06bdbbdad62cbe34cfab7958bbc1
Merge: ad182de ff9c29a
Author: Orit Wasserman <<EMAIL>>
Date:   Thu Jun 30 10:34:42 2016 +0200

    Merge pull request #9267 from Abhishekvrshny/wip-15960-jewel
    
    jewel: rgw: custom metadata aren't camelcased in Swift's responses
    Reviewed-by: Orit Wasserman <<EMAIL>>

commit ad182de1ca849d90fbbbf2ec057b476f32e86d8b
Merge: 3ccfac6 108638f
Author: Orit Wasserman <<EMAIL>>
Date:   Thu Jun 30 10:28:45 2016 +0200

    Merge pull request #9316 from Abhishekvrshny/wip-15954-jewel
    
    jewel: rgw: initial slashes are not properly handled in Swift's BulkDelete
    Reviewed-by: Orit Wasserman <<EMAIL>>

commit 3ccfac6b8e98afef19e6c113bd38ae7ab0c51c24
Merge: 4e1ebac 0bdc8fd
Author: Orit Wasserman <<EMAIL>>
Date:   Thu Jun 30 10:26:09 2016 +0200

    Merge pull request #9390 from vumrao/wip-vumrao-16071
    
    jewel: rgw : cleanup radosgw-admin temp command as it was deprecated
    Reviewed-by: Orit Wasserman <<EMAIL>>

commit 4e1ebac3048ee1750978e11115144df074923e22
Merge: 4957014 4eded9a
Author: Orit Wasserman <<EMAIL>>
Date:   Thu Jun 30 10:25:51 2016 +0200

    Merge pull request #9542 from Abhishekvrshny/wip-15967-jewel
    
    jewel: rgw: account/container metadata not actually present in a request are deleted during POST through Swift API
    Reviewed-by: Orit Wasserman <<EMAIL>>

commit 4957014a0e7a744af6c782e6d4e9e5a89c934ace
Merge: bb02d3e e8b7dd4
Author: Orit Wasserman <<EMAIL>>
Date:   Thu Jun 30 10:25:30 2016 +0200

    Merge pull request #9543 from Abhishekvrshny/wip-16040-jewel
    
    jewel: rgw: updating CORS/ACLs might not work in some circumstances
    Reviewed-by: Orit Wasserman <<EMAIL>>

commit 757babb03a9024d6aa42fb327244fd983c066545
Author: Jason Dillaman <<EMAIL>>
Date:   Thu Jun 23 09:03:24 2016 -0400

    librbd: memory leak possible if journal op event failed
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit f5069750849c2c43caa4bff766301169a4bfc6ca)

commit e7ec20e6c54f913dd4bf52a949488ac9d258c150
Author: Jason Dillaman <<EMAIL>>
Date:   Thu Jun 23 08:45:59 2016 -0400

    librbd: ignore snap unprotect -EBUSY errors during journal replay
    
    Fixes: http://tracker.ceph.com/issues/16445
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit beef0b4b024e9692305f4b413e6c6b520fdaa7f9)

commit cbc963684def355b9c27932fdb4c605bf373ed6e
Author: Jason Dillaman <<EMAIL>>
Date:   Thu Jun 23 09:23:32 2016 -0400

    librbd: delete ExclusiveLock instance when switching to snapshot
    
    Fixes: http://tracker.ceph.com/issues/16446
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 677832ceb24096ddbeaf2d60e0ac72d28c399f02)

commit d9c3f28f1200e99c8ecf92f50ec4e8840d74a0f1
Author: Jason Dillaman <<EMAIL>>
Date:   Thu Jun 23 13:34:56 2016 -0400

    librbd: mark exclusive lock as released after journal is closed
    
    Fixes: http://tracker.ceph.com/issues/16450
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 1811e62aa0ba3bab85c536eaab8f3adec6d1fffa)

commit b1d969868bdd898958236212ee847f7a401c6406
Author: Jason Dillaman <<EMAIL>>
Date:   Fri Jun 24 10:03:53 2016 -0400

    librbd: do not propagate mirror status notification failures
    
    These should be treated as a broadcast since no ACK is required. The
    peer will eventually see the notification or its watch will have timed
    out and it will re-sync.
    
    Fixes: http://tracker.ceph.com/issues/16470
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 97bade9f76af62765d7aa8c2154e51a7b231e40f)

commit 51defeada069c0e33b03b96be962f155ce3df295
Author: Jason Dillaman <<EMAIL>>
Date:   Wed Jun 22 18:19:52 2016 -0400

    librbd: fix crash while using advisory locks with R/O image
    
    Fixes: http://tracker.ceph.com/issues/16364
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 1007aea3d78ca5aead8a11f932da0c1d9d67259e)

commit db28ddcf88c13aef80e5a7131db463b305102abe
Author: Jason Dillaman <<EMAIL>>
Date:   Wed Jun 22 10:14:21 2016 -0400

    rbd-mirror: block proxied ops with -EROFS return code
    
    When replicating to a local image, the daemon will own the
    exclusive lock and will receive any proxied maintenance ops
    from other clients. Since the image is non-primary, respond
    with -EROFS.
    
    Fixes: http://tracker.ceph.com/issues/16411
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 07b49df24e5f30460ce3ab584a89370ea3ff7cc8)

commit ebce8ceb9353052d1d43d18e2bb76c68e581272e
Author: Jason Dillaman <<EMAIL>>
Date:   Wed Jun 22 10:13:45 2016 -0400

    librbd: optionally block proxied requests with an error code
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 93e2faf38e866fb3e32a7b3f3527d97215c60d31)

commit bb02d3e7a4db89d693ed1555d2ac82c5452978e1
Merge: 8542898 b4a80cb
Author: Loic Dachary <<EMAIL>>
Date:   Thu Jun 30 09:41:20 2016 +0200

    Merge pull request #9631 from dillaman/wip-16232
    
    jewel: test: improve rbd-mirror test case coverage
    
    Reviewed-by: Loic Dachary <<EMAIL>>

commit 70bf7465ad7c75c9c7623a446218ab501f329bd3
Author: Jason Dillaman <<EMAIL>>
Date:   Mon Jun 20 09:39:24 2016 -0400

    librbd: potential race when replaying journal ops
    
    Fixes: http://tracker.ceph.com/issues/16198
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 75b0f58e37b0ede5e8cf8dcaea980cf71a5ca908)

commit f3f4a4a20ab3039f8dfeda23c773141bf6d95792
Author: Jason Dillaman <<EMAIL>>
Date:   Thu Jun 16 09:27:50 2016 -0400

    librbd: journal callback to interrupt replay
    
    If the exclusive lock is lost while the journal is in the
    replay state, the journal close will block until the replay
    completes. The new callback will facilitate stopping replay
    in a timely fashion so that the journal can be closed.
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 9687e5e34aca98934fcf04089ead2794629455a1)

commit b203168e21c0cfba9df55e9ff43e73a5905119d6
Author: Jason Dillaman <<EMAIL>>
Date:   Wed Jun 15 23:04:48 2016 -0400

    rbd-mirror: keep local pointer to image journal
    
    Fixes: http://tracker.ceph.com/issues/16230
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 47e25e59a8ed4c4e64ae58b41c03125c6d3c4104)

commit 0399958f6cc2a16487f0962b5d5a317dc1d9ff21
Author: Jason Dillaman <<EMAIL>>
Date:   Mon Jun 20 11:41:31 2016 -0400

    rbd-mirror: keep events from different epochs independent
    
    Fixes: http://tracker.ceph.com/issues/16362
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 3a5b491ec61134dc2e18cbe4e27a54e64b17f7d2)

commit 6a28d63ac609c4e3eb43a31cabe36f2c79c875d2
Author: Jason Dillaman <<EMAIL>>
Date:   Mon Jun 20 10:32:04 2016 -0400

    librbd: fix lockdep issue when duplicate event detected
    
    Fixes: http://tracker.ceph.com/issues/16363
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 86ef725c34ae950c0e41e89c1aa0c6a15e40f369)

commit 1e85da9cb97f6262eb25d8de234d45e8daccd461
Author: Jason Dillaman <<EMAIL>>
Date:   Thu Jun 16 17:32:20 2016 -0400

    rbd-mirror: ensure replay status formatter has completed before stopping
    
    Fixes: http://tracker.ceph.com/issues/16352
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit cccdca460b4be310f99877cf43c48300713150df)

commit bf58eabf561fdb041ed170e54e5d35c46cbe3258
Author: Jason Dillaman <<EMAIL>>
Date:   Thu Jun 16 10:37:56 2016 -0400

    journal: do not log watch errors against deleted journal
    
    The peer rbd-mirror process will eventually notice that the journal has
    been removed and clean itself up. This avoids flooding the log with
    expected error messages when journaling is disabled.
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 8317ce1611c39ad6a58bf2d760a010587d91ec60)

commit deb6ca84e4083b1cf569d22b84f3194b1fb27a4b
Author: Jason Dillaman <<EMAIL>>
Date:   Thu Jun 16 09:38:28 2016 -0400

    librbd: force-remove journal when disabling feature and removing image
    
    If mirroring is enabled, it's nearly certain that the peer rbd-mirror
    daemon will be a registered client within the journal. Without
    forcing the removal, this would prevent the removal from occurring.
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit d83aa1e0e44784e1f38698a839f8a353c87027eb)

commit 415ea77fdad016faf7da150630e933930c5ac4b6
Author: Jason Dillaman <<EMAIL>>
Date:   Thu Jun 16 09:31:03 2016 -0400

    librbd: ignore ENOENT error when removing image from mirror directory
    
    Fixes: http://tracker.ceph.com/issues/16348
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 4255afd34927ad6c303074aa6814538a8b5ae96e)

commit cf65ed998b175e5d766364ff18741554b775b632
Author: Yuan Zhou <<EMAIL>>
Date:   Thu May 12 18:22:12 2016 +0800

    rbd: Skip rbd cache flush if journaling is enabled under aio_flush
    
    With journaling rbd writes will be persisteted on rbd journal objects.
    The journal will be replayed if crash happen. So it's not necessary to
    flush rbd_cache in this case. This will improve the flush latency.
    
    This patch adds checking on handling aio_flush: if journaling is
    enabled, rbd cache flushing is skipped.
    In a system flush(ImageCtx::flush) the cache is flushed even w/ journaling
    where we truly do need to flush all IO out to disk.
    
    Fixes: http://tracker.ceph.com/issues/15761
    
    Signed-off-by: Yuan Zhou <<EMAIL>>
    (cherry picked from commit f2def83b7a4a98bc60db5ba8936d78a49abace88)

commit caad884704ce22ae0a860a12693d7529a5837212
Author: youji <<EMAIL>>
Date:   Tue Jun 14 11:12:16 2016 -0700

    mon: Monitor: validate prefix on handle_command()
    
    Fixes: http://tracker.ceph.com/issues/16297
    
    Signed-off-by: You Ji <<EMAIL>>
    (cherry picked from commit 7cb3434fed03a5497abfd00bcec7276b70df0654)

commit 3250c4d5a0bd9fc32eb6b1831a38363581f7c1bd
Author: Abhishek Lekshmanan <<EMAIL>>
Date:   Mon Jun 20 15:56:50 2016 +0200

    rgw_swift: newer versions of boost/utility no longer include in_place
    
    boost > 1.58 no longer includes in_place in boost/utility, we need to
    include in_place_factory explicitly. This causes build failures in
    distros that ship with a higher version of boost. Since the only call is for
    swift_ver_location, another possibility is to use emplace()
    instead (though this requires boost ~ 1.56)
    
    Fixes: http://tracker.ceph.com/issues/16391
    Signed-off-by: Abhishek Lekshmanan <<EMAIL>>
    (cherry picked from commit a14f0f3869bb7defa7587ad9ccb18c3f086b2c3d)
    
    Conflicts:
            src/rgw/rgw_rest_swift.cc
                jewel does not have #include <boost/optional.hpp>

commit 85428983070c8df663056404a7c63959bb8ef693
Merge: 2817f99 e9f9916
Author: Loic Dachary <<EMAIL>>
Date:   Wed Jun 29 16:47:15 2016 +0200

    Merge pull request #9294 from theanalyst/wip-16009-jewel
    
    jewel: radosgw-admin: failure for user create after upgrade from hammer to jewel
    
    Reviewed-by: Casey Bodley <<EMAIL>>

commit dd635e46ee2425335264ae493cb6b8437cd712fc
Author: Jason Dillaman <<EMAIL>>
Date:   Thu Jun 23 10:25:54 2016 -0400

    librbd: ignore missing object map during snap remove
    
    Fixes: http://tracker.ceph.com/issues/16350
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 68b296b0f107db39b16fa90c186fdc920d97170d)

commit db7ce96a3093e661f535efdd5e7e37177ba0732b
Author: Jason Dillaman <<EMAIL>>
Date:   Fri Jun 24 10:52:16 2016 -0400

    librbd: removal of partially deleted image needs id lookup
    
    Several operations depend on the image id but if the image cannot be
    opened to retrieve the id, these cleanup operations cannot be executed.
    
    Fixes: http://tracker.ceph.com/issues/16471
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 6a9e7da6c3da40c7b25ff3d5ac84027e94beb287)

commit c1a47c7728a17dbfc71280270b0c3079e5961ffb
Author: Ken Dreyer <<EMAIL>>
Date:   Tue May 31 17:50:05 2016 -0600

    packaging: move parted requirement to -osd subpkg
    
    Prior to this change, ceph-base required the "parted" package, which
    meant that any installation of ceph-osd, ceph-mon, or ceph-mds would
    pull in the parted package.
    
    Move the parted dependency to ceph-osd, since ceph-disk is the only
    thing that uses parted.
    
    The advantage of this change is that Mon and MDS systems will not need
    to install the parted package.
    
    Fixes: http://tracker.ceph.com/issues/16095
    
    Signed-off-by: Ken Dreyer <<EMAIL>>
    (cherry picked from commit 115349680bad520a0aa56ce3a346d93f541a905c)

commit 74dd0359d4d6db3c6dac4fd41703270e5020aad7
Author: Sage Weil <<EMAIL>>
Date:   Mon May 2 14:28:55 2016 -0400

    osd/PG: set last_* PG fields on a creating PG
    
    Use the value from pg_history_t that ultimately came from the
    mon if last_epoch_started == 0.  This establishes a sane lower
    bound on these timestamps, and prevents a mon health error from
    coming up about how a PG is stuck in whatever state while it is
    being created.
    
    (We addressed half of this problem in commit 6ca6aeac, but the
    warning comes back as soon as the OSD reports with a creating
    state.)
    
    Fixes: http://tracker.ceph.com/issues/14952
    Signed-off-by: Sage Weil <<EMAIL>>
    (cherry picked from commit 3be3bc60c12448a36f607c8d4fbf3300c7bbdbee)

commit 2c03e02a04c217b8461b858fd3b46b73c4a370d1
Author: Sage Weil <<EMAIL>>
Date:   Mon May 2 14:27:16 2016 -0400

    osd: set pg history last_clean_scrub_stamp on create
    
    We were setting the other two; set this one as well.
    
    Signed-off-by: Sage Weil <<EMAIL>>
    (cherry picked from commit 47f540d5b7ecc4ae193057df429db24ca6b3de8d)

commit a2e8ae68ea452d03519359cb0cd344e71e603fa9
Author: kofiliu <<EMAIL>>
Date:   Fri May 27 03:45:06 2016 -0400

    osd: sparse_read offset may not be zero for ecpool
    
    Signed-off-by: kofiliu <<EMAIL>>
    (cherry picked from commit 65e8738611cde0090619b3566a2e25f83b4c8468)

commit 2817f9978f73014b1b1363cecacabdd98e499c72
Merge: 38a2542 8b82bb8
Author: Mykola Golub <<EMAIL>>
Date:   Wed Jun 29 11:42:43 2016 +0300

    Merge pull request #9721 from trociny/wip-16289-jewel
    
    jewel: qa/workunits/rbd: respect RBD_CREATE_ARGS environment variable
    
    Reviewed-by: Jason Dillaman <<EMAIL>>

commit b8f7aa28c394830bac9aa73429131e92f7499aa0
Author: Haomai Wang <<EMAIL>>
Date:   Sat Jun 11 13:39:23 2016 +0800

    msg/async/AsyncConnection: make verify auth callback without connection lock
    
    Fixes: http://tracker.ceph.com/issues/16237
    Signed-off-by: Haomai Wang <<EMAIL>>
    (cherry picked from commit 2b7776545c3f87d7f54a53190e65ec48378eaa05)

commit 3dbb08a069c8e8238d2884a78b04f0a0d01a36e3
Author: Haomai Wang <<EMAIL>>
Date:   Thu May 12 12:01:35 2016 +0800

    AsyncConnection: create writable event for in progress connection
    
    Previously we use a tricky with ceph msgr protocol, if initiator side got
    in progress connection state, it will wait until read event. Because if
    tcp session built successfully server side will send the banner firstly
    and initiator side will get read event. Otherwise, if connection failed,
    read event also be called.
    
    But actually man(2)[http://man7.org/linux/man-pages/man2/connect.2.html]
    specify if we want to get notification whether connection built, we need
    to listen writable event. It means when connection built, send buffer
    is ready to be written.
    
    This patch follow the strict nonblock connect process. Not fully sure fix
    http://tracker.ceph.com/issues/15849
    
    Signed-off-by: Haomai Wang <<EMAIL>>
    (cherry picked from commit a74ce419133881ff8618733a0501c4a47e1368e3)

commit 6554d462059b68ab983c0c8355c465e98ca45440
Author: Samuel Just <<EMAIL>>
Date:   Tue Jun 7 17:15:05 2016 -0700

    OSDMonitor::prepare_pgtemp: only update up_thru if newer
    
    Fixes: http://tracker.ceph.com/issues/16185
    Signed-off-by: Samuel Just <<EMAIL>>
    (cherry picked from commit 5f2bd7b6b28aad96d68444b22c04b8b24564616b)

commit a826bb818b02edac71b69fdd97e9318bb42ebc36
Author: Samuel Just <<EMAIL>>
Date:   Thu Jun 2 10:43:17 2016 -0700

    OSDMonitor: drop pg temp from sources other than the current primary
    
    Fixes: http://tracker.ceph.com/issues/16127
    Signed-off-by: Samuel Just <<EMAIL>>
    (cherry picked from commit 1a07123c38e3fecb3fb2e43bbbae962d8411d287)

commit 18fdc1c9f061b396df8095907c5b0ffb0591e76d
Author: Sage Weil <<EMAIL>>
Date:   Tue May 24 14:02:32 2016 -0400

    osd: reject PARALLELEXEC ops with EINVAL
    
    Errors are better than crashing.
    
    Signed-off-by: Sage Weil <<EMAIL>>
    (cherry picked from commit 407495197dd878b62f1356f6d939e33ab50d78c6)

commit f91da931bcca9fd9f055a8d42d86ba3c3d3ea25f
Author: Sage Weil <<EMAIL>>
Date:   Tue May 24 14:02:14 2016 -0400

    ceph_test_rados_api_misc: test rados op with bad flas
    
    Pass the bad PARALLELEXEC flag to remove(), which takes a flags arg.
    
    Signed-off-by: Sage Weil <<EMAIL>>
    (cherry picked from commit 1aa807f4f29b4b016cc737ffa443e8f30c3b7693)

commit edd3f799fb1e5d70244412c5a1be17f74397aae2
Author: Haomai Wang <<EMAIL>>
Date:   Sun Jun 19 23:42:36 2016 +0800

    msg/async: close STATE_WAIT connection in short period
    
    1. in practice, STATE_WAIT connection caused by racing connect should be
    resolved in milliseconds level. we don't need to keep this connection
    forever.
    2. it will avoid unexpected osd peering hang because of outside network
    problem.
    
    Fixes: http://tracker.ceph.com/issues/16378
    Signed-off-by: Haomai Wang <<EMAIL>>
    (cherry picked from commit 4de5407ac96686748497253e4daf51177f809a95)

commit af8e86c20e9b1124abe33f0dff58817fc8aad7c9
Author: Haomai Wang <<EMAIL>>
Date:   Sun Jun 19 23:12:10 2016 +0800

    test_msgr: add assert if wait for shutdown hang
    
    Signed-off-by: Haomai Wang <<EMAIL>>
    (cherry picked from commit ca22e0bc09c1a13c8dac14b25c00501e912b8006)

commit 42ef435554a21dfd5daec53002440a38f4d9f705
Author: Haomai Wang <<EMAIL>>
Date:   Sun Jun 19 23:04:46 2016 +0800

    test/test_msgr: add shutdown hang debug info
    
    Signed-off-by: Haomai Wang <<EMAIL>>
    (cherry picked from commit 37b53d69d3d8c848d4627475fd63c80e15e118e7)

commit d4c531dca281560b904ec14b2749bc2924f470fb
Author: Haomai Wang <<EMAIL>>
Date:   Sun May 22 23:43:47 2016 +0800

    test_msgr: add verbose info for pending connections
    
    Signed-off-by: Haomai Wang <<EMAIL>>
    (cherry picked from commit 39515b0f7c49d0cedbec0cf0dc2196b9d6d6339c)

commit 90ce35c4f449bfd48398b8a164e423f3d72609b2
Author: Haomai Wang <<EMAIL>>
Date:   Sun Jun 19 23:03:07 2016 +0800

    msg/async/AsyncConnection: lower racing case debug log level
    
    Signed-off-by: Haomai Wang <<EMAIL>>
    (cherry picked from commit d8cc985fe0ebc18d4ff725f50304a5ff36a77cd0)

commit 38a25420a0120288029d65cf924f1cbe5f3eb3fd
Merge: 980abcf 75d3261
Author: Loic Dachary <<EMAIL>>
Date:   Wed Jun 29 09:56:58 2016 +0200

    Merge pull request #9425 from cbodley/wip-rgw-sync-retry-jewel
    
    jewel: rgw: retry on bucket sync errors
    
    Reviewed-by: Loic Dachary <<EMAIL>>

commit 980abcfd3966c29b4871d5a0913051a8312d866c
Merge: e230023 933fdef
Author: Loic Dachary <<EMAIL>>
Date:   Wed Jun 29 09:55:26 2016 +0200

    Merge pull request #9327 from cbodley/wip-rgw-zone-modify-master-jewel
    
    jewel: rgw: add_zone only clears master_zone if --master=false
    
    Reviewed-by: Loic Dachary <<EMAIL>>

commit e2300232b07175a12b92f23352deb34210d8ae41
Merge: e63dcb0 8163c4d
Author: Loic Dachary <<EMAIL>>
Date:   Wed Jun 29 09:50:04 2016 +0200

    Merge pull request #9242 from yehudasa/wip-15911-jewel
    
    jewel: rgw: don't unregister request if request is not connected to manager
    
    Reviewed-by: Loic Dachary <<EMAIL>>

commit e63dcb08083dff3a8382900f90df9f3311c9ec99
Merge: c9f2fda b751d48
Author: Jason Dillaman <<EMAIL>>
Date:   Tue Jun 28 15:53:03 2016 -0400

    Merge pull request #9883 from dillaman/wip-16422
    
    jewel: librbd: flag image as updated after proxying maintenance op
    
    Reviewed-by: Jason Dillaman <<EMAIL>>

commit c9f2fda32c55f85f717ec26dfcf9140b16fa8cc6
Merge: 9aafefe 70018bf
Author: Samuel Just <<EMAIL>>
Date:   Mon Jun 27 12:43:11 2016 -0700

    Merge pull request #9105 from Abhishekvrshny/wip-15768-jewel
    
    jewel: FileStore: umount hang because sync thread doesn't exit
    
    Reviewed-by: Samuel Just <<EMAIL>>

commit 9aafefeab6b0f01d7467f70cb2f1b16ae88340e8
Merge: 25c807b cb9e9e1
Author: Loic Dachary <<EMAIL>>
Date:   Mon Jun 27 17:50:39 2016 +0200

    Merge pull request #9633 from liewegas/wip-async-jewel
    
    msg/async: backports of all asyncmsgr fixes to jewel
    
    Reviewed-by: Haomai Wang <<EMAIL>>

commit 25c807b1919fe11b8d8183b06cdfc5465357d509
Merge: ae34e37 6619bd9
Author: Sage Weil <<EMAIL>>
Date:   Mon Jun 27 09:12:14 2016 -0400

    Merge pull request #9576 from Abhishekvrshny/wip-16148-jewel
    
    jewel: Scrub error: 0/1 pinned
    
    Reviewed-by: Sage Weil <<EMAIL>>

commit ae34e3742651f7b679e4b524676d26d4fb906562
Merge: e16830c b7f0e73
Author: Sage Weil <<EMAIL>>
Date:   Mon Jun 27 09:11:43 2016 -0400

    Merge pull request #9575 from Abhishekvrshny/wip-16150-jewel
    
    jewel: crash adding snap to purged_snaps in ReplicatedPG::WaitingOnReplicas
    
    Reviewed-by: Sage Weil <<EMAIL>>

commit e16830c6287323aae4fdddd9f4192793a6c48e74
Merge: 2cf8d58 6c1163c
Author: Sage Weil <<EMAIL>>
Date:   Mon Jun 27 09:10:36 2016 -0400

    Merge pull request #8754 from kmroz/wip-backport-fix-run-dir-chown
    
    jewel: global-init: fixup chown of the run directory along with log and asok files
    
    Reviewed-by: Sage Weil <<EMAIL>>

commit 1f19dbd0f092390b44acfb5fe9dc3936c6732fdd
Author: Mykola Golub <<EMAIL>>
Date:   Mon Jun 27 14:45:02 2016 +0300

    librbd: potential use after free on refresh error
    
    Signed-off-by: Mykola Golub <<EMAIL>>
    (cherry picked from commit e92b3950cd261d36de422ccaa4a2441d41c80d0c)

commit 2cf8d58beb164017d065e91fdbf8c7a380a4dddb
Merge: b861c30 3f8642d
Author: Loic Dachary <<EMAIL>>
Date:   Mon Jun 27 10:12:35 2016 +0200

    Merge pull request #9578 from Abhishekvrshny/wip-16080-jewel
    
    jewel: osd:sched_time not actually randomized
    
    Reviewed-by: Loic Dachary <<EMAIL>>

commit b861c30bdf86378845379bd3e66db32f264b23ee
Merge: eea4851 1c7abcb
Author: Loic Dachary <<EMAIL>>
Date:   Mon Jun 27 10:08:25 2016 +0200

    Merge pull request #9574 from Abhishekvrshny/wip-16153-jewel
    
    jewel: Missing export for rados_aio_get_version in src/include/rados/librados.h
    
    Reviewed-by: Loic Dachary <<EMAIL>>

commit eea48519d76e53ebe94947165c9404259397a37e
Merge: 057ff74 9415d38
Author: Loic Dachary <<EMAIL>>
Date:   Mon Jun 27 10:06:54 2016 +0200

    Merge pull request #9565 from dzafman/wip-15767-jewel
    
    jewel: Fixes for list-inconsistent-*
    
    Reviewed-by: Loic Dachary <<EMAIL>>

commit 057ff74ede5510e1b28b19e3f99d823781d5da39
Merge: 50e242c b2d1df1
Author: Loic Dachary <<EMAIL>>
Date:   Mon Jun 27 09:42:28 2016 +0200

    Merge pull request #8904 from SUSE/wip-15700-jewel
    
    jewel: rados/test.sh workunit timesout on OpenStack
    
    Reviewed-by: Loic Dachary <<EMAIL>>

commit ad3b788b0c83ffb1339d940cd86555dbc3b1b55f
Author: Nathan Cutler <<EMAIL>>
Date:   Sat Apr 23 20:33:17 2016 +0200

    Drop ceph Resource Agent
    
    This RA wraps the ceph sysvinit script. As of Jewel, none of the supported
    distros are using sysvinit anymore. So, drop it.
    
    Incidentally, Pacemaker can control systemd units without any wrappers.
    
    References: http://tracker.ceph.com/issues/14828
    Signed-off-by: Nathan Cutler <<EMAIL>>
    (cherry picked from commit bb624c7334ee4241ea3bf892f88e25d165dc3477)

commit b751d4854aa8629f46f0e3dd835aff27b8be709d
Author: Jason Dillaman <<EMAIL>>
Date:   Tue Jun 21 14:56:43 2016 -0400

    librbd: flag image as updated after proxying maintenance op
    
    Fixes: http://tracker.ceph.com/issues/16404
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 478dd5f173591914b41d87b8c6d035c32cac8d3e)

commit 50e242c28b766bd35c924f0f01c0298ed166d20a
Merge: a34b227 9b75275
Author: Josh Durgin <<EMAIL>>
Date:   Tue Jun 21 16:43:20 2016 -0700

    Merge branch 'wip-dmick-install-deps-jewel' into jewel
    
    install-deps.sh: use mk-build-deps instead of processing control
    
    Reviewed-by: Erwan Velu <<EMAIL>>
    Reviewed-by: Josh Durgin <<EMAIL>>

commit 9b7527537547b388d1cd75cb70a522712420a455
Author: Dan Mick <<EMAIL>>
Date:   Wed Mar 23 17:43:53 2016 -0700

    install-deps.sh: use mk-build-deps instead of processing control
    
    mk-build-deps is designed to handle installing build dependencies;
    use that instead, so '|' indications are handled properly.
    
    Signed-off-by: Dan Mick <<EMAIL>>
    (cherry picked from commit 99bca09b8d09c41077a9d2141ff556b74328e57a)

commit a34b227c1ed11f51d830ea48a6fbfc0c74a981a6
Author: Roi Dayan <<EMAIL>>
Date:   Wed Apr 20 17:56:16 2016 +0300

    xio: add MNop.h to dist tarball
    
    This file is needed when we want to build a package with xio messenger
    but was not added to dist tarball.
    
    Signed-off-by: Roi Dayan <<EMAIL>>
    (cherry picked from commit ac50842d81d271569d4149737834ac53b2f51db2)

commit 393bf7ef409253123e0721cdfe3b78aa4fd55148
Author: Nathan Cutler <<EMAIL>>
Date:   Wed Jun 15 16:53:16 2016 +0200

    rgw: check for -ERR_NOT_MODIFIED in rgw_rest_s3.cc
    
    Fixes: http://tracker.ceph.com/issues/16327
    Signed-off-by: Nathan Cutler <<EMAIL>>
    (cherry picked from commit fc38346c596d8b0bc156183970d6a327943cb577)

commit 6b41d76f53337a696523cddb47b53b703cdfa571
Author: Haomai Wang <<EMAIL>>
Date:   Tue Jun 14 11:03:19 2016 +0800

    TaskFinisher: cancel all tasks wait until finisher done
    
    Otherwise, caller may think pending task won't be executed but actually
    finisher may execute callback which may cause refer to freed object.
    
    Signed-off-by: Haomai Wang <<EMAIL>>
    (cherry picked from commit 40c5679ef038375e2bde982f401d78c1f3e05c6c)

commit 762db30a3c2c43e56b227017b993802369c07219
Author: Vikhyat Umrao <<EMAIL>>
Date:   Tue May 24 09:29:32 2016 +0530

    rgw: support size suffixes for --max-size in
    radosgw-admin command
    
    Fixes: http://tracker.ceph.com/issues/16339
    
    Signed-off-by: Vikhyat Umrao <<EMAIL>>
    (cherry picked from commit 62eec902c70463173e758e55fa38594c594f868f)

commit e3a99c082e3ebd56d5b40d7d94d98e35629df81e
Author: Vikhyat Umrao <<EMAIL>>
Date:   Thu May 26 23:30:25 2016 +0530

    common: add int64_t template for strict_si_cast()
    
    Signed-off-by: Vikhyat Umrao <<EMAIL>>
    (cherry picked from commit 8e429d05370fbe7935212d0ae9608e7547f39860)

commit f8e491168d4802ab071e30e4fdd654dca018bfe6
Author: Vikhyat Umrao <<EMAIL>>
Date:   Mon May 9 18:00:28 2016 +0530

    rados: Add cleanup message with time to rados bench output
    
    Fixes: http://tracker.ceph.com/issues/16338
    
    Signed-off-by: Vikhyat Umrao <<EMAIL>>
    (cherry picked from commit 885ebb1650bb4a355600d85ca436c1ecd4916dce)

commit 8b82bb85e4f2d6fb81c3c4ccc6789856c78f4984
Author: Mykola Golub <<EMAIL>>
Date:   Tue Jun 14 15:28:06 2016 +0300

    qa/workunits/rbd: respect RBD_CREATE_ARGS environment variable
    
    Fixes: http://tracker.ceph.com/issues/16289
    Signed-off-by: Mykola Golub <<EMAIL>>
    (cherry picked from commit 3048d3c7ce800a6174c54946b838e4e4079ec609)

commit 9415d38139a66b86ff40e0521b769665f31fd36b
Author: David Zafman <<EMAIL>>
Date:   Thu May 5 23:20:58 2016 -0700

    rados: Improve list-inconsistent json format
    
    Use array "errors" of strings for the error bits
    Change snapshot number to integer instead of hex
    
    Signed-off-by: David Zafman <<EMAIL>>
    (cherry picked from commit 07dc844d6804db93055573ec83e6753773a4c049)

commit 2fd6e7cf78f2eabbec669f3f9c4e60f1aa3ee8bd
Author: David Zafman <<EMAIL>>
Date:   Fri May 6 19:54:11 2016 -0700

    test: Fix test to not use jq -S which isn't avail in all distributions
    
    Fixes: http://tracker.ceph.com/issues/15766
    
    Signed-off-by: David Zafman <<EMAIL>>
    (cherry picked from commit a2147ad3cb256c34541ff7dc285594a22a24dfc6)

commit c789aa39f27dc83f52465ba2d5b513a06b99c9d2
Author: David Zafman <<EMAIL>>
Date:   Tue May 3 12:10:38 2016 -0700

    test: Add testing of new scrub commands in rados
    
    Test list-inconsistent-pg
    Test list-inconsistent-obj
    Test inconsistent-snapset
    
    Signed-off-by: David Zafman <<EMAIL>>
    (cherry picked from commit db517ba9176f1ee829453b016b1bd43c6054a555)

commit a6f3f762505849994c91efea98c345da16561a22
Author: David Zafman <<EMAIL>>
Date:   Fri Apr 29 11:25:18 2016 -0700

    rados: Don't bother showing list-inconsistent-* errors that aren't set
    
    Signed-off-by: David Zafman <<EMAIL>>
    (cherry picked from commit a551b6bd5c4eba11c170afe63994258e7598c3d2)

commit 4c72195a6c5cb9eeca952f12edf62a10ec666f10
Author: David Zafman <<EMAIL>>
Date:   Fri Apr 29 17:09:13 2016 -0700

    osd, rados: Fixes for list-inconsistent-snapset
    
    Signed-off-by: David Zafman <<EMAIL>>
    (cherry picked from commit fdca113fc24adbb4f60bfdc55fbbd5044b711b5d)

commit 4e4e562926d38371f70ba2cf2f7003daa251b9f0
Author: David Zafman <<EMAIL>>
Date:   Fri Apr 29 11:26:39 2016 -0700

    include, rados: Fixes for list-inconsistent-obj and librados
    
    Signed-off-by: David Zafman <<EMAIL>>
    (cherry picked from commit 4fc4326b36aa00092043514746aa39aedad06b0f)

commit 80f0dd348f32ff6e1f98ca29ea2ed38b5b7f5854
Author: David Zafman <<EMAIL>>
Date:   Tue Apr 26 18:16:22 2016 -0700

    rados: Balance format sections in same do_get_inconsistent_cmd()
    
    Signed-off-by: David Zafman <<EMAIL>>
    (cherry picked from commit de4681b80a5d24e72bbc7ac6dfee4775987fd834)

commit d248aba10ed232df116729591fea5b195a245735
Author: David Zafman <<EMAIL>>
Date:   Mon Mar 21 20:41:03 2016 -0700

    rados: Include epoch in the list-inconsistent-* command output
    
    Signed-off-by: David Zafman <<EMAIL>>
    (cherry picked from commit df4bc16c4d49489f7398eb1ecc7b9aef93258414)

commit 43141e383ebac822221b3f22392f02b95b015ef5
Author: David Zafman <<EMAIL>>
Date:   Mon Mar 21 20:39:53 2016 -0700

    rados: Improve error messages for list-inconsistent commands
    
    Signed-off-by: David Zafman <<EMAIL>>
    (cherry picked from commit cea7cf56ee3b9593c935d0c74ce6e4b28b14c2da)

commit 502540faf67308fa595e03f9f446b4ba67df731d
Author: Jenkins Build Slave User <<EMAIL>>
Date:   Fri May 13 17:50:34 2016 +0000

    10.2.1

commit b4a80cb1e30f8da8e2b0fc2b18fc846609282879
Author: Mykola Golub <<EMAIL>>
Date:   Sat Jun 11 20:01:31 2016 +0300

    qa/workunits/rbd: specify source path
    
    otherwise it looks in $PATH for the script
    
    Signed-off-by: Mykola Golub <<EMAIL>>
    (cherry picked from commit 698242e7435c9c4d1db7cb6d5aab3faf57eda840)

commit 70c97bd07e6764e1c6ff83225f6a2a9dcdfb989e
Author: Jason Dillaman <<EMAIL>>
Date:   Thu Jun 9 16:23:52 2016 -0400

    qa/workunits/rbd: additional rbd-mirror stress tests
    
    Fixes: http://tracker.ceph.com/issues/16197
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit db3e583a5fe4a7985b1e7f1740114da414835af5)

commit c7cfb4825fbcc82e74b5b1461fc86591098a8599
Author: Jason Dillaman <<EMAIL>>
Date:   Thu Jun 9 17:14:36 2016 -0400

    vstart: add --nolockdep option
    
    rbd-mirror stress test cases need a way to disable lockdep when
    initiated in a vstart environment.
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit d5eec7b2f4222dde0cfdaa9e0e4b43be015c3692)

commit d48a1ed7845843cfc7598d97dce6b10992079ec3
Author: Robin H. Johnson <<EMAIL>>
Date:   Sun Apr 17 08:23:23 2016 -0700

    rgw/s3website: Fix x-amz-website-redirect-location support.
    
    Support for the x-amz-website-redirect-location header was broken in the
    feature merging of Jewel, as it previously depended on the error handler to
    redirect, which was overkill. Simplify it to work.
    
    Backport: jewel
    Fixes: http://tracker.ceph.com/issues/15531
    Signed-off-by: Robin H. Johnson <<EMAIL>>
    (cherry picked from commit 7cbb63bb748c4c78c02926acb4ad9bcea5593695)

commit 3f8642d134c473be331a53f420d122b4f390dee6
Author: xie xingguo <<EMAIL>>
Date:   Sat May 14 11:28:04 2016 +0800

    osd: fix sched_time not actually randomized
    
    The test program:
    
    main()
    {
      for (int i = 0; i < 1000; i++) {
        double before = rand() / RAND_MAX;
        double after = rand() / (double)RAND_MAX;
        cout << "before: " << before << " after: " << after << endl;
      }
    }
    
    And the output(partial and with "-std=c++11" option):
    
    before: 0 after: 0.394383
    before: 0 after: 0.79844
    before: 0 after: 0.197551
    before: 0 after: 0.76823
    before: 0 after: 0.55397
    before: 0 after: 0.628871
    before: 0 after: 0.513401
    before: 0 after: 0.916195
    before: 0 after: 0.717297
    before: 0 after: 0.606969
    before: 0 after: 0.242887
    before: 0 after: 0.804177
    before: 0 after: 0.400944
    before: 0 after: 0.108809
    before: 0 after: 0.218257
    before: 0 after: 0.839112
    before: 0 after: 0.296032
    before: 0 after: 0.524287
    
    Fixes: http://tracker.ceph.com/issues/15890
    Signed-off-by: xie xingguo <<EMAIL>>
    (cherry picked from commit d3b7767a64af0cd57d17b888e4ec3bdae6fdab87)

commit 6619bd9dbe257bfeb19931cf7f94c56520ae1ebf
Author: Samuel Just <<EMAIL>>
Date:   Fri May 20 13:59:10 2016 -0700

    ReplicatedPG: adjust num_pinned in _delete_oid
    
    Fixes: http://tracker.ceph.com/issues/15952
    Signed-off-by: Samuel Just <<EMAIL>>
    (cherry picked from commit 907d4e25c676fd3e1c2be90ce8ab5b64b362b0bc)

commit b7f0e73223687035e470fcd7ffc7b851c04aba00
Author: Samuel Just <<EMAIL>>
Date:   Thu May 19 16:00:35 2016 -0700

    PG: update PGPool to detect map gaps and reset cached_removed_snaps
    
    Fixes: http://tracker.ceph.com/issues/15943
    Signed-off-by: Samuel Just <<EMAIL>>
    (cherry picked from commit 5798fb3bf6d726d14a9c5cb99dc5902eba5b878a)

commit 1c7abcbfe58f77d9d915dd2fc2fc4c75883d46c1
Author: Jim Wright <<EMAIL>>
Date:   Thu Jun 2 15:12:30 2016 +0100

    librados: Added declaration for rados_aio_get_version
    
    Was missing from librados.h and hence had an incorrect
    visability.
    
    Fixes: #15535
    (cherry picked from commit a5a1c1732e50be7d27f8742e794eba88d35c1c7f)

commit ec884a3406568ccb4c997b4da8924d25d3e2473b
Author: Robin H. Johnson <<EMAIL>>
Date:   Tue May 17 17:38:38 2016 -0700

    rgw/s3website: whitespace style fixes
    
    Signed-off-by: Robin H. Johnson <<EMAIL>>
    (cherry picked from commit fd35ad001c9430753cc2b1f91a7d48f591c6754e)

commit bf26b6eeb7fb806bff0ee306467277a273dcc887
Author: Robin H. Johnson <<EMAIL>>
Date:   Tue May 17 17:35:41 2016 -0700

    rgw/s3website: Fix ErrocDoc memory leak.
    
    Yehuda pointed [1] out a memory leak with the RGWGetObj not being handed back, and
    suggested that shared_ptr was used to handle it, rather than the prior approach
    of explicitly calling handler->put_op.
    
    This commit implements just that change, in isolation from other changes, for
    ease of comparision testing.
    
    X-Reference-1: https://github.com/ceph/ceph/pull/8669#discussion_r63445516
    Signed-off-by: Robin H. Johnson <<EMAIL>>
    (cherry picked from commit 2fd7bc8cbe4a2e23553e716ce0be75937853e07b)

commit 36672c61be8289ba00dd694c92cd9057dbf5cf29
Author: Robin H. Johnson <<EMAIL>>
Date:   Sun Apr 17 08:23:23 2016 -0700

    rgw/s3website: Fix x-amz-website-redirect-location support.
    
    Support for the x-amz-website-redirect-location header was broken in the
    feature merging of Jewel, as it previously depended on the error handler to
    redirect, which was overkill. Simplify it to work.
    
    Backport: jewel
    Fixes: http://tracker.ceph.com/issues/15531
    Signed-off-by: Robin H. Johnson <<EMAIL>>
    (cherry picked from commit 7cbb63bb748c4c78c02926acb4ad9bcea5593695)

commit 3c0ac8eb9c9d19a6418e3d84df36e4a87f24fb23
Author: Robin H. Johnson <<EMAIL>>
Date:   Wed Apr 20 15:52:51 2016 -0700

    rgw/s3website: Implement ErrorDoc & fix Double-Fault handler
    
    Fix more last minute breakage from merges, now has has a working ErrorDoc as
    well as working double-fault. Also moves some s3website-specific code out of
    the main S3 codepath.
    
    Fixes: #15532
    Fixes: #15555
    Signed-off-by: Robin H. Johnson <<EMAIL>>
    (cherry picked from commit fcb3cf169f1fa7cf878eb154dc3f1ff78e278056)

commit cb9e9e1c322cb7fb2150b15b17bde4371fd1e703
Author: Piotr Dałek <<EMAIL>>
Date:   Fri Feb 26 13:54:20 2016 +0100

    msg/async: Implement smarter worker thread selection
    
    This changeset makes AsyncMessenger a bit smarter when it comes
    to assigning worker threads to AsyncConnections. Each time a worker
    is assigned, its reference count is increased. Next time when Async
    Messenger needs to assign another worker to new AsyncConnection, it
    picks the one with the lowest reference count. If it cannot find an
    idle one, and number of currently instantiated workers is less than
    specified with "ms async op max threads", the new worker is created
    and returned.
    Once AsyncConnection goes away, the reference count on assigned
    worker is decreased.
    This does not prevent, but greatly reduces chances of having a single
    async worker thread doing most (or even all) of the ops, and also
    removes the need to manually tune the "ms async op threads" option.
    
    Signed-off-by: Piotr Dałek <********************>
    (cherry picked from commit 3e80f8d74a535e14d4092b27ea5417bacff8394e)

commit 578ac8aacfdd0ecfecb3ae3ebeb8a0b3ff53b67d
Author: Haomai Wang <<EMAIL>>
Date:   Wed Apr 20 14:23:20 2016 -0400

    Event: fix delete_time_event while in processing list
    
    Signed-off-by: Haomai Wang <<EMAIL>>
    (cherry picked from commit 1ddeede83200566666fce80867eb7cb5a61a4f62)

commit 8c7a13fdfa46095621b96a7da8d3b9ce09439509
Author: Haomai Wang <<EMAIL>>
Date:   Fri Apr 15 13:33:35 2016 +0800

    test_msgr: add delay inject test
    
    Signed-off-by: Haomai Wang <<EMAIL>>
    (cherry picked from commit 0a2392919f9ad7286ae4b5924566197c1069474f)

commit 846992f025586fa83c69eaec3ed09b6ab6677fcc
Author: Haomai Wang <<EMAIL>>
Date:   Fri Apr 15 11:43:42 2016 +0800

    AsyncConnection: make delay message happen within original thread
    
    Fixes: http://tracker.ceph.com/issues/15503
    Signed-off-by: Haomai Wang <<EMAIL>>
    (cherry picked from commit 83f7db58aad2509e1a8742e862d4e8bbfd85c37c)

commit 9b199d0b19220bf4adb9b0754f493e7c1ad78a4e
Author: Piotr Dałek <<EMAIL>>
Date:   Tue Apr 5 09:37:23 2016 +0200

    msg/async: add missing DelayedDelivery and delay injection
    
    Delay injection was missing from a few spots, also, DelayedDelivery
    was added.
    
    Fixes: http://tracker.ceph.com/issues/15372
    Signed-off-by: Piotr Dałek <<EMAIL>>
    (cherry picked from commit 49a0c9981bd4bf61b520ece8fb8adfdf7439185b)

commit 0e6324a726e85e498946a49393e0f7c228c2f913
Author: Haomai Wang <<EMAIL>>
Date:   Sun May 1 08:32:24 2016 +0800

    Event: replace ceph_clock_now with coarse_real_clock
    
    reduce cpu utilization on real clock latency
    
    Signed-off-by: Haomai Wang <<EMAIL>>
    (cherry picked from commit 79343a8a093630f1c0696f135c6e3bef0c23da28)

commit 6597fab4cc27de6d6f1dcfa070ed401612bfed76
Author: Yan Jun <<EMAIL>>
Date:   Mon Apr 25 09:45:20 2016 +0800

    msg/async: fix some return values and misspellings.
    
    Signed-off-by: Yan Jun <<EMAIL>>
    (cherry picked from commit 0519f938e13d0adf53c1328fd636b9fa5e6c5b93)

commit 18f18c7beee3c7a072009838c02d5ba1f97fef2c
Author: Yan Jun <<EMAIL>>
Date:   Mon Mar 28 15:33:30 2016 +0800

    msg/async: delete the confused comments.
    
    Signed-off-by: Yan Jun <<EMAIL>>
    (cherry picked from commit 69a587b139815812433f2b651db6bd723353605d)

commit 79354f4223191c9d8ce6f92aaadf3481f9abf72d
Author: Yan Jun <<EMAIL>>
Date:   Fri Mar 25 17:34:12 2016 +0800

    msg/async: add numevents statistics for external_events
    
    Maybe we need to add the statistics for external_events.
    
    Signed-off-by: Yan Jun <<EMAIL>>
    (cherry picked from commit f08ca0a0892767a8c40e06e336297109aa6142a1)

commit 4005a5193a1cb3e3a0ac1e6f019bead0837ea552
Author: Haomai Wang <<EMAIL>>
Date:   Sun Mar 13 12:24:35 2016 +0800

    AsyncConnection: remove unnecessary "send" flag
    
    Signed-off-by: Haomai Wang <<EMAIL>>
    (cherry picked from commit 5c0a689d720f1016846ff945ca75b9f91756170d)

commit 441847d9951e230b48776fa0fbe639d7b23a595b
Author: Haomai Wang <<EMAIL>>
Date:   Sat Mar 12 21:02:51 2016 +0800

    async: skip unnecessary steps when parsing simple messages
    
    Signed-off-by: Haomai Wang <<EMAIL>>
    (cherry picked from commit a9ac1c4f88ffb5f66c43527ea0ae1deef1be3a96)

commit c02501005b0316ba9f0ae1cd316a92c567b37c3b
Author: Noah Watkins <<EMAIL>>
Date:   Wed May 4 17:22:14 2016 +0800

    client: fstat should take CEPH_STAT_CAP_INODE_ALL
    
    Fixes: http://tracker.ceph.com/issues/15723
    Signed-off-by: Noah Watkins <<EMAIL>>
    Signed-off-by: Yan, Zheng <<EMAIL>>
    (cherry picked from commit 4b1a5d4ef9c3a116bb08100edd576819930047e3)

commit c3f6d82d86f9720a373ec9792b927292f01897c0
Author: xie xingguo <<EMAIL>>
Date:   Wed Jun 1 14:28:17 2016 +0800

    mon/MDSMonitor: fix wrongly set expiration time of blacklist
    
    Signed-off-by: xie xingguo <<EMAIL>>
    (cherry picked from commit 0c3a92bb999e90212a9f38f9f4dc3bf89bd20acb)

commit d4017ae915e8355f9146844a443942c0dce32476
Author: xie xingguo <<EMAIL>>
Date:   Wed Jun 1 11:17:32 2016 +0800

    mon/MDSMonitor: fix wrong positive of jewel flag check
    
    Signed-off-by: xie xingguo <<EMAIL>>
    (cherry picked from commit 24b82bafffced97384135e55ab2a97091e9a7b4b)

commit eea0e916640c3ac2d69ffb9c335dde6332b03938
Author: Yan, Zheng <<EMAIL>>
Date:   Fri May 6 19:07:07 2016 +0800

    mds: finish lock waiters in the same order that they were added.
    
    Current code first processes lock waiters who have smaller wait mask.
    Lock waiters who have large wait mask can starve if client keeps
    sending requests that add waiter with small mask.
    
    Signed-off-by: Yan, Zheng <<EMAIL>>
    (cherry picked from commit d463107473382170c07d9250bb7ace0e5a2a7de2)

commit 2c7fc95e06f6b1b52bef89904399de4088efaff1
Author: Yan, Zheng <<EMAIL>>
Date:   Mon May 23 17:40:05 2016 +0800

    mds: fix race between StrayManager::{eval_stray,reintegrate_stray}
    
    StrayManager::eval_stray() is called after Server::respond_to_request()
    drops locks. So it can race with StrayManager::reintegrate_stray()
    
    Fixes: http://tracker.ceph.com/issues/15920
    Signed-off-by: Yan, Zheng <<EMAIL>>
    (cherry picked from commit 65827a0fd4d4ecb6d5e2eccf3a1818026ce1f10c)

commit 19c13669ecb1bc9788b6741753d3eedbde713620
Author: John Spray <<EMAIL>>
Date:   Mon May 2 22:23:04 2016 +0100

    osdc: send error to recovery waiters on shutdown
    
    ...instead of sending them '0', which gets things
    confused.
    
    Fixes: http://tracker.ceph.com/issues/15689
    Signed-off-by: John Spray <<EMAIL>>
    (cherry picked from commit 62c7a3c59ce479d5240eb5fbef01edf11388c7bb)

commit 7b0318aad79a08f1549bbf9054519a18c9e8c379
Author: John Spray <<EMAIL>>
Date:   Mon May 2 22:14:07 2016 +0100

    mds: fix mdsmap print_summary with standby replays
    
    Fixes: http://tracker.ceph.com/issues/15705
    Signed-off-by: John Spray <<EMAIL>>
    (cherry picked from commit 899a16e50a6c22359e7a5c5ac720a605f6a4b67e)

commit f858f94c9dcd2b2845fe59ff9d213b59574144c5
Author: Yehuda Sadeh <<EMAIL>>
Date:   Wed Jun 1 04:24:34 2016 -0700

    rgw: reduce string copy
    
    As suggested by Casey Bodley.
    
    Signed-off-by: Yehuda Sadeh <<EMAIL>>
    (cherry picked from commit f8f1f217314c32cf65ac1fa4e8e0132b501ee184)

commit 67e3be38fdfcec8fa4b00dfc703a45ffa101679b
Author: Yehuda Sadeh <<EMAIL>>
Date:   Thu May 19 15:02:21 2016 -0700

    rgw: rework aws4 header parsing
    
    Fixes: #15940
    
    Signed-off-by: Yehuda Sadeh <<EMAIL>>
    (cherry picked from commit e3618c87026b5ced8ef81adbcafc7f9b34f2d48d)

commit 5bdd13e6f2bc8c52f9a4829a5cc80691947233bb
Author: Yehuda Sadeh <<EMAIL>>
Date:   Thu May 19 12:52:54 2016 -0700

    rgw: don't add port to aws4 canonical string if using default port
    
    Fixes: #15939
    
    When either port 80 is used, or if it's a secure connection and port 443 is used,
    and when going through the presigned url auth, don't add the port to the signed
    string.
    
    Signed-off-by: Yehuda Sadeh <<EMAIL>>
    (cherry picked from commit 033888bbd0e4d8d81358bf61a099276dddb5692b)

commit 474739e87231dc1fc3e3a805584c3f15e1dd1f94
Author: Yehuda Sadeh <<EMAIL>>
Date:   Thu May 19 11:30:44 2016 -0700

    rgw: use correct method to get current epoch
    
    Fixes: #15939
    
    Signed-off-by: Yehuda Sadeh <<EMAIL>>
    (cherry picked from commit 310f5bdf56a9deb09347aadc158da25750fb6735)

commit fc34fbd469dd1c35804305a96d4e650828049d51
Author: Yehuda Sadeh <<EMAIL>>
Date:   Wed May 18 17:21:28 2016 -0700

    rgw: check for aws4 headers size where needed
    
    Fixes: #15940
    
    Signed-off-by: Yehuda Sadeh <<EMAIL>>
    (cherry picked from commit 493cc5d1241693f3ea52f4d7f3a194d9e0ec1905)

commit 44decb4ea1d8e60ba929500e0ccbdac3417c3647
Author: Radoslaw Zarzynski <<EMAIL>>
Date:   Wed May 25 14:23:29 2016 +0200

    rgw: properly handle initial slashes in SLO's segment path.
    
    Fixes: http://tracker.ceph.com/issues/16015
    Signed-off-by: Radoslaw Zarzynski <<EMAIL>>
    (cherry picked from commit d384b2b8e0ed670f229eb889a14f521fa8d194fc)

commit e8b7dd47018ab115ffa27b7e72470956de738fd3
Author: Radoslaw Zarzynski <<EMAIL>>
Date:   Mon May 23 13:27:24 2016 +0200

    rgw: remove unnecessary data copying in RGWPutMetadataBucket.
    
    Signed-off-by: Radoslaw Zarzynski <<EMAIL>>
    (cherry picked from commit 19c12bbc233a118496f8ad5d640d19bb0e2c5d05)

commit 63e0993e33b10adc4d9e1f80c4fe4c5ee9c5f4ff
Author: Radoslaw Zarzynski <<EMAIL>>
Date:   Sun May 22 15:32:19 2016 +0200

    rgw: Fix updating CORS/ACLs during POST on Swift's container.
    
    Introduced in: 7a7de9b75265b978ba4e53966f614fac033972cb
    Fixes: http://tracker.ceph.com/issues/15976
    Signed-off-by: Radoslaw Zarzynski <<EMAIL>>
    
    (cherry picked from commit d9e4727b7e6ffa1cb2918b610381d41439a056e8)

commit 4eded9aa94384e60e765accb4c9f093bd2534970
Author: Radoslaw Zarzynski <<EMAIL>>
Date:   Sat May 21 02:47:12 2016 +0200

    rgw: fix update of already existing account/bucket's custom attributes.
    
    Introduced in: 3f3b18bff16f6a5b36987f888ba3f2a0d1ea3155.
    Fixes: http://tracker.ceph.com/issues/15977
    Signed-off-by: Radoslaw Zarzynski <<EMAIL>>
    
    (cherry picked from commit d6129e664fc8d25e70bfaf83e340703005f8f73f)

commit 30ee18018ae890a058ae40a6006e1045258d36d5
Author: Radoslaw Zarzynski <<EMAIL>>
Date:   Mon May 9 17:22:45 2016 +0200

    rgw: fix updating account/container metadata of Swift API.
    
    This patch rectifies an issue with handling of user metadata that are
    actually stored by an updated resource (account or container).
    The expected behaviour is they will be merged with new medadata coming
    from an HTTP client.
    
    Backport: Jewel
    Fixes: http://tracker.ceph.com/issues/15779
    Signed-off-by: Radoslaw Zarzynski <<EMAIL>>
    (cherry picked from commit 3f3b18bff16f6a5b36987f888ba3f2a0d1ea3155)

commit 75d326117dd57acdae355cec5ac25112e70ff2ba
Author: Yehuda Sadeh <<EMAIL>>
Date:   Fri May 13 14:13:27 2016 -0700

    rgw: back off if error repo is empty
    
    Don't check it every time
    
    Signed-off-by: Yehuda Sadeh <<EMAIL>>
    (cherry picked from commit dbf6dcb29faa853c2439457c767d550d5fcdc0f2)

commit 8dcd2a1d93f08db6ede05b3028c3fc601defe932
Author: Yehuda Sadeh <<EMAIL>>
Date:   Fri May 13 11:13:48 2016 -0700

    rgw: data sync retries sync on prevously failed bucket shards
    
    Signed-off-by: Yehuda Sadeh <<EMAIL>>
    (cherry picked from commit f1ccc4cd973d16e7676b2374eeefe4ee6f6a4630)

commit 3e5210ddedd1d98473e0bcff04a03958b0b850fc
Author: Yehuda Sadeh <<EMAIL>>
Date:   Wed May 11 15:59:27 2016 -0700

    rgw: store failed data sync entries in separate omap
    
    so that we can reiterate over them
    
    Signed-off-by: Yehuda Sadeh <<EMAIL>>
    (cherry picked from commit b7deb7cb09304f1b0963139296bdb3abb22895ff)

commit d08ca528ea869cf5a899fd684caecf030b96f5fe
Author: Yehuda Sadeh <<EMAIL>>
Date:   Fri May 13 11:20:49 2016 -0700

    rgw: configurable window size to RGWOmapAppend
    
    We want to be able to disable buffering for certain operations.
    
    Signed-off-by: Yehuda Sadeh <<EMAIL>>
    (cherry picked from commit 94ff675e2e7763650d14f62aaf1ff9ddb05cc380)

commit 368e88409e57d6827c7b10f72761d0320cdb4dc2
Author: Yehuda Sadeh <<EMAIL>>
Date:   Thu May 12 09:14:29 2016 -0700

    rgw: add a cr for omap keys removal
    
    Signed-off-by: Yehuda Sadeh <<EMAIL>>
    (cherry picked from commit 20c1b4b8a3695b818c5c64d61d5ea4f84c48c782)

commit 933fdefb56ce1ab883e6d3ee09c34fb2aa14808e
Author: Casey Bodley <<EMAIL>>
Date:   Mon May 16 11:03:45 2016 -0400

    rgw: add_zone only clears master_zone if --master=false
    
    Fixes: http://tracker.ceph.com/issues/15901
    
    Signed-off-by: Casey Bodley <<EMAIL>>
    (cherry picked from commit 40c4d150757cba4b05938c6c760ab9f0296c2ff6)

commit e9f99166f7fc1b4b468e146a47ec9ac71541bec9
Author: Orit Wasserman <<EMAIL>>
Date:   Fri May 20 09:52:01 2016 +0200

    rgw: add missing metadata_heap pool to old zones
    
    Fixes: http://tracker.ceph.com/issues/15937
    
    Signed-off-by: Orit Wasserman <<EMAIL>>
    Signed-off-by: Abhishek Lekshmanan <<EMAIL>>
    (cherry picked from commit b79856bd3f7db8498231d7f35b53b4bcb44a0422)

commit 22662875320717d1ee939732f9cf7939db697106
Author: Zhi Zhang <<EMAIL>>
Date:   Tue May 3 17:12:26 2016 +0800

    mds: wrongly treat symlink inode as normal file/dir when symlink inode is stale on kcephfs
    
    Signed-off-by: Zhi Zhang <<EMAIL>>
    (cherry picked from commit 88b6d669e37924536152a552db46ef43a7353562)

commit 0bdc8fd58ce790638c62bbe5264294ed5e1f710a
Author: Vikhyat Umrao <<EMAIL>>
Date:   Thu May 26 15:43:41 2016 +0530

    rgw : cleanup radosgw-admin temp command as it was deprecated
    and also implementation code for this command was removed in commit
    8d7c8828b02c46e119adc4b9e8f655551512fc2d
    
    Fixes: http://tracker.ceph.com/issues/16071
    
    Signed-off-by: Vikhyat Umrao <<EMAIL>>
    (cherry picked from commit 949f917af80ae0bd9a5448129d3ce8979acf7e0b)

commit 108638f366a70ca634a21b7fe4c9206368791514
Author: Radoslaw Zarzynski <<EMAIL>>
Date:   Tue Mar 15 16:48:26 2016 +0100

    rgw: handle initial slashes properly in BulkDelete of Swift API.
    
    Fixes: http://tracker.ceph.com/issues/15948
    Signed-off-by: Radoslaw Zarzynski <<EMAIL>>
    (cherry picked from commit 1dde062c21c3d6fa3cc01e8c922d7f89c6973bfa)

commit a40cfe4df81c03b197dc31888f5d77927eeae7c5
Author: Abhishek Lekshmanan <<EMAIL>>
Date:   Tue May 3 14:56:38 2016 +0200

    rgw: remove -EEXIST error msg for ZoneCreate
    
    currently for any admin operations like user create etc. you would
    always see:
    
    `RGWZoneParams::create(): error creating default zone params: (17) File
    Exists`
    
    in stdout as the debug level is set to 0, which doesn't make much sense
    for an end user, so skip the error message, callers of the function handle
    the error message anyway, so we skip it here
    
    Fixes: http://tracker.ceph.com/issues/15720
    Signed-off-by: Abhishek Lekshmanan <<EMAIL>>
    (cherry picked from commit 2ace9d7f349ef09d3ed87fb216cda3e305ef706f)

commit ff9c29a0e7a3719858ad94f632b6d1af3f3ec73e
Author: Radoslaw Zarzynski <<EMAIL>>
Date:   Mon May 16 18:24:55 2016 +0200

    rgw: camelcase names of custom attributes in Swift's responses.
    
    Fixes: http://tracker.ceph.com/issues/15902
    Signed-off-by: Radoslaw Zarzynski <<EMAIL>>
    (cherry picked from commit cfde4c42c0248b52ef1b855f7de79ee1e229e73f)

commit 4a3c9f338fb0f2cee71f31652cf31a8ba61d5e4e
Author: Orit Wasserman <<EMAIL>>
Date:   Fri May 13 15:39:32 2016 +0200

    rgw: fix realm pull and period pull for apache frontend
    
    add missing content-type and content-length headers
    
    Fixes: http://tracker.ceph.com/issues/15846
    
    Signed-off-by: Orit Wasserman <<EMAIL>>
    (cherry picked from commit 8454ff1a91322697224332f0e6f0c7a9c856ec9a)

commit a08caa6d3cbca0395a9ed5f487f9de33b615f59f
Author: Radoslaw Zarzynski <<EMAIL>>
Date:   Tue May 10 17:45:20 2016 +0200

    rgw: handle errors properly during GET on Swift's DLO.
    
    Backport: Jewel, Hammer
    Fixes: http://tracker.ceph.com/issues/15812
    Signed-off-by: Radoslaw Zarzynski <<EMAIL>>
    (cherry picked from commit 5ef0202a86a1e4cb56bd29d926555f114a1acdd8)

commit 8163c4dd34d24249bf662c1110c49136ff335bf9
Author: Yehuda Sadeh <<EMAIL>>
Date:   Tue May 10 15:34:19 2016 -0700

    rgw: don't unregister request if request is not connected to manager
    
    That means that request is already complete and done with the manager.
    
    Signed-off-by: Yehuda Sadeh <<EMAIL>>
    (cherry picked from commit 16c0605bf276b245fbf41cb3e000ecdf7b049e15)

commit 70018bfe6f5e298791c34ae4ad6eac2140d6f761
Author: Kefu Chai <<EMAIL>>
Date:   Tue May 3 17:20:04 2016 +0800

    os/FileStore::sync_entry check for stop in after wait
    
    there is chance that the sync_entry() gets signaled in the
    WaitInterval(max_interval) call because of sync_cond.Signal() call after
    "stop = true" in umount(), so without this change, sync_entry() will
    continue wait until min_interval is reached. this forces umount() to
    wait even it has called d_force_sync(), and hence slows down the
    progress of umount(). so we need to check for `stop` if we are not
    signalled because of `force_sync`.
    
    Fixes: http://tracker.ceph.com/issues/15695
    Reported-by: Haomai Wang <<EMAIL>>
    Signed-off-by: Kefu Chai <<EMAIL>>
    (cherry picked from commit 65426a522d9d052fd7c38964f143087f277816c5)

commit b2d1df129bc4403d98b66873ee6bca64ba1ea264
Author: Loic Dachary <<EMAIL>>
Date:   Wed Apr 6 15:39:23 2016 +0200

    tests: be more generous with test timeout
    
    When the thrasher is in action together with a validater (lockdep or
    valgrind), a single test may hang for more than 360 seconds. Increase to
    1200: it does not matter if the value is large, only that it prevents
    the test from hanging forever.
    
    Fixes: http://tracker.ceph.com/issues/15403
    
    Signed-off-by: Loic Dachary <<EMAIL>>
    (cherry picked from commit af89474b3fb2c4aa63680aa6b30d71fad2fdd373)

commit 6c1163cd6e39ba293be1be4a3f82e60fd054a348
Author: Karol Mroz <<EMAIL>>
Date:   Mon Apr 25 17:24:07 2016 +0200

    admin-socket: use chown instead of fchown
    
    fchown() returns success, but does not change asok file
    ownership. chown() does.
    
    Signed-off-by: Karol Mroz <<EMAIL>>
    (cherry picked from commit 3d051a58cc117ff79d4cdd768603d1dec8ca7f60)

commit 3963de78635d760924a36eeb1e8b7782dd5a276e
Author: Karol Mroz <<EMAIL>>
Date:   Sat Apr 23 13:55:18 2016 +0200

    global-init: fixup inconsistent use of ceph ctx and conf
    
    Signed-off-by: Karol Mroz <<EMAIL>>
    (cherry picked from commit 088cc66a94886130df647976050c9d98fcd7572e)

commit 2bc41945ca6a8e8e8e852ae0b7e888bc223dd7a3
Author: Karol Mroz <<EMAIL>>
Date:   Sat Apr 23 13:54:45 2016 +0200

    global-init: chown pid files
    
    Signed-off-by: Karol Mroz <<EMAIL>>
    (cherry picked from commit 9c82d8466a264c0f07fd3ec4cc58c1ddb88cc84f)

commit 5681b78ac74728db70a89bc86845a74ff7d81217
Author: Karol Mroz <<EMAIL>>
Date:   Sat Apr 23 13:47:58 2016 +0200

    global-init: chown run dir
    
    Also performs log file chown() operations if permission drop has been
    deferred. Documents that admin socket chown() happen in the common init
    path.
    
    Signed-off-by: Karol Mroz <<EMAIL>>
    (cherry picked from commit 9d513793b3fe4b7e1402e36a4a675553edd6f317)

commit c2075448f052ef7e49bc95c1f8240c2ff559b5ae
Author: Karol Mroz <<EMAIL>>
Date:   Sat Apr 23 13:40:08 2016 +0200

    common-init: chown admin socket after service thread started
    
    Signed-off-by: Karol Mroz <<EMAIL>>
    (cherry picked from commit 206585c1d0fc6826c91f7830cde8ded86288abef)

commit f7e6b3cf9c363a3a65da4e176c7324d027352420
Author: Karol Mroz <<EMAIL>>
Date:   Sat Apr 23 13:36:01 2016 +0200

    global-init: check init flags and set accordingly
    
    If init flags have changed between the time ceph context was
    instantiated and now, update them. This can happen, for example, if
    between manual global_pre_init() and global_init() calls, a daemon adds
    modifies it's init flags.
    
    Signed-off-by: Karol Mroz <<EMAIL>>
    (cherry picked from commit a0ad13db7aba95537808a570ad4c90f6ac4ca0b8)

commit d4afe947578a7ae4956a99fbc9350920e2239e0b
Author: Karol Mroz <<EMAIL>>
Date:   Sat Apr 23 13:35:02 2016 +0200

    global-init: add a path chown wrapper function
    
    Signed-off-by: Karol Mroz <<EMAIL>>
    (cherry picked from commit 860977b105595dbb50320a30dc0edb922279cb00)

commit 770ae9eecf5c8e064529efec29020f38a3675a63
Author: Karol Mroz <<EMAIL>>
Date:   Sat Apr 23 13:32:45 2016 +0200

    ceph-context: add function to set init flags
    
    Signed-off-by: Karol Mroz <<EMAIL>>
    (cherry picked from commit aecab14089bcfd33d6a18adabede6a54040460d4)

commit 21f0216287485e6cce8811f324fee804ef733524
Author: Peter Sabaini <<EMAIL>>
Date:   Fri Apr 8 00:30:55 2016 +0200

    ceph-disk: Accept bcache devices as data disks
    
    Fixes: http://tracker.ceph.com/issues/13278
    Signed-off-by: Peter Sabaini <<EMAIL>>
