commit 3a66dd4f30852819c1bdaa8ec23c795d4ad77269 (tag: refs/tags/v10.2.1, refs/remotes/gh/jewel)
Author: Jenkins Build Slave User <<EMAIL>>
Date:   Thu May 12 21:48:00 2016 +0000

    10.2.1

commit d93a90ec2743c9aec229891c3b0da72b038cffad
Merge: cb35d7c 466b7fe
Author: <PERSON><PERSON> <<EMAIL>>
Date:   Thu May 12 17:30:14 2016 +0200

    Merge pull request #8853 from ceph/wip-jewel-backports
    
    jewel: several backports
    
    Reviewed-by: <PERSON><PERSON> <lda<PERSON><EMAIL>>

commit cb35d7c25bb1df2a8eff01432d27c5d3bbb196af
Merge: 0e1b633 b6ebb25
Author: <PERSON><PERSON> <<EMAIL>>
Date:   Thu May 12 16:50:33 2016 +0200

    Merge pull request #9041 from vshankar/jewel-15721-backport
    
    jewel: rbd: helpful error message on map failure
    
    Reviewed-by: <PERSON><PERSON> <<EMAIL>>

commit 0e1b6336b60af6bceaf9dbc8ce544fcb7af71961
Merge: bfdf709 867f798
Author: Yehuda Sadeh <<EMAIL>>
Date:   Wed May 11 12:33:50 2016 -0700

    Merge pull request #9081 from yehudasa/wip-rgw-period-commit-jewel
    
    rgw: period commit fix

commit bfdf709dff9bd3d60d11950d4b2d8741ad0cb113
Merge: bf1474f 9d5162f
Author: Gregory Farnum <<EMAIL>>
Date:   Wed May 11 12:24:23 2016 -0700

    Merge pull request #8969 from Abhishekvrshny/wip-15731-jewel
    
    jewel: MDSAuthCap parse no longer fails on paths with hyphens
    
    Reviewed-by: Greg Farnum <<EMAIL>>

commit bf1474f86ce1a102d15c54ee0e1b11005fe48191
Merge: 7a37155 52ca195
Author: Gregory Farnum <<EMAIL>>
Date:   Wed May 11 12:23:28 2016 -0700

    Merge pull request #8970 from Abhishekvrshny/wip-15732-jewel
    
    jewel: MDS incarnation no longer gets lost after remove filesystem
    
    Reviewed-by: Greg Farnum <<EMAIL>>

commit 7a37155d798aebc688fb4a06056dc4761062b098
Merge: e83764c e6aa453
Author: Gregory Farnum <<EMAIL>>
Date:   Wed May 11 12:20:03 2016 -0700

    Merge pull request #8971 from Abhishekvrshny/wip-15738-jewel
    
    jewel: handle standby-replay nodes properly in upgrades
    
    Reviewed-by: Greg Farnum <<EMAIL>>

commit e83764c03b9e88dd2a8ef848269768ffb8b6a703
Merge: 4916950 dccb5ef
Author: Yehuda Sadeh <<EMAIL>>
Date:   Wed May 11 10:38:30 2016 -0700

    Merge pull request #9054 from yehudasa/wip-rgw-admin-output-jewel
    
    jewel: rgw admin output

commit 49169501a922034c2686dd88365d438e12605116
Merge: aa24358 f071d8c
Author: Yehuda Sadeh <<EMAIL>>
Date:   Wed May 11 10:38:17 2016 -0700

    Merge pull request #9053 from yehudasa/wip-15745-jewel
    
    jewel: rgw: handle stripe transition when flushing final pending_data_bl

commit aa243585508eedf87b2e9e558cc63c014f84b3b0
Merge: f88e2df 9bb17db
Author: Yehuda Sadeh <<EMAIL>>
Date:   Wed May 11 10:38:02 2016 -0700

    Merge pull request #9047 from ceph/wip-rgw-period-delete-jewel
    
    jewel: rgw: period delete fixes

commit f88e2df881c045f52f78516aa08def8569be6ecf
Merge: a614858 a496b70
Author: Sage Weil <<EMAIL>>
Date:   Wed May 11 12:30:18 2016 -0400

    Merge pull request #9076 from dillaman/wip-cmake-test-rbd-mirror-jewel
    
    jewel: cmake: fix rbd compile errors

commit a496b705ed48da187b341392530f7e8579637310
Author: Jason Dillaman <<EMAIL>>
Date:   Tue May 10 22:37:01 2016 -0400

    cmake: fix rbd compile errors
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 8d4c00d1bec431f8c721195c81684c4095079c1d)

commit 9a46e13f3ca9915d71f01bb3506818db59c31a7b
Author: runsisi <<EMAIL>>
Date:   Thu Apr 28 10:22:07 2016 +0800

    cmake: add library cls_journal for target unittest_librbd
    
    otherwise if we want to generate target unittest_librbd we need to
    (re)generate cls_journal manually
    
    Signed-off-by: runsisi <<EMAIL>>
    (cherry picked from commit 7efb29416b789fcb7e68aab554d6033e6b5ebb43)

commit a6148582432ff36110145ba642d2e0f38e5872b3
Merge: 7a4810d 48b732f
Author: Kefu Chai <<EMAIL>>
Date:   Wed May 11 16:50:41 2016 +0800

    Merge pull request #9063 from dachary/wip-15838-jewel
    
    jewel: tests: make check fails on ext4
    
    Reviewed-by: Kefu Chai <<EMAIL>>

commit 48b732fbfac1fa6af10bec0ef900029b71204c26
Author: Loic Dachary <<EMAIL>>
Date:   Mon May 2 10:42:24 2016 +0200

    tests: enable make check on ext4 (part 2)
    
    Followup of 475cc08c330e639b226f2ab57dedbe4495f63022
    
    Signed-off-by: Loic Dachary <<EMAIL>>
    (cherry picked from commit b0b3a068d4fcc7b739c5dd3d68297e2d517c378e)

commit e1c67d4684c485711b2b9af02edabe161f3d7779
Author: Sage Weil <<EMAIL>>
Date:   Sat Apr 30 17:12:05 2016 -0400

    qa/workunits/ceph-helpers.sh: make ceph-osd behave on ext4
    
    Signed-off-by: Sage Weil <<EMAIL>>
    (cherry picked from commit 475cc08c330e639b226f2ab57dedbe4495f63022)

commit 867f798d9c364ab5400a61d83cbaef0da9a57b27
Author: Casey Bodley <<EMAIL>>
Date:   Tue May 10 15:51:27 2016 -0400

    radosgw-admin: 'period commit' sends to new master zone by default
    
    if no --remote or --url is given, set --remote = master_zone_id to
    override the rest_master_conn (which refers to the old master zone)
    
    Fixes: http://tracker.ceph.com/issues/15828
    
    Signed-off-by: Casey Bodley <<EMAIL>>
    (cherry picked from commit fbe1a05355549dfe0c05750683eeb61e275950c2)

commit a6a6c13c751bbb74ba4f12dc593cd054aabed87c
Author: Casey Bodley <<EMAIL>>
Date:   Tue May 10 15:48:33 2016 -0400

    radosgw-admin: allow --remote to specify zone or zonegroup id
    
    --remote was only looking for zonegroup connections. extended to check
    for zone connections as well
    
    updated the usage to clarify that it expects zone/zonegroup ids
    
    Signed-off-by: Casey Bodley <<EMAIL>>
    (cherry picked from commit 9fcff89ee1e60009fa367e33c915a90354c9a9cc)

commit dccb5efb128233affaa544b43f93aa4b3d557259
Author: Casey Bodley <<EMAIL>>
Date:   Thu May 5 09:59:49 2016 -0400

    radosgw-admin: 'zonegroup add' prints updated zonegroup
    
    Signed-off-by: Casey Bodley <<EMAIL>>
    (cherry picked from commit d355208023fe08a7e16f1078c872286048c4bcc6)

commit bd6b5fc5d49c08f574b84a2705a8de4a1d5f8ed1
Author: Casey Bodley <<EMAIL>>
Date:   Thu May 5 09:59:32 2016 -0400

    radosgw-admin: 'zonegroup modify' prints updated zonegroup
    
    Signed-off-by: Casey Bodley <<EMAIL>>
    (cherry picked from commit a033f40caf60083111886aa804fcdbf3ee60aef8)

commit c1e92df545d0a8db64c84879bc09023c98783fe4
Author: Casey Bodley <<EMAIL>>
Date:   Thu May 5 09:56:47 2016 -0400

    radosgw-admin: 'zone modify' prints updated zone
    
    Signed-off-by: Casey Bodley <<EMAIL>>
    (cherry picked from commit 8a639014a6f2dd950062aa405bbee1762ca23e09)

commit f071d8c6885786ac5fd5400381369945704abaa8
Author: Yehuda Sadeh <<EMAIL>>
Date:   Thu May 5 14:02:25 2016 -0700

    rgw: handle stripe transition when flushing final pending_data_bl
    
    Fixes: http://tracker.ceph.com/issues/15745
    
    When complete_writing_data() is called, if pending_data_bl is not empty
    we still need to handle stripe transition correctly. If pending_data_bl
    has more data that we can allow in current stripe, move to the next one.
    
    Signed-off-by: Yehuda Sadeh <<EMAIL>>
    (cherry picked from commit b7a7d41839558b1a8786a66e27d9d80e606f61c7)

commit 7a4810d1f51c072f6406a7306743ad7577476c76
Merge: 67809b8 10b393b
Author: Samuel Just <<EMAIL>>
Date:   Tue May 10 11:14:40 2016 -0700

    Merge pull request #9052 from dillaman/wip-cmake-rbd-mirror-replay-status-jewel
    
    jewel: cmake: add missing source file to rbd_mirror/image_replayer
    
    Reviewed-by: Samuel Just <<EMAIL>>

commit 10b393bd90ed76ad41d2123aae5700e171a8e9bc
Author: Casey Bodley <<EMAIL>>
Date:   Fri Apr 29 17:33:03 2016 -0400

    cmake: add missing source file to rbd_mirror/image_replayer
    
    fixes an undefined reference when linking librbd_mirror_internal.a
    
    Signed-off-by: Casey Bodley <<EMAIL>>
    (cherry picked from commit 2f3f56170ab47fc10d4232101ae2e85398a4c299)

commit 67809b8e3d71b3cba5ce91e542e840c099c927cd
Merge: e15854c 8bb4c7f
Author: Sage Weil <<EMAIL>>
Date:   Tue May 10 14:10:05 2016 -0400

    Merge pull request #9051 from athanatos/wip-15815-jewel
    
    Wip 15815 jewel
    
    Reviewed-by: Sage Weil <<EMAIL>>

commit 8bb4c7f92ca784a116d0f7a9a22677b514e7e6ca
Author: Sage Weil <<EMAIL>>
Date:   Fri Apr 22 16:38:14 2016 -0400

    ceph_test_keyvaluedb: clean up test dir
    
    Just like ceph_test_objectstore
    
    Signed-off-by: Sage Weil <<EMAIL>>
    (cherry picked from commit 2b075aa52d4475338c0f3cd3360e1ee4de8befa7)

commit 108cdfdff65bcce98cb3b2da799fe46bed4421f4
Author: Allen Samuels <<EMAIL>>
Date:   Fri Apr 22 14:06:56 2016 -0400

    ceph_test_keyvaluedb: fixup unit test for keyvaluedb
    
    Fixes: http://tracker.ceph.com/issues/15435
    Signed-off-by: Allen Samuels <<EMAIL>>
    (cherry picked from commit 85c14de20936ff9ee224f9b6569a73f9bae75f19)

commit e15854ce98291b3186e9cfadb6cb47a401a4c8ae
Merge: 7686c62 815967a
Author: Jason Dillaman <<EMAIL>>
Date:   Tue May 10 13:52:54 2016 -0400

    Merge pull request #9050 from dillaman/wip-15821
    
    jewel: rbd-mirror: image sync needs to handle snapshot size and protection status
    
    Reviewed-by: Jason Dillaman <<EMAIL>>

commit 815967ab97631f65c44cd200971cc8056e94d89e
Author: Jason Dillaman <<EMAIL>>
Date:   Mon May 9 18:25:39 2016 -0400

    test: add missing header dependency for MockJournaler
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit fa91919c856ac8186545bc8f8da31a8f438a9b5d)

commit 32189573c2da9e1922b6bfae232a62a909797e1a
Author: Jason Dillaman <<EMAIL>>
Date:   Wed May 4 10:23:49 2016 -0400

    rbd-mirror: image sync object copy was not properly mapping snapshots
    
    When the snapshot sequence is out-of-sync between remote and local
    clusters (expected), the objects would not be written with the
    correct snapshot context.
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit dd8f08039bf5354a0413c9da4263d1075243e9c9)

commit d3dc919991a013eb45d55a8b6a04628d089d000a
Author: Jason Dillaman <<EMAIL>>
Date:   Wed May 4 10:23:25 2016 -0400

    rbd-mirror: clean up image sync debug messages
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit acca475fe5c9ff024c7f76397e7f49e4c4558b09)

commit 58e0612a7bb66f6c50dcfc27ed0f1e49d6ec08fa
Author: Jason Dillaman <<EMAIL>>
Date:   Wed May 4 00:12:05 2016 -0400

    librbd: readahead should not read past end of image
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit adb14303ccf8a26ed5536bb692ae63d92ac40a9f)

commit 7dadecb96a4f856d18fcccffb323941b1149ceaa
Author: Jason Dillaman <<EMAIL>>
Date:   Tue May 3 23:17:12 2016 -0400

    test: randomize rbd-mirror test case IO
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 0d75f2ce61cee164da4e5b7ffb4e69c286e6036a)

commit 7aef2976084b8be858e354a05dae12df30743737
Author: Jason Dillaman <<EMAIL>>
Date:   Tue May 3 23:15:58 2016 -0400

    rbd-mirror: don't set object map to nonexistent during image sync
    
    The object maps are initialized to nonexistent at snapshot creation,
    so this operation is unnecessary.
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit fe5d4a811f9d9d1160ff7104fe9243cc17ef1bef)

commit 58e90f1bcb53a38be2491ac7797f170de324af9a
Author: Jason Dillaman <<EMAIL>>
Date:   Tue May 3 23:01:30 2016 -0400

    librbd: improve object map debug messages
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 2c657752a5067e11405dca59461ce9cbdf062602)

commit 6c1af50b67e1a1769bb3d30d28e45fee8b13bb71
Author: Jason Dillaman <<EMAIL>>
Date:   Tue May 3 22:53:52 2016 -0400

    test: snapshot stress test rbd-mirror image sync
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 468287bd182fa0cd02f7551ad6d729ff67ab1a22)

commit 0f84aaf011f1a62250948647069cc7fbd6c45699
Author: Jason Dillaman <<EMAIL>>
Date:   Tue May 3 21:42:05 2016 -0400

    rbd-mirror: allocate empty object map during snapshot create
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 57a7fe0f44c5542a72120d0beed8b38e9ccd66db)

commit fa8a61df1570893e222c0d9431ba61e192a23c40
Author: Jason Dillaman <<EMAIL>>
Date:   Fri Apr 29 16:43:07 2016 -0400

    librbd: optionally skip object map creation upon snapshot
    
    This feature is used by rbd-mirror during image bootstrap since
    it will directly create an empty object map.
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit db16240c79be7e28d65edac2088352b9b146da5f)

commit a140753b6b01215f832cc9524e3532e825e4c2f2
Author: Jason Dillaman <<EMAIL>>
Date:   Mon Apr 25 15:11:28 2016 -0400

    rbd-mirror: refactor snapshot create into its own state machine
    
    Initial implementation will resize the image (if needed) and create
    the snapshot.  It is stubbed out for future state machines for handling
    object map and clones.
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 8adc47fbf8efa3991bd247be43b786676fe5fff9)

commit ef1e4c874adba51bc5a3acbbf3177174f25da088
Author: Jason Dillaman <<EMAIL>>
Date:   Thu Apr 14 21:47:19 2016 -0400

    rbd-mirror: sync snapshot protection status during bootstrap
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit e6fba2b2306c8f04ce088fb15ef70e1dac282ff3)

commit c790da5aef0cb6a5573d86d0a8298d850b63ef25
Author: Jason Dillaman <<EMAIL>>
Date:   Sat Apr 9 13:30:56 2016 -0400

    test: stub test cases for rbd-mirror image bootstrap
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit d0f732877213ba9eec9d3158b839c5a4c796e7ab)

commit 7a7889572789e1bec9cb72e08a0d66d8302d7639
Author: Jason Dillaman <<EMAIL>>
Date:   Tue Apr 5 00:03:12 2016 -0400

    test: share journal::MockJournaler between librbd and rbd-mirror
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 04db7ddec661ef195077c3fab06f248705b17220)
    
    Conflicts:
    	src/test/librbd/test_mock_Journal.cc: MockJournaler moved to new file

commit 7686c628b199dd2f3f58b70e7ab3bbb9c1f199c1
Merge: 63d2d23 f221fed
Author: Jason Dillaman <<EMAIL>>
Date:   Tue May 10 13:40:32 2016 -0400

    Merge pull request #9040 from dillaman/wip-15818
    
    jewel: librbd: potential concurrent event processing during journal replay
    
    Reviewed-by: Jason Dillaman <<EMAIL>>

commit 63d2d2396d80fd7d9fd439488d08e8747846d181
Merge: 1bb1a3a f18b14b
Author: Jason Dillaman <<EMAIL>>
Date:   Tue May 10 13:39:46 2016 -0400

    Merge pull request #9038 from dillaman/wip-15817
    
    jewel: journal: incorrectly computed object offset within set
    
    Reviewed-by: Jason Dillaman <<EMAIL>>

commit 1bb1a3a4862e5bb5a99c7549d499564deecbdafe
Merge: abbe69e 8605040
Author: Jason Dillaman <<EMAIL>>
Date:   Tue May 10 13:39:06 2016 -0400

    Merge pull request #9039 from dillaman/wip-15820
    
    jewel: librbd: client-side handling for incompatible object map sizes
    
    Reviewed-by: Jason Dillaman <<EMAIL>>

commit 86050402066d1f5d680ec777826386a5760e3456
Author: Jason Dillaman <<EMAIL>>
Date:   Wed May 4 17:00:59 2016 -0400

    librbd: guard object map against incompatible image sizes
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 42d3d3ac2d33360aae071330cfa34b87c87eadc5)

commit 7448d29027dafe1abd1e6ebff6f0fe5bb15e4b07
Author: Jason Dillaman <<EMAIL>>
Date:   Wed May 4 15:16:17 2016 -0400

    librbd: properly handle object map open returning error code
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 570de56bfc7fd2edd5cf1cf5b7b92084cd3352a3)

commit 764b3bd413b81d2c98f8c325100661ee41a571bc
Author: Jason Dillaman <<EMAIL>>
Date:   Wed May 4 15:01:58 2016 -0400

    librbd: add client-side memory constraint when loading object map
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 2dd34b2c7b8c376817aafe5cfdda7aa9cfb60e1a)

commit abbe69e19af6d453095e03adbaaaef680ed80958
Merge: 05d5749 5ec1f79
Author: Jason Dillaman <<EMAIL>>
Date:   Tue May 10 13:30:49 2016 -0400

    Merge pull request #9048 from dillaman/wip-15819
    
    jewel: librbd: constrain size of AioWriteEvent journal entries
    
    Reviewed-by: Jason Dillaman <<EMAIL>>

commit 5ec1f79086e4841aa27c12531a1220f69ed3cccb
Author: Jason Dillaman <<EMAIL>>
Date:   Fri May 6 00:08:38 2016 -0400

    librbd: split large AioWrite journal events
    
    Ensure that AioWrite journal events fit within a single
    journal object.
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 70b9fbf2745c795fe345f0298a2f30af80a12985)

commit f1a2e6aeaa22233f13878d004cbe905a42bdc744
Author: Jason Dillaman <<EMAIL>>
Date:   Thu May 5 21:30:18 2016 -0400

    journal: prohibit journal entries larger than the soft object size
    
    This avoids complications like a librbd write event of 128MB trying
    to fit within a 24MB journal object.
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit d5f307dec10429a1ffc042919b71ac25466a1b72)

commit 05d574926df94b4fa2e0186f31d5050bd0b39ed3
Merge: 495313f 2d906e6
Author: Jason Dillaman <<EMAIL>>
Date:   Tue May 10 13:28:11 2016 -0400

    Merge pull request #9036 from dillaman/wip-15814
    
    jewel: librbd: fix get/list mirror image status API
    
    Reviewed-by: Jason Dillaman <<EMAIL>>

commit 2d906e600a8d81aae15bcf3f8b2fc694537890d4
Author: Mykola Golub <<EMAIL>>
Date:   Tue May 10 12:29:12 2016 +0300

    rbd-mirror: fixup to get/list mirror image status API
    
    - embed name and rbd_mirror_image_info_t into rbd_mirror_image_status_t;
    - index image status list by image id to make 'start' work correctly;
    - provide rbd_mirror_image_status_list_cleanup function.
    
    Fixes: #15771
    Signed-off-by: Mykola Golub <<EMAIL>>
    (cherry picked from commit e6c709b3a8be489a900178b8b0d962705d5687e9)

commit 495313f355955463f747be9e6650fe8796c0d124
Merge: 9750610 617004e
Author: Jason Dillaman <<EMAIL>>
Date:   Tue May 10 13:15:23 2016 -0400

    Merge pull request #9003 from dillaman/wip-15743
    
    jewel: librbd: reduce log level for image format 1 warning
    
    Reviewed-by: Jason Dillaman <<EMAIL>>

commit 9750610ebf7f4d853dee23adaf6109c2c6b63ee0
Merge: 0218f0b fb7eb4e
Author: Jason Dillaman <<EMAIL>>
Date:   Tue May 10 13:13:27 2016 -0400

    Merge pull request #9010 from dillaman/wip-15736
    
    jewel: rbd-mirror: admin socket commands to start/stop/restart mirroring
    
    Reviewed-by: Jason Dillaman <<EMAIL>>

commit fb7eb4e6548b6536fccb6fd38b18a5ced0f9ee4b
Author: Mykola Golub <<EMAIL>>
Date:   Thu Apr 28 09:32:33 2016 +0300

    rbd-mirror: admin socket commands to start/stop/restart mirroring
    
    Signed-off-by: Mykola Golub <<EMAIL>>
    (cherry picked from commit a22cf518f9cc0872f72a8a850ea01558094ebd00)

commit 8e46e4383a15f022e9213c6710671e6241e7fb6c
Author: Mykola Golub <<EMAIL>>
Date:   Sun May 1 22:04:46 2016 +0300

    qa/workunits/rbd: fix rbd-mirror log file name
    
    Signed-off-by: Mykola Golub <<EMAIL>>
    (cherry picked from commit 4c28fc2aa378c10ce6e78b1a2f98d5e14ac211e9)

commit d73073a400807f2aa62d0eadc8ff6a8537ca04fa
Author: Mykola Golub <<EMAIL>>
Date:   Thu Apr 28 09:23:35 2016 +0300

    rbd-mirror: make image replayer asok commands available when not started
    
    Initially the asok commands were registered only after the image
    replayer start (and unregistered on stop) because their names were
    built using remote pool and image names, which became known only after
    start.
    
    Now, the asok commands are registered on the image replayer construction
    using the temporary name "remote_pool_name/global_image_id". They are
    re-registered using "remote_pool_name/remote_image_name" when the
    image replayer is started.  Also the commands are not unregistered on
    the image replayer stop.
    
    Signed-off-by: Mykola Golub <<EMAIL>>
    (cherry picked from commit 21790484dc66e97e7625b5b2afabb7efe3a92b08)

commit 24ea574a94c009d64d480bc3f887d4b0325d26d8
Author: Josh Durgin <<EMAIL>>
Date:   Fri Apr 1 18:15:33 2016 -0700

    qa/workunits/rbd: add env vars to adapt rbd_mirror.sh to teuthology
    
    With these set, this script can work in teuthology, where we want to
    control the how daemons run and their log locations, valgrind
    settings, etc. The workunit task always sets CEPH_ID, and allows
    other env vars to be configured optionally.
    
    The RBD_MIRROR_USE_EXISTING_CLUSTER option can also speed up local testing.
    
    Signed-off-by: Josh Durgin <<EMAIL>>
    (cherry picked from commit dcd11a1f262e0c56bb1b37d54283cda06abbf4c7)

commit 21220a1fa668d4080eb7c4844cafeefffa77ecdc
Author: Josh Durgin <<EMAIL>>
Date:   Thu Apr 7 16:57:14 2016 -0700

    qa: rbd_mirror.sh: change parameters to cluster rather than daemon name
    
    Daemon name is only useful to differentiate asok and pid filenames, so
    just inline its use there. Everywhere else replace the 'daemon'
    parameter with cluster or local_cluster as appropriate.
    
    Remove the CLUSTER{1,2}_DAEMON vars as well, since they are no longer
    used.
    
    Signed-off-by: Josh Durgin <<EMAIL>>
    (cherry picked from commit 5fe64fa806f3bbdbdb950a5643238cc2508814b0)

commit 0218f0b0750b88f308f3ee0837b4e4efc7aeea63
Merge: a152b32 c1960de
Author: Jason Dillaman <<EMAIL>>
Date:   Tue May 10 13:04:09 2016 -0400

    Merge pull request #9005 from dillaman/wip-15716
    
    jewel: rbd-nbd: fix rbd-nbd aio callback error handling
    
    Reviewed-by: Jason Dillaman <<EMAIL>>

commit a152b32016529633a810fc901a2c980ab1169b27
Merge: 54177d0 0e95ee4
Author: Jason Dillaman <<EMAIL>>
Date:   Tue May 10 13:03:38 2016 -0400

    jewel: librbd: unlock image if journal error encountered during lock
    
    jewel: librbd: unlock image if journal error encountered during lock
    
    Reviewed-by: Jason Dillaman <<EMAIL>>

commit 54177d00f06ab3d712c8837832de080e452205de
Merge: b6b3cdd a73b9dd
Author: Jason Dillaman <<EMAIL>>
Date:   Tue May 10 13:02:24 2016 -0400

    Merge pull request #9009 from dillaman/wip-15713
    
    jewel: librbd: possible race condition leads to use-after-free
    
    Reviewed-by: Jason Dillaman <<EMAIL>>

commit a73b9dd670bcd3a77611c7240d86963b29527983
Author: Jason Dillaman <<EMAIL>>
Date:   Tue May 3 07:41:30 2016 -0400

    librbd: possible race condition leads to use-after-free
    
    The invoke async operation state machine can complete before the
    owner lock is released.  Use a stack reference to prevent
    use-after-free.
    
    Fixes: http://tracker.ceph.com/issues/15690
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit d9421a25b582e41550526714b71fb55f05abb1de)

commit b6b3cdd40c60775c27b631cd8670e95bc84a5219
Merge: a3e2e41 28ac027
Author: Jason Dillaman <<EMAIL>>
Date:   Tue May 10 12:53:30 2016 -0400

    Merge pull request #9004 from dillaman/wip-15708
    
    jewel: rbd: help message distinction between commands and aliases
    
    Reviewed-by: Jason Dillaman <<EMAIL>>

commit a3e2e410f5ef6308fcfd59baf607c49112f69138
Merge: ade4359 82838e3
Author: Jason Dillaman <<EMAIL>>
Date:   Tue May 10 12:53:00 2016 -0400

    Merge pull request #8972 from Abhishekvrshny/wip-15697-jewel
    
    jewel: python: clone operation will fail if config overridden with "rbd default format = 1"
    
    Reviewed-by: Jason Dillaman <<EMAIL>>

commit ade4359b15d18ab51ab00168339d1aadcdffacbb
Merge: 7f95348 451246d
Author: Jason Dillaman <<EMAIL>>
Date:   Tue May 10 12:50:43 2016 -0400

    Merge pull request #9008 from dillaman/wip-15698
    
    jewel: rbd-mirror: lockdep error during bootstrap
    
    Reviewed-by: Jason Dillaman <<EMAIL>>

commit 9bb17dbb1ce2ae910927e120cf35da9ee4c98225 (refs/remotes/gh/wip-rgw-period-delete-jewel)
Author: Casey Bodley <<EMAIL>>
Date:   Fri May 6 15:57:22 2016 -0400

    rgw: fix for duplicates in list_periods()
    
    Signed-off-by: Casey Bodley <<EMAIL>>
    (cherry picked from commit abed30befd86c68922759a66db589285fe01e54b)

commit 7ea6e78aa0aa3b86d663dab3f69998e5cd3a5177
Author: Casey Bodley <<EMAIL>>
Date:   Fri May 6 15:18:46 2016 -0400

    rgw: 'period delete' cleans up all period objects
    
    Fixes: http://tracker.ceph.com/issues/15469
    
    Signed-off-by: Casey Bodley <<EMAIL>>
    (cherry picked from commit a286b32224d4a9757058415f98a2feff683ac520)

commit 451246df64890e39205d20d40eb5676893a7e1db
Author: Jason Dillaman <<EMAIL>>
Date:   Mon May 2 10:33:50 2016 -0400

    librbd: disable automatic refresh of image upon lock message
    
    There is no need to refresh the image upon request/release/acquire
    of the exclusive lock.  The next IO or maintenance op will kick off
    the refresh.  This is interfering with the refresh state machine
    unit test case (since two concurrent refreshes shouldn't be possible).
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit ee617a0ca56d752daf62bfffa59219b2c540a428)

commit 9e058fc852090ecb1b3a8daa3e0ee9df9d5eba3c
Author: Jason Dillaman <<EMAIL>>
Date:   Mon May 2 10:26:29 2016 -0400

    librbd: update_features should release lock if acquired
    
    There is a small window where requests are unblocked and before
    the image is shut down where new requests can be accepted.  These
    are correctly canceled during the shut down, but it can lead to
    warning messages.
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 608e09851396a9eeb7cf6dc1c7f687ef7a033601)

commit e653a1541ca7df29d01e8524a3f3b597fa0fb67b
Author: Jason Dillaman <<EMAIL>>
Date:   Mon May 2 09:27:29 2016 -0400

    librbd: avoid applying refreshed image config within librados callback
    
    There is a potential that a synchronous API call could deadlock a
    image refresh.
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit ce5c701bc47b0959f8453b6b92dee4804d3b1d75)

commit a5996e5ad64e01fcde50fc46bf9861b2d163f0c4
Author: Jason Dillaman <<EMAIL>>
Date:   Mon May 2 08:42:35 2016 -0400

    rbd-mirror: don't use temporary string for snap operation
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 06faf83bbfab000c8200a9cbe781adb0082b3f7b)

commit a891919b85f44a7d81fb2fb84d1b8d7258b3fbdc
Author: Jason Dillaman <<EMAIL>>
Date:   Mon May 2 08:31:54 2016 -0400

    librbd: avoid recursive locking within operation state machine
    
    Fixes: http://tracker.ceph.com/issues/15664
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 91a4890ee78c25391c1548fdacb2b51c46a47415)

commit 7f953488b46642ed3825349d41a34b0faeb6a76b
Merge: 8963af7 895c975
Author: Jason Dillaman <<EMAIL>>
Date:   Tue May 10 12:47:01 2016 -0400

    Merge pull request #8869 from Abhishekvrshny/wip-15669-jewel
    
    jewel: Errors encountered disabling object-map while flatten is in-progress
    
    Reviewed-by: Jason Dillaman <<EMAIL>>

commit 8963af73823285e9261acf6305778903a8f4205d
Merge: 978f30e 63738d4
Author: Jason Dillaman <<EMAIL>>
Date:   Tue May 10 12:46:12 2016 -0400

    Merge pull request #8870 from Abhishekvrshny/wip-15668-jewel
    
    jewel: rbd disk-usage CLI command should support calculating full image usage
    
    Reviewed-by: Jason Dillaman <<EMAIL>>

commit 978f30e100fa91c148fd2aeaadad2f6e7bb94f9a
Merge: 1dceb38 32c0901
Author: Jason Dillaman <<EMAIL>>
Date:   Tue May 10 12:35:41 2016 -0400

    Merge pull request #8868 from Abhishekvrshny/wip-15666-jewel
    
    jewel: rbd CLI to retrieve rbd mirror state for a pool / specific image
    
    Conflicts:
    	src/tools/rbd_mirror/ImageReplayer.cc: trivial resolution
    
    Reviewed-by: Jason Dillaman <<EMAIL>>

commit 1dceb38538d9c8a13551ef8b9217cb5757089576
Merge: 2495f08 357ad37
Author: Jason Dillaman <<EMAIL>>
Date:   Tue May 10 12:33:33 2016 -0400

    Merge pull request #9046 from dillaman/wip-fix-snapc-validation-jewel
    
    jewel: librbd: put the validation of image snap context earlier
    
    Reviewed-by: Jason Dillaman <<EMAIL>>

commit 357ad37a4facc9917866c780892c7cd0e21653fd
Author: runsisi <<EMAIL>>
Date:   Wed Apr 20 09:55:39 2016 +0800

    librbd: put the validation of image snap context earlier
    
    we'd better validate the snap context right after we got the mutable
    metadata before we could go any further
    
    Signed-off-by: runsisi <<EMAIL>>
    (cherry picked from commit ca8ae66ba42e7a598ad23ed911938589dc288456)

commit 2495f08a64b352cbd21432b708fdd8e4090b67bb
Merge: 5e9128d 125aab8
Author: Jason Dillaman <<EMAIL>>
Date:   Tue May 10 12:31:10 2016 -0400

    Merge pull request #8867 from Abhishekvrshny/wip-15661-jewel
    
    jewel: Parent image is closed twice if error encountered while opening
    
    Reviewed-by: Jason Dillaman <<EMAIL>>

commit 5e9128dc8b4af16512f689d556baa7d6e08ae1f8
Merge: c167514 af1c0bc
Author: Jason Dillaman <<EMAIL>>
Date:   Tue May 10 12:30:12 2016 -0400

    Merge pull request #8866 from Abhishekvrshny/wip-15660-jewel
    
    jewel: rbd-mirror can crash if start up is interrupted
    
    Reviewed-by: Jason Dillaman <<EMAIL>>

commit c16751431baed0322765e502bf750b7f9be49e78
Merge: 5e89e82 a2eb187
Author: Jason Dillaman <<EMAIL>>
Date:   Tue May 10 12:29:02 2016 -0400

    Merge pull request #9044 from dillaman/wip-librbd-bad-header-jewel
    
    jewel: librbd: does not crash if image header is too short
    
    Reviewed-by: Jason Dillaman <<EMAIL>>

commit a2eb187658d045160fb85f6c1d9437eaa49f0830
Author: Kefu Chai <<EMAIL>>
Date:   Thu Apr 28 00:59:34 2016 +0800

    librbd: does not crash if image header is too short
    
    if something goes wrong with the object, and returns a chunk shorter
    than expected, don't panic
    
    Signed-off-by: Kefu Chai <<EMAIL>>
    (cherry picked from commit 7b52183addda83f98c8b1f37d3de20ca0fc4687b)

commit 5e89e821f3e9b368b6d4963824e334acbd49cceb
Merge: f99aba4 8cae07c
Author: Jason Dillaman <<EMAIL>>
Date:   Tue May 10 12:26:48 2016 -0400

    Merge pull request #8803 from SUSE/wip-15605-jewel
    
    jewel: Potential double free of SetSnapRequest instance
    
    Reviewed-by: Jason Dillaman <<EMAIL>>

commit f99aba4a869bbb6dda5353a6f5c9c65da32dcd02
Merge: 9d40896 9b2c173
Author: Jason Dillaman <<EMAIL>>
Date:   Tue May 10 12:16:09 2016 -0400

    Merge pull request #8865 from Abhishekvrshny/wip-15649-jewel
    
    jewel: possible double-free of object map invalidation request upon error
    
    Reviewed-by: Jason Dillaman <<EMAIL>>

commit b6ebb25afc3d75d834311a9931b08c84e858a5e4
Author: Venky Shankar <<EMAIL>>
Date:   Thu May 5 15:28:16 2016 +0530

    rbd: helpful error message on map failure
    
    Failure to map an rbd image provides pretty much nothing to
    debug (or rectify) the nature of failure. This change tries
    to improve the same by providing helpful error messages (by
    pointing to dmesg, etc..).
    
    Fixes: http://http://tracker.ceph.com/issues/15816
    Signed-off-by: Venky Shankar <<EMAIL>>

commit f221fedb3b34bccd80383096d6ccfa19a4a72db9
Author: Jason Dillaman <<EMAIL>>
Date:   Fri May 6 11:16:37 2016 -0400

    librbd: assertion to ensure no concurrent processing of replay events
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 44827a3e8e02529be84c829a76ef01f70e0d20ee)

commit 37f08e6e15b21eec2dedfd5e497619165cd3d117
Author: Jason Dillaman <<EMAIL>>
Date:   Fri May 6 11:05:50 2016 -0400

    journal: suppress notifications if client still in try_pop_front loop
    
    One such example is popping the last entry from an object.  The next
    object will be automatically prefetched.  When that object is received,
    we do not want to alert the user that entries are available since
    try_pop_front already indicated more records were available.
    
    Fixes: http://tracker.ceph.com/issues/15755
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 3b8d21ac13865791d8fc4c519f47c1f7d6096e76)

commit 82c04c5ad3476805d8c4f7569e1eba41244ad2c7
Author: Jason Dillaman <<EMAIL>>
Date:   Fri May 6 09:47:01 2016 -0400

    librbd: delay processing of next journal entry until flush in-progress
    
    When replaying a journal flush event, do not start processing the next
    journal entry until after the flush is in progress to ensure the barrier
    is correctly guarding against future writes.
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 5d205ef33cb8b5517f141d46a62947dd583500e0)

commit 9d40896d280f44080623b1d9a0d9aba35ae410c5
Merge: bc764b6 075ee03
Author: Yehuda Sadeh <<EMAIL>>
Date:   Tue May 10 08:31:02 2016 -0700

    Merge pull request #8930 from theanalyst/wip-15707-jewel
    
    jewel: multisite: Issues with Deleting Buckets

commit bc764b6af7fb7f5aea6bc5fd8c8f5b7174949a90
Merge: 8bbe2a9 7029b9c
Author: Yehuda Sadeh <<EMAIL>>
Date:   Tue May 10 08:30:52 2016 -0700

    Merge pull request #9029 from theanalyst/wip-15680
    
    jewel: rgw: radosgw-admin zone set cuts pool names short if name starts with a period

commit 8bbe2a924d9b27582c130f045e1fede29171f159
Merge: 80beeff 5fdca84
Author: Yehuda Sadeh <<EMAIL>>
Date:   Tue May 10 08:30:03 2016 -0700

    Merge pull request #9022 from yehudasa/wip-rgw-leak-3-jewel
    
    jewel: rgw: leak fixes

commit 80beeff4b1bf89fba6c36b59ffcec61aa8fc975e
Merge: b9cb6bd d93fa13
Author: Yehuda Sadeh <<EMAIL>>
Date:   Tue May 10 08:29:52 2016 -0700

    Merge pull request #9021 from yehudasa/wip-rgw-xstorageurl-fix-jewel
    
    jewel: rgw: fix printing wrong X-Storage-Url in Swift's TempAuth.

commit b9cb6bd73593da4312889c928fba8f02a50b1533
Merge: ce2f028 f37318a
Author: Yehuda Sadeh <<EMAIL>>
Date:   Tue May 10 08:29:27 2016 -0700

    Merge pull request #9020 from yehudasa/wip-15597-jewel
    
    jewel: rgw: fix issue #15597

commit ce2f028268509361700980d47dabe5429127910f
Merge: 1cba195 a28fe02
Author: Yehuda Sadeh <<EMAIL>>
Date:   Tue May 10 08:29:18 2016 -0700

    Merge pull request #9019 from yehudasa/wip-rgw-admin-zonegroup-jewel
    
    jewel: radosgw-admin: add missing --zonegroup-id to usage

commit 1cba1951898e8d21dbfd6b8e354ebaa29f48b720
Merge: 4ead0f9 1d28aac
Author: Yehuda Sadeh <<EMAIL>>
Date:   Tue May 10 08:29:03 2016 -0700

    Merge pull request #9018 from yehudasa/wip-15626-jewel
    
    rgw: add AWS4 completion support for RGW_OP_SET_BUCKET_WEBSITE

commit 4ead0f98dccf79f65878384ac0f45b9f3e2ad97c
Merge: 6f23218 c08e90e
Author: Yehuda Sadeh <<EMAIL>>
Date:   Tue May 10 08:28:50 2016 -0700

    Merge pull request #9017 from yehudasa/wip-15625-jewel
    
    jewel: rgw: segfault at RGWAsyncGetSystemObj

commit 6f23218bbcee8190acfdf0ae7b76e6e742015d91
Merge: 2650806 e69bfa1
Author: Yehuda Sadeh <<EMAIL>>
Date:   Tue May 10 08:28:37 2016 -0700

    Merge pull request #9016 from yehudasa/wip-rgw-admin-modify-jewel
    
    jewel: radosgw-admin: update usage for zone[group] modify

commit 26508062cbb13ba28252735449784dd4b63175af
Merge: fdf8dcb 11d599c
Author: Yehuda Sadeh <<EMAIL>>
Date:   Tue May 10 08:27:56 2016 -0700

    Merge pull request #9015 from yehudasa/wip-rgw-zonegroup-remove-jewel
    
    jewel: radosgw-admin: zonegroup remove command

commit f18b14b00fd3b8fbf3007f9eeb9424bbaf5ee14a
Author: Jason Dillaman <<EMAIL>>
Date:   Mon May 9 11:22:48 2016 -0400

    journal: incorrectly computed object offset within set
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit f3372a1bcf81fcfd75e7ef5901dbcdc1daa062a3)

commit 466b7fe3d5cd6da8de993169da4c0e4c0990e703
Author: xie xingguo <<EMAIL>>
Date:   Fri Apr 22 09:45:30 2016 +0800

    tools/crushtool: add straw2 support for help message
    
    Signed-off-by: xie xingguo <<EMAIL>>
    (cherry picked from commit 6448ea283b4e1519682ecb29f18fbb767752d496)

commit d6692ed70aebc5387997660e9e2b025e96eff96f
Author: Alfredo Deza <<EMAIL>>
Date:   Wed Apr 20 16:23:55 2016 -0400

    install-deps: remove distribute requirement
    
    Because it was meant for Ubuntu 12.04 and it is installing directly
    from PyPI on newer releases that aren't being built for 12.04
    
    Signed-off-by: Alfredo Deza <<EMAIL>>
    (cherry picked from commit 64f14d43a829f998b9a2ad5f3d87095560914e2a)

commit 3011edab65e78156346ff7de655707619589bd7a
Author: Alfredo Deza <<EMAIL>>
Date:   Wed Apr 20 15:48:19 2016 -0400

    tools: remove installation for distribute. It is no longer maintained and breaks builds
    
    Signed-off-by: Alfredo Deza <<EMAIL>>
    (cherry picked from commit d02bd9cb70479b7373c5b65e42e9c57f8eae55f1)

commit 45a1f01aa4baa1913c335efb2f8b3941f3c0f791
Author: Alexandre Derumier <<EMAIL>>
Date:   Fri Apr 29 03:30:05 2016 +0200

    set 128MB tcmalloc cache size by bytes
    
    (cherry picked from commit d1d144c03bc9050931d86d635a16363bd2b73fd0)

commit bb1aeb7c7bb296d9264f051519d100ecf1f50f1d
Author: Alexandre Derumier <<EMAIL>>
Date:   Thu Apr 28 22:55:37 2016 +0200

    etc/default/ceph: set 128MB tcmalloc cache size
    
    Signed-off-by: Alexandre Derumier <<EMAIL>>
    (cherry picked from commit 9decbd1026c6ab0474c73a26a9c84c24557beb8a)

commit 617004ef26b6c97621a85a44b78cc7b0708fe161
Author: Jason Dillaman <<EMAIL>>
Date:   Tue May 10 08:33:03 2016 -0400

    test: update rbd integration cram test to remove format 1 warning
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 098038fad47b181a9ddd11767397082b05405be5)

commit 7029b9c3b0c02c9f20f6079243521458d28b2ab4
Author: Yehuda Sadeh <<EMAIL>>
Date:   Mon Apr 25 15:09:47 2016 -0700

    rgw: fix suffix in RGWZoneParams::fix_pool_names()
    
    Fixes: http://tracker.ceph.com/issues/15598
    
    Signed-off-by: Yehuda Sadeh <<EMAIL>>
    (cherry picked from commit 6f6b0c925994b0369eda390a6b3fd20005e70c9b)

commit f37318af823f76ba7bf05e09da83141b569bf579
Author: Yehuda Sadeh <<EMAIL>>
Date:   Mon May 9 14:23:03 2016 -0700

    rgw: code cleanup
    
    Signed-off-by: Yehuda Sadeh <<EMAIL>>
    (cherry picked from commit d53ffbfe65611425b08af71e0d24bfe2a0076f1e)

commit a8b800b903f0f1144efe0cf3568b02b3e200c3b2
Author: Yehuda Sadeh <<EMAIL>>
Date:   Tue Apr 26 11:31:43 2016 -0700

    rgw: upgrade default zonegroup and set correct zone[group] id
    
    Fixes: http://tracker.ceph.com/issues/15597
    
    Signed-off-by: Yehuda Sadeh <<EMAIL>>
    (cherry picked from commit 27806c71922eef2f4cde360d7ba40dee9d5bd4bd)

commit 5fdca846aaea9afd16828d33ccc279c4dcdbcd4c
Author: Yehuda Sadeh <<EMAIL>>
Date:   Thu May 5 10:57:05 2016 -0700

    rgw: RGWHTTPClient requests can unregister themselves early
    
    No need to wait for req_state to complete anymore.
    
    Signed-off-by: Yehuda Sadeh <<EMAIL>>
    (cherry picked from commit 91f61d68bf5fc39152d75fbc633f088e17d53d9e)
    
    Conflicts:
    	src/rgw/rgw_http_client.cc

commit 5609eb1bae0fc55b24a3025621597590f30aaad4
Author: Yehuda Sadeh <<EMAIL>>
Date:   Wed May 4 18:08:38 2016 -0700

    rgw: move around sync_env.init()
    
    call sync_env.init() even if we're initalized in RGWRemoteDataLog::init()
    
    Signed-off-by: Yehuda Sadeh <<EMAIL>>
    (cherry picked from commit 3d8442effc1c175e0b7c5064ed41bc642f6cdcb0)

commit faf6b2d25e89b7a84ed262c08b0a858dbf4199da
Author: Yehuda Sadeh <<EMAIL>>
Date:   Tue May 3 12:01:55 2016 -0700

    rgw: rados crs, explicit cleanup
    
    Signed-off-by: Yehuda Sadeh <<EMAIL>>
    (cherry picked from commit 1c876eb3873692042c0af3644f8ee3ad15ffdbf5)

commit b55514c390b22605fabdf3a8e5011415f57e6da8
Author: Yehuda Sadeh <<EMAIL>>
Date:   Tue May 3 12:00:46 2016 -0700

    rgw: RGWHTTPManager, avoid referring to req_data->client when completing
    
    this is past calling req_data->finish(), client can be destroyed by now
    
    Signed-off-by: Yehuda Sadeh <<EMAIL>>
    (cherry picked from commit 7ada4df124a71eae413e7dec0e25264a8deffcad)

commit 2d0cd8a08bb26f519d2b9b59bfe54f014cc36a1f
Author: Yehuda Sadeh <<EMAIL>>
Date:   Tue May 3 11:59:11 2016 -0700

    rgw: civetweb_callback() refer to store inside lock
    
    pe->store might change when reconfiguring
    
    Signed-off-by: Yehuda Sadeh <<EMAIL>>
    (cherry picked from commit 2ca38dab071c7d20a224a4e1dacfd68548fe52dd)

commit 2f65b313d08a5c9dfde220353eaa54204531b1ee
Author: Yehuda Sadeh <<EMAIL>>
Date:   Tue May 3 10:03:14 2016 -0700

    rgw: RGWPeriodPusher, stop http manager explicitly
    
    Signed-off-by: Yehuda Sadeh <<EMAIL>>
    (cherry picked from commit 98433e589f2a8b519726ed23b1565c5eb331c68e)

commit e5312b10aaa000d942468bf51d2164c689add279
Author: Yehuda Sadeh <<EMAIL>>
Date:   Tue May 3 10:02:02 2016 -0700

    rgw: RGWHTTPManager, can call stop() more than once
    
    destructor calls stop(), but it can be also called explicitly, if caller
    wants to ensure requests are not being handled anymore.
    
    Signed-off-by: Yehuda Sadeh <<EMAIL>>
    (cherry picked from commit bdce81a2ae00f154420386e107a104cbf100d96d)

commit cbea993562b03f75ceb0bdb7c02d72e5b51ae0fa
Author: Yehuda Sadeh <<EMAIL>>
Date:   Tue May 3 10:01:33 2016 -0700

    rgw: RGWReadRESTResourceCR, fix refcounting
    
    Signed-off-by: Yehuda Sadeh <<EMAIL>>
    (cherry picked from commit d1ec31def71505dc95139fbdd3b49c92fec1d296)

commit 93a65f3306e21c1bc34f5a95d85deeba746adb62
Author: Yehuda Sadeh <<EMAIL>>
Date:   Tue May 3 09:59:47 2016 -0700

    rgw: RGWReadRemoteDataLogShardCR fix destructor
    
    was missing destructor, constructor was broken
    
    Signed-off-by: Yehuda Sadeh <<EMAIL>>
    (cherry picked from commit 95162a4c1bf61f7329e363b7efd5686f7e1025f0)

commit 2de16690b6a404a365af5d9f526de044873f9729
Author: Yehuda Sadeh <<EMAIL>>
Date:   Mon May 2 18:13:17 2016 -0700

    rgw: RGWDataSyncStatusManager, cleanup if failing init
    
    init can be called more than once, ended up with leaking some resources
    
    Signed-off-by: Yehuda Sadeh <<EMAIL>>
    (cherry picked from commit 14cd44e0847fd914068f48dd0208ce7c1fe4eecb)

commit 57266e6b78851f8e6167f15ad8dbf7195d561371
Author: Yehuda Sadeh <<EMAIL>>
Date:   Fri Apr 29 13:04:13 2016 -0700

    rgw: rest crs, explicitly call cleanup
    
    Signed-off-by: Yehuda Sadeh <<EMAIL>>
    (cherry picked from commit a3a3f4f5883f3fc2bf47a7ebfa538206b7ab51d9)

commit 6b86332ca3ee35eb522cbf88408b7fd5f3070772
Author: Yehuda Sadeh <<EMAIL>>
Date:   Thu Apr 28 17:03:21 2016 -0700

    rgw: more leaks fixes
    
    Signed-off-by: Yehuda Sadeh <<EMAIL>>
    (cherry picked from commit cb797d4fd754b4a33eb34c249da3aabc65a7aba3)

commit 0fb48546a9fe6eb497833a6566353229e0923d3f
Author: Yehuda Sadeh <<EMAIL>>
Date:   Wed Apr 27 16:39:36 2016 -0700

    rgw: drop a reference to http op
    
    Signed-off-by: Yehuda Sadeh <<EMAIL>>
    (cherry picked from commit 4442d1c19acfc28c2dd4a011365ebebe08750963)

commit d93fa132bba597e71c4dd483663d2069e7117bf1
Author: Radoslaw Zarzynski <<EMAIL>>
Date:   Sun Apr 24 17:41:55 2016 +0200

    rgw: fix printing wrong X-Storage-Url in Swift's TempAuth.
    
    If the option "rgw_swift_account_in_url" is being set to true,
    both user and subuser IDs will be present in the X-Storage-Url
    header generated by the implementation of Swift API's TempAuth.
    This patch rectifies this behavior and makes that only the user
    identifier will be placed there.
    
    Fixes: http://tracker.ceph.com/issues/15667
    Signed-off-by: Radoslaw Zarzynski <<EMAIL>>
    (cherry picked from commit 88f4c67d822388d2038454a25ead7e9a11f404b5)

commit a28fe02c933e2ec7ea969a974f961769052e9d38
Author: Casey Bodley <<EMAIL>>
Date:   Tue Apr 26 15:20:45 2016 -0400

    radosgw-admin: add missing --zonegroup-id to usage
    
    also clarified that --rgw-zone takes the zone by name
    
    Signed-off-by: Casey Bodley <<EMAIL>>
    Reported-by: John Wilkins <<EMAIL>>
    (cherry picked from commit 97fad754565b262529073e1b9f8d1799bfdfe0de)

commit c08e90ea20b8ed96c7fe8638f725cf5abefc6783
Author: Yehuda Sadeh <<EMAIL>>
Date:   Wed Apr 27 15:09:55 2016 -0700

    rgw: don't allow any concurrent sync requests on the same key
    
    We used to allow concurrent requests, as long as these had different op,
    which wasn't correct.
    
    Signed-off-by: Yehuda Sadeh <<EMAIL>>
    (cherry picked from commit edea6d58dd25995bcc1ed4fc5be6f72ce4a6835a)

commit 6c61341697b99db34466c9e7e90aa7867048d651
Author: Yehuda Sadeh <<EMAIL>>
Date:   Tue Apr 26 15:59:33 2016 -0700

    rgw: collect children after waiting for them
    
    Otherwise we're going to busy loop there.
    
    Signed-off-by: Yehuda Sadeh <<EMAIL>>
    (cherry picked from commit 806fb85d18653910863ba6b8cb56b8db1a0fa3d2)

commit 4a5f33dd975eb44f908b8a90a7ad2e4beb17b354
Author: Yehuda Sadeh <<EMAIL>>
Date:   Tue Apr 26 15:29:41 2016 -0700

    rgw: don't pass object context into async coroutines
    
    Fixes: http://tracker.ceph.com/issues/15625
    
    The async read data/attrs coroutines may outlive the callers. Instead
    of introducing a complicated refcounting scheme, just don't pass it in.
    Anyway, it was not thread safe, and the benefit of using it is not
    clear.
    
    Signed-off-by: Yehuda Sadeh <<EMAIL>>
    (cherry picked from commit b225d1758057efc30721aad25447d54fa36e4cb2)

commit 1d12f82a24f78769bcebb32cb379445e04f35e46
Author: Yehuda Sadeh <<EMAIL>>
Date:   Thu Apr 21 17:24:16 2016 -0700

    rgw: check for status >= 0
    
    Fixes: #15565
    
    check only verified that status was > 0, so we ended not updating mtime for
    the target object. This resulted in a continuous sync of objects.
    
    Signed-off-by: Yehuda Sadeh <<EMAIL>>
    (cherry picked from commit 255f19d9ef964536f2db17f70749f489330262a6)

commit a288a054c18fedda9ac3230d392f5a41a140ca9a
Author: Yehuda Sadeh <<EMAIL>>
Date:   Fri Apr 22 14:23:22 2016 -0700

    rgw: don't try to delete object if does not exist
    
    If we know that the object does not exist, no need to remove it.
    
    Signed-off-by: Yehuda Sadeh <<EMAIL>>
    (cherry picked from commit 8d59a30d56b5f197a293f7fba6ebb1a630a33a05)

commit 308b9f54e9fa07d071335bb2d953a50a5095c464
Author: Yehuda Sadeh <<EMAIL>>
Date:   Fri Apr 22 11:55:28 2016 -0700

    rgw, cls/rgw: store removed object mtime in bi log
    
    Store the mtime of the object that was removed in the bucket index log.
    This will be used in data sync.
    
    Signed-off-by: Yehuda Sadeh <<EMAIL>>
    (cherry picked from commit 80d64049937e1930ea8273fdad76a51493a5e14a)

commit 1d28aac642d940bb1c63733406999a7b027e05f2
Author: Javier M. Mellid <<EMAIL>>
Date:   Wed Apr 27 20:28:08 2016 +0200

    rgw: add AWS4 completion support for RGW_OP_SET_BUCKET_WEBSITE
    
    Fixes: http://tracker.ceph.com/issues/15626
    
    Signed-off-by: Javier M. Mellid <<EMAIL>>
    (cherry picked from commit 05373354e4725c3fc675df652fbf97da26160bcb)

commit e69bfa14e184716c4ab6bf01746edf5c9663a8e1
Author: Casey Bodley <<EMAIL>>
Date:   Wed Apr 27 18:06:45 2016 -0400

    radosgw-admin: update usage for zone[group] modify
    
    the help strings indicated that only --master could be changed
    
    Signed-off-by: Casey Bodley <<EMAIL>>
    (cherry picked from commit 410d432d11541a29c3fd4608a070cecf8dc2766e)

commit 11d599cda659a42ddde43f34307c5e3553596c54
Author: Casey Bodley <<EMAIL>>
Date:   Fri May 6 14:26:46 2016 -0400

    test/rgw: add test_zonegroup_remove
    
    Signed-off-by: Casey Bodley <<EMAIL>>
    (cherry picked from commit e2b27c7266f0ace372dbf94e062598fd7a575795)

commit 928ccf4952052ab19044de1a8f20246075d54832
Author: Casey Bodley <<EMAIL>>
Date:   Fri May 6 14:26:23 2016 -0400

    test/rgw: index zones by name instead of insertion order
    
    Signed-off-by: Casey Bodley <<EMAIL>>
    (cherry picked from commit 6327ade12bf0255c3b35bd6407f1dd2bcc6936fa)

commit 5edacdccc61d565a0e91af452f9297d559a474e4
Author: Casey Bodley <<EMAIL>>
Date:   Mon May 2 11:38:24 2016 -0400

    radosgw-admin: add missing 'zonegroup remove'
    
    Fixes: http://tracker.ceph.com/issues/15684
    
    Signed-off-by: Casey Bodley <<EMAIL>>
    (cherry picked from commit db0fa48ed5958d37928f893acd44cd66b6b9b990)

commit 6b1d9ebb80478c1dfe0e974377bd4fb070a5d417
Author: Casey Bodley <<EMAIL>>
Date:   Mon May 2 11:36:58 2016 -0400

    rgw: RGWZoneGroup::remove_zone() takes zone id
    
    Signed-off-by: Casey Bodley <<EMAIL>>
    (cherry picked from commit 3a451511fb38a5f1cd83ca3610c91141e7279f63)

commit 0e95ee4bd16f4d54d1da476b51fd0fd0829d69ed
Author: Jason Dillaman <<EMAIL>>
Date:   Tue May 3 10:15:08 2016 -0400

    librbd: unlock image if journal error encountered during lock
    
    Explicitly unlock to prevent a client from accidentally blacklisting
    itself when retrying the lock.
    
    Fixes: http://tracker.ceph.com/issues/15709
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit a11f5e8e55fc448ed60616cbf66a3ea7db2247b9)

commit fdf8dcb772d8313a3a801bf265f022505dba934d
Merge: 030883f 0de6345
Author: Sage Weil <<EMAIL>>
Date:   Mon May 9 13:54:58 2016 -0400

    Merge pull request #9006 from athanatos/wip-15655-jewel
    
    OSDMonitor: avoid underflow in reweight-by-utilization if max_change=1
    
    Reviewed-by: Sage Weil <<EMAIL>>

commit c1960debf5c8696f365cd670dedeea01d2a3b9a8
Author: <EMAIL> <<EMAIL>>
Date:   Sat Apr 30 15:22:29 2016 +0800

    rbd-nbd: Fix aio_callback error handling
    
    Change aio_callback return code handling for NBD_CMD_READ command.
    
    Tracker:
    http://tracker.ceph.com/issues/15604
    
    Signed-off-by: Chang-Yi Lee <<EMAIL>>
    (cherry picked from commit ee211295441b126a995c07820176cfc966ab61c5)

commit 4e87c1cd7e923f4254ce43e92e1cce4329fd86dd
Author: <EMAIL> <<EMAIL>>
Date:   Wed Apr 27 11:00:25 2016 +0800

    Fix RBD-NBD aio_callback error handling
    
    Tracker-Id: #15604
    
    Signed-off-by: Chang-Yi Lee <<EMAIL>>
    (cherry picked from commit f8f6c1dc865b7ed642fec3f416e5eca5e91523d0)

commit 28ac027f6f86635d320f0f5e25582452011d9bed
Author: YongQiang <<EMAIL>>
Date:   Fri Apr 22 11:20:31 2016 -0400

    rbd:make a distinction of help message between ''rbd snap rollback" and "rbd snap revert"
    
    Fixes: https://tracker.ceph.com/issues/15521
    
    Signed-off-by: Yongqiang He <<EMAIL>>
    (cherry picked from commit 1bf26509c6efd06a8facc0e45ab42255592ca74d)

commit 039554d7ad8cfd82bcb6d4c6564f6dd46320a24f
Author: Jason Dillaman <<EMAIL>>
Date:   Wed May 4 13:36:06 2016 -0400

    librbd: reduce log level for image format 1 warning
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit c87f383b65ccc2e91645394f1c75de1c2d52e1e1)

commit 030883f41d3d2d8f297b3d3f6e7c7fa5a06ea4c0
Author: xie xingguo <<EMAIL>>
Date:   Tue Apr 26 15:12:17 2016 +0800

    osdc/Objecter: fix race condition for sortbitwise flag detection
    
    During list_objects/list_nobjects(), we are checking osdmap's
    sortbitwise flag setting without holding rwlock in hand, which
    is a potential race condition.
    
    Signed-off-by: xie xingguo <<EMAIL>>
    (cherry picked from commit 7fb6be706be2454cc2a831df29808997f0aa5ab3)

commit 82838e340e0ab7c593618f5807ea5868ecd6bc48
Author: Jason Dillaman <<EMAIL>>
Date:   Mon May 2 15:06:51 2016 -0400

    librbd: default clone operation to image format 2
    
    Image format 1 doesn't support clones, so unless the user
    overrode the setting, default to format 2.
    
    Fixes: http://tracker.ceph.com/issues/15685
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit f24c70f54343aa6698af8ac1f0774282b0659968)

commit ca13a9554d8a9d97428c2096b5a2ae8e25d08a9e
Author: Jason Dillaman <<EMAIL>>
Date:   Mon May 2 14:51:31 2016 -0400

    librbd: add rbd_image_options_is_set helper method to API
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 2633b045e0b57827cc10c2e7707bd5a5e344e59a)

commit e6aa4533feb9c8e6d15c820f54dd719c378dd26e
Author: John Spray <<EMAIL>>
Date:   Fri Apr 29 20:12:20 2016 +0100

    mds: fix upgrades with replay daemons in map
    
    In the new world, a standby replay daemon which
    is actively replaying has its rank set.  In the old
    world it did not: map this during the upgrade/downgrade
    paths in decode/encode.
    
    Fixes: http://tracker.ceph.com/issues/15591
    Signed-off-by: John Spray <<EMAIL>>
    (cherry picked from commit d94115aa2a4bd92c56552e9fd11205d391a1c673)

commit 6c1b792b7b2fba729f21b0f10ec70d14b830ffa9
Author: John Spray <<EMAIL>>
Date:   Wed Mar 30 12:06:27 2016 +0100

    doc: add cephfs daemon management docs
    
    Principally to tell people how to configure
    standby, but more generally as a place to define
    our terms for GIDs etc.
    
    Signed-off-by: John Spray <<EMAIL>>
    (cherry picked from commit 97dd6897efaa14d3f8859a791949f9d246769620)

commit bf44c6d434a41b90c0739e493a022cb3d3df5554
Author: John Spray <<EMAIL>>
Date:   Wed Mar 30 12:05:43 2016 +0100

    mds: omit fscid in single-filesystem status output
    
    Signed-off-by: John Spray <<EMAIL>>
    (cherry picked from commit ec8f3db3e5c26b60b9333e86453e1b17324a8cfa)

commit be5274c3f47ecb0dca0facca37d4a90c68b37027
Author: John Spray <<EMAIL>>
Date:   Thu Mar 24 13:23:26 2016 +0000

    mds: simplify standby/standby-replay logic
    
    This used to use an arcane set of constants
    in standby_for_rank, combined with daemons sometimes
    sending requests to enter state STANDBY_REPLAY.
    
    Simplify this so that there is only one place we
    put daemons into standby replay, and that is
    in tick->maybe_promote_staandby.
    
    There is a behavioural change in here, which is
    that we used to sometimes promote standbys to
    be standby-replay, even if they didn't have
    "standby replay = true", when they did have
    a standby_for_rank or standby_for_name set.
    I'm calling that a bug, and making it so that
    daemons will only go into standby-replay if
    "standby replay = true" is set.
    
    Signed-off-by: John Spray <<EMAIL>>
    (cherry picked from commit 2a2a56b2d0e9e4224cae27f80d990b787e209bed)

commit ae3ce5b8741eaf65d03aeb7b74925a75e5ef2598
Author: John Spray <<EMAIL>>
Date:   Thu Mar 24 13:13:55 2016 +0000

    messages: add MMDSBeacon::standby_replay
    
    So that daemons can explicitly say that they
    want to be standby replay, instead of using
    magic standby_for_rank values.
    
    Because in some cases daemons would indicate this
    with a beacon requesting STANDBY_REPLAY state, we
    also munge these during decode to be normal STANDBY
    requests with standby_replay=true.
    
    Signed-off-by: John Spray <<EMAIL>>
    (cherry picked from commit cdf8a29f81475a000824ce8942f02d9d570ec3bb)

commit 02e3edd93c0f4ef6e0d11df1f35187f74c7ea2ff
Author: John Spray <<EMAIL>>
Date:   Tue Mar 22 17:44:22 2016 +0000

    mds: remove ONESHOT_REPLAY mode
    
    This predated cephfs-journal-tool.  Don't bother
    with any backwards-compatibility mechanisms here, as
    it was a seriously niche undocumented thing.
    
    On the off chance that someone ran an old ceph-mds against
    a newer ceph-mon with oneshot enabled, it shouldn't break
    anything, it just won't do what they want.
    
    Signed-off-by: John Spray <<EMAIL>>
    (cherry picked from commit edfda9c6b0a0c8b75ebff5f06017aa475339b1fd)

commit 52ca195221ffa86b778346e847aeec817329c651
Author: John Spray <<EMAIL>>
Date:   Thu Apr 7 12:53:00 2016 +0100

    mds: remove stale comments
    
    A little bit of detritus from the MDS->MDSRank refactor.
    
    Signed-off-by: John Spray <<EMAIL>>
    (cherry picked from commit e9b58e7ff97646e557c5b3e2c86a067d2d136149)

commit c1279d8ff8716a54622bf98bd29ce40faa2bf41d
Author: John Spray <<EMAIL>>
Date:   Thu Apr 7 12:37:08 2016 +0100

    mds: remove inc array from mdsmap
    
    Instead, use the MDSMap epochs to provide the `inc` that
    we populate into mds_info_t for use by the daemons.
    
    It is not necessary to respect the current value of inc
    in old maps, because the per-rank counters were necessarily
    lower numbers than the overall map epoch (incrementing
    the counters always corresponded to a map change).
    
    Fixes: http://tracker.ceph.com/issues/15399
    Signed-off-by: John Spray <<EMAIL>>
    (cherry picked from commit 30a375ebf814245fbbb5155fd39d8ff673f0a32a)

commit 9d5162fe102301bc94065f99a20b3067395dde2a
Author: John Spray <<EMAIL>>
Date:   Thu Sep 24 14:03:58 2015 +0100

    test/mds: add test for symbols in paths
    
    Especially, hyphens.
    
    Signed-off-by: John Spray <<EMAIL>>
    (cherry picked from commit 4e9327f64e5965c57c0eeea397060dc2ab43cfbf)

commit af3a4e4af36b5159721b634e216edcab9a171155
Author: John Spray <<EMAIL>>
Date:   Tue Apr 12 02:49:32 2016 +0100

    mds: fix auth caps with hyphen in path
    
    Quirk of the boost spirit char() fn, hyphens
    are treated as range delimiters unless they're
    the last character in the set.
    
    Fixes: #15465
    Signed-off-by: John Spray <<EMAIL>>
    (cherry picked from commit f82fa5f59a80851581573553be07e8f1620ee346)

commit 075ee0310888d012c56c494291b873be47393e47
Author: Abhishek Lekshmanan <<EMAIL>>
Date:   Tue Apr 19 15:20:53 2016 +0200

    rgw_op: pass delete bucket op to master zone first
    
    Basically first ask the master to process a delete bucket request before
    we process it in the zone locally, otherwise we reach a situation where
    we drop the bucket locally and master will still maintain an index
    
    Fixes: http://tracker.ceph.com/issues/15540
    Signed-off-by: Abhishek Lekshmanan <<EMAIL>>
    (cherry picked from commit da6b44a6e47399493adae53ad3c36a029f1e2a23)

commit 1527b56a888a65fa28223cd9c9cdb06d1575973c
Author: Abhishek Lekshmanan <<EMAIL>>
Date:   Tue Apr 19 15:20:07 2016 +0200

    rgw: add errno entry for -ENOTEMPTY
    
    currently if master returns an -ENOTEMTPY, we return a -EIO as we dont
    have an entry for this
    
    Signed-off-by: Abhishek Lekshmanan <<EMAIL>>
    (cherry picked from commit 40c3bfef8cc3bce7d15fbb0e135c1a909ee59368)

commit 791eba81a5467dd5de4f1680ed0deb647eb3fb8b
Author: Alexandre Derumier <<EMAIL>>
Date:   Thu Apr 28 22:48:52 2016 +0200

    fix deb package /etc/default/ceph location
    
    commit 7384a14f243519547a61534d22e21e6069aae016 have introduce
    the /etc/default/ceph file at a wrong location : /etc/default/ceph/ceph
    
    (cherry picked from commit 13804fafd4c33068d8ed6d0480b594daec0f9341)

commit d9851351aeb6d45a2df1c107b23e77c992926d0a
Author: xie xingguo <<EMAIL>>
Date:   Tue Apr 26 11:13:32 2016 +0800

    mon/OSDMonitor: improve reweight_by_utilization() logic
    
    By calling reweight_by_utilization() method, we are aiming at an evener result
    of utilization among all osds. To achieve this, we shall decrease weights of
    osds which are currently overloaded, and try to increase weights of osds which
    are currently underloaded when it is possible.
    However, we can't do this all at a time in order to avoid a massive pg migrations
    between osds. Thus we introduce a max_osds limit to smooth the progress.
    
    The problem here is that we have sorted the utilization of all osds in a descending
    manner and we always try to decrease the weights of the most overloaded osds
    since they are most likely to encounter a nearfull/full transition soon, but
    we won't increase the weights from the most underloaded(least utilized by contrast)
    at the same time, which I think is not quite reasonable.
    
    Actually, the best thing would probably be to iterate over teh low and high osds
    in parallel, and do the ones that are furthest from the average first.
    
    Signed-off-by: xie xingguo <<EMAIL>>
    (cherry picked from commit e7a32534ebc9e27f955ff2d7a8d1db511383301e)

commit b0543fdfdebc6766fe927b4cc34a4369e7f86544
Author: xie xingguo <<EMAIL>>
Date:   Mon Apr 25 15:43:03 2016 +0800

    mon/OSDMonitor: avoid potential expensive grace calculation
    
    The grace calculation during check_failure() is now very complicated
    and time-consuming. Therefore we shall skip this when it is possible.
    
    Signed-off-by: xie xingguo <<EMAIL>>
    (cherry picked from commit 3557903d5d57642179b2ae137bedc389974b1956)

commit 53686dfac0622a981374d448ce8229eb3984e5c2
Author: Sage Weil <<EMAIL>>
Date:   Fri Apr 29 22:01:56 2016 -0400

    mon/OSDMonitor: max_osds must be > 0 for reweight-by-*
    
    Include a few test cases.
    
    Signed-off-by: Sage Weil <<EMAIL>>
    (cherry picked from commit 84bd0dce18f6dd0c35d7e6cfee91ce8c5a0f4db7)

commit ec416556e516b60ab490c424a4e45fa9ff8f8fd0
Merge: 442c31b f4d63af
Author: John Spray <<EMAIL>>
Date:   Tue May 3 08:05:23 2016 -0500

    Merge pull request #8787 from xiaoxichen/wip-15631
    
    jewel: CephFSVolumeClient should isolate volumes by RADOS namespace
    
    Reviewed-by: John Spray <<EMAIL>>

commit 0de63455cbf662435ff57a35b9d98187091dcec7 (refs/remotes/gh/wip-sam-testing-jewel)
Author: Samuel Just <<EMAIL>>
Date:   Thu Apr 28 13:47:48 2016 -0700

    OSDMonitor: avoid underflow in reweight-by-utilization if max_change=1
    
    Fixes: http://tracker.ceph.com/issues/15655
    Signed-off-by: Samuel Just <<EMAIL>>
    (cherry picked from commit c121bc51a5a877a49094cfbd3bd16af1f3ae5090)

commit 63738d4d154d983d98021a09daa24607df64b979
Author: Jason Dillaman <<EMAIL>>
Date:   Thu Apr 28 15:53:59 2016 -0400

    rbd: disk usage now includes all snapshots by default
    
    Added a new "--from-snap" optional to restrict the calculation to
    specific subsets of snapshots and the HEAD revision.
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 1ccdcb5b6c1cfd176a86df4f115a88accc81b4d0)

commit 895c975651b722832befeb829a43acc55823740b
Author: Jason Dillaman <<EMAIL>>
Date:   Thu Apr 28 12:48:59 2016 -0400

    librbd: block RPC requests while updating features
    
    Disabling the journal and object map require acquiring the exclusive
    lock locally.  We don't want to start executing long-running ops
    for this quick process.
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit db7aaff0f6d7a57e2b36d22b609f915f9b2b3893)

commit 1f0056a427cd46793934c59c49aa225da2237628
Author: Jason Dillaman <<EMAIL>>
Date:   Thu Apr 28 12:43:24 2016 -0400

    librbd: return -ESHUTDOWN when a refresh is requested on a closed image
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit ed132a1eb10e6b2eb4bc465658eb0379daca3d67)

commit 6da1a84f16f4478ec17bdd4f50d9b0a02178c444
Author: Jason Dillaman <<EMAIL>>
Date:   Thu Apr 28 12:42:49 2016 -0400

    librbd: synchronous block_writes should return possible error result
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 88e76cb3c9a74b4718ad39c827f2194666f5be2a)

commit 04ef40e7652ff05146289a6e9e15ce2290a1ee32
Author: Jason Dillaman <<EMAIL>>
Date:   Thu Apr 28 11:46:20 2016 -0400

    librbd: refresh image before executing maint op
    
    If a maint op is interrupted and restarted, it's possible that the
    image will need to be refreshed.
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 9cf6821bd9f58c181720a5c8156cec5ab33d35d4)

commit 64cb6455f384f5ecc20abcf4d4ec6596569ac4c0
Author: Jason Dillaman <<EMAIL>>
Date:   Thu Apr 28 11:10:25 2016 -0400

    librbd: reduce log level when lock owner not detected
    
    This can happen under normal operating conditions and the
    lock request is automatically retried.
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 5a4893b7a7e50cc399696e1d0cb4d4f8785e723d)

commit 49cfb0e48014610d04233a6c7549bbc3756dd3f6
Author: Jason Dillaman <<EMAIL>>
Date:   Thu Apr 28 10:52:15 2016 -0400

    librbd: reduce log level for interrupted maint ops
    
    Fixes: http://tracker.ceph.com/issues/15572
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 1617328402345c68bf5d54828da6d687e51ef42b)

commit 32c09015a16ef8dee535d2c81825a2590f7f15e7
Author: Mykola Golub <<EMAIL>>
Date:   Sun Apr 24 19:39:40 2016 +0300

    qa/workunits/rbd: test mirror status in pool directory
    
    Signed-off-by: Mykola Golub <<EMAIL>>
    (cherry picked from commit f15cd51a1ddbd4079a8218bc2446a4bf68f95107)

commit 9a30a89abc5749c37d26d39c175e51259f63caec
Author: Mykola Golub <<EMAIL>>
Date:   Thu Apr 21 09:52:47 2016 +0300

    rbd-mirror: in replay status store number of entries behind master
    
    Signed-off-by: Mykola Golub <<EMAIL>>
    (cherry picked from commit e4229d1d4b163f1003e68fef1db63f796393ba23)

commit 15a9131fd2d55191c3082c6692f5e8f714fcff71
Author: Mykola Golub <<EMAIL>>
Date:   Fri Apr 15 08:42:17 2016 +0300

    librbd: API to retrieve image mirroring status
    
    Signed-off-by: Mykola Golub <<EMAIL>>
    (cherry picked from commit a2045d61874f9e418b5b3c3136af4482603c8e7f)

commit 98ca7f3904a9c8ff9a661f153069a8747a51431b
Author: Mykola Golub <<EMAIL>>
Date:   Wed Apr 6 14:57:29 2016 +0300

    test: MockJournaler: add get_cached_client method
    
    Signed-off-by: Mykola Golub <<EMAIL>>
    (cherry picked from commit 7633407ab584ce8c7777a525554773c2a358ce5f)

commit e57c4d8f0832abcf431029b04da43e043a479d19
Author: Mykola Golub <<EMAIL>>
Date:   Tue Apr 5 14:42:09 2016 +0300

    rbd: CLI to retrieve rbd mirror state for a pool / specific image
    
    Fixes: #15144
    Signed-off-by: Mykola Golub <<EMAIL>>
    (cherry picked from commit fe5207278d884464c05d6808245a0c4df03c441c)

commit 6111a25448fda1824ca6b3007fcdece1970674d6
Author: Mykola Golub <<EMAIL>>
Date:   Tue Apr 5 14:40:50 2016 +0300

    rbd-mirror: store replay status in mirroring object
    
    Fixes: #14420
    Signed-off-by: Mykola Golub <<EMAIL>>
    (cherry picked from commit 52b2fe14cfc932b2aa53ce76a3150ce87ce4d377)

commit f22514201c3b49d93593fad27360deb7b715493d
Author: Mykola Golub <<EMAIL>>
Date:   Tue Apr 5 14:25:11 2016 +0300

    test: cls_rbd: methods to store/retrieve mirroring status
    
    Signed-off-by: Mykola Golub <<EMAIL>>
    (cherry picked from commit 8aefad59813489cee112f46b3fe10331bb039b57)

commit 4a3f0d23ba4b96a28de661e219dcb52165bbbeac
Author: Mykola Golub <<EMAIL>>
Date:   Tue Apr 5 14:24:07 2016 +0300

    cls::rbd:: methods to store/retrieve mirroring status
    
    Signed-off-by: Mykola Golub <<EMAIL>>
    (cherry picked from commit 17dd40df4f4201cf0c0c6d58b1728177c5ee726b)

commit 62c60144311518e7e7947791a17608ed1765f971
Author: Mykola Golub <<EMAIL>>
Date:   Fri Apr 1 08:27:03 2016 +0300

    objclass: add method to list watchers
    
    Signed-off-by: Mykola Golub <<EMAIL>>
    (cherry picked from commit 44a31d2b770ef016271f9285447c601921b9c838)

commit 442c31bcf73899767c7fa198427712bbdf767067
Merge: 6e5091e d302617
Author: Boris Ranto <<EMAIL>>
Date:   Mon May 2 08:35:09 2016 +0200

    Merge pull request #8843 from SUSE/wip-msd-typo-fix-jewel
    
    systemd: fix typo in preset file
    
    Reviewed-by: Boris Ranto <<EMAIL>>

commit 125aab8dc4300dc9c67b2d4ca219cfbd60ab0924
Author: Jason Dillaman <<EMAIL>>
Date:   Wed Apr 27 15:56:11 2016 -0400

    librbd: propagate any image refresh errors back to caller
    
    Previously, the saved error code was only propagated when an image
    flush was required.
    
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 105ef8a24aa6ac8719f68c5a9366129ddb41ee88)

commit 5fe4beac0ed68ebf33fa69ba7a3082d2072bb9ce
Author: Jason Dillaman <<EMAIL>>
Date:   Wed Apr 27 15:45:25 2016 -0400

    librbd: avoid second close attempt of invalid parent image
    
    Fixes: http://tracker.ceph.com/issues/15574
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit e91e7f8d9ce10f0d19c48facfb6318d6b49798e8)

commit af1c0bce9184c55aa88b4082db9302239009af36
Author: Jason Dillaman <<EMAIL>>
Date:   Wed Apr 27 15:22:59 2016 -0400

    rbd-mirror: interrupting image replay startup can cause crash
    
    Properly shut down the journaler if a shut down has been requested
    while start up is in-progress.
    
    Fixes: http://tracker.ceph.com/issues/15630
    Signed-off-by: Jason Dillaman <<EMAIL>>
    (cherry picked from commit 3a71052a5b14af34ed81f6ee77f862f576a7a5cc)

commit 9b2c17392a2d9543f05d0cb8486183edf84eb2c3
Author: runsisi <<EMAIL>>
Date:   Thu Apr 28 09:05:33 2016 +0800

    librbd: fix potential assertion for object map invalidation
    
    if update_flags fails we should not go any further, or the async request
    will be removed from m_image_ctx.async_requests twice
    
    Signed-off-by: runsisi <<EMAIL>>
    (cherry picked from commit dd0eb719a50e9fb24eca5a840c2e8adb53be41b2)

commit 6e5091e454018e42ebe79bc4ec70cd24bfaeb36d
Author: Nathan Cutler <<EMAIL>>
Date:   Sat Apr 30 16:21:13 2016 +0200

    systemd: fix typo in preset file
    
    Signed-off-by: Nathan Cutler <<EMAIL>>
    (cherry picked from commit 80be4a8cbf1d42232ff61494d035e7abd90c2c14)

commit d3026174f87ede746a0ab0aa96b0efbf5e77b161
Author: Nathan Cutler <<EMAIL>>
Date:   Sat Apr 30 16:21:13 2016 +0200

    systemd: fix typo in preset file
    
    Signed-off-by: Nathan Cutler <<EMAIL>>
    (cherry picked from commit 80be4a8cbf1d42232ff61494d035e7abd90c2c14)

commit 9cdad1f348a67e3f4daec617210340b14a2dd49a
Merge: 96b3726 37ccacf
Author: Boris Ranto <<EMAIL>>
Date:   Fri Apr 29 08:22:01 2016 +0200

    Merge pull request #8801 from SUSE/wip-15633-jewel
    
    jewel: ceph-{mds,mon,osd} packages need scriptlets with systemd code
    
    Reviewed-by: Nathan Cutler <<EMAIL>>
    Reviewed-by: Boris Ranto <<EMAIL>>

commit 8cae07c10b9cb62af87d494724586c945f181127
Author: runsisi <<EMAIL>>
Date:   Thu Apr 21 14:48:55 2016 +0800

    librbd: fix potential double free of SetSnapRequest instance
    
    if image feature EXCLUSIVE_LOCK is not enabled we should not try to
    initialize the exclusive lock, or we may end with two async Contexts
    to finish the same SetSnapRequest instance
    
    Fixes: http://tracker.ceph.com/issues/15571
    Signed-off-by: runsisi <<EMAIL>>
    (cherry picked from commit 0a6eaac12fe29762c3dd29068f9427840cfea100)

commit 37ccacf7d1ce57291d6cbadd416fb96aed7db36a
Author: Boris Ranto <<EMAIL>>
Date:   Wed Apr 27 18:10:51 2016 +0200

    rpm: Add rpm scripts for ceph-rbd-mirror
    
    We are currently missing the systemd scripts for the new ceph-rbd-mirror
    daemons. This patch introduces them.
    
    Signed-off-by: Boris Ranto <<EMAIL>>
    (cherry picked from commit 14652f32165f72efde569a939179fc26584ab155)

commit c729bdd4bcb974081899c65934d3fdb9e0e96316
Author: Boris Ranto <<EMAIL>>
Date:   Wed Apr 27 18:00:08 2016 +0200

    rpm: Start all the targets in %post
    
    We need to have the sub-targets active for the ceph.target to be able
    to propagate its calls to the services. If the sub-target is inactive,
    the main target won't propagate the stop/restart calls.
    
    Signed-off-by: Boris Ranto <<EMAIL>>
    (cherry picked from commit 73f457986753b504fd088c1fa064608aa9d340c9)

commit c72f0bc3d4fb9a3f5abfefcf8d2775721d95b33a
Author: Nathan Cutler <<EMAIL>>
Date:   Tue Apr 19 14:48:41 2016 +0200

    rpm: implement scriptlets for the post-split daemon packages
    
    This patch gives each of the ceph-{mds,mon,osd,radosgw} packages its own
    %post, %preun, and %postun scriptlets dealing with the package's unit files.
    
    The scriptlets of ceph-base are adapted to handle the ceph.target unit file
    only.
    
    The scriptlets of ceph-mon handle ceph-create-keys services in addition to ceph-mon.
    
    The scriptlets of ceph-osd handle ceph-disk services in addition to ceph-osd.
    
    Fixes: http://tracker.ceph.com/issues/14941
    Signed-off-by: Nathan Cutler <<EMAIL>>
    Signed-off-by: Boris Ranto <<EMAIL>>
    (cherry picked from commit 644aba9270714e5e231ac7d7e3437477837531eb)

commit 81f407337da18b5b555c6737e394a0e054d7c7da
Author: Nathan Cutler <<EMAIL>>
Date:   Tue Apr 19 14:51:01 2016 +0200

    systemd: enable all the ceph .target services by default
    
    Some distros, like Fedora and openSUSE, have a policy that all services are
    disabled by default.
    
    This patch changes that default for the ceph.target and
    ceph-{mds,mon,osd,radosgw}.target services.
    
    Signed-off-by: Nathan Cutler <<EMAIL>>
    Signed-off-by: Boris Ranto <<EMAIL>>
    (cherry picked from commit 53b1a6799c2ce08acc8382e4aa996d1bde700316)

commit f4d63af533b22f740d809272e2e47ebc9d8d6845
Author: Xiaoxi Chen <<EMAIL>>
Date:   Wed Apr 6 22:50:05 2016 +0800

    python_cephfs: rule out empty/None volume_id
    
    Signed-off-by: Xiaoxi Chen <<EMAIL>>
    (cherry picked from commit 73368069dd466cdbde1ebeae97771d603ce431f5)

commit b609017a52e3140e9ff6314b8d312a409af52a57
Author: Xiaoxi Chen <<EMAIL>>
Date:   Wed Apr 6 22:45:02 2016 +0800

    python-cephfs: use rados namespace for data isolation.
    
    As cephfs dir layout supports rados namespace in jewel, it would be
    good to use rados namespace instead of pool, since it is free,
    we always enforce namespace isolation for volumes.
    
    The meaning of "data_isolated" flag changed a bit, it's no longer used
    for security isolation, referring to physical isolation
    (i.e. potentially using different OSDs).
    
    Also, (de)authorize based on namespace.
    
    Signed-off-by: Xiaoxi Chen <<EMAIL>>
    (cherry picked from commit 0e1d013ea690c18f7fa6e2a19500f854a9949091)

commit 96b3726d74cb7bbcacdd22e0ab0fe233e4c4d829
Author: Tamil Muthamizhan <<EMAIL>>
Date:   Tue Apr 26 17:34:52 2016 -0700

    Signed-off-by: Tamil Muthamizhan <<EMAIL>>
    
    s/ceph-deploy/ceph-tests, hence removing ceph-deploy folder

commit 77fdbf1747d73fd809607efd3325cc38b2fbafc0
Author: Tamil Muthamizhan <<EMAIL>>
Date:   Fri Apr 22 18:25:46 2016 -0400

    Signed-off-by: Tamil Muthamizhan <<EMAIL>>
    
      renamed the workunit and test to reflect reality and for better clarity
    
    (cherry picked from commit 0fd358b877c611bf67c94b4a2dacbe65e17deae9)

commit 1fa533e83d8e541315aa4925a6192ab0401b688b
Author: Alexandre Derumier <<EMAIL>>
Date:   Sun Apr 24 09:55:35 2016 +0200

    fix ceph init script
    
    commit 65963739cd6815b8008282c8f64cd64365662e60 have introduce a bug
    
    test variables need to be quoted, or -n always return true, even if variables don't exist
    
    (cherry picked from commit b581dd56bea09520c154611ecea91ebe399058d3)

commit 7acbefadace28412f395bb98aec5651d5ec70666
Author: David Zafman <<EMAIL>>
Date:   Mon Apr 18 18:27:52 2016 -0700

    test: Fix ceph-objectstore-tool test to run manually from src non-cmake
    
    Fix init-ceph to handle it when CEPH_* values set
    
    Signed-off-by: David Zafman <<EMAIL>>
    (cherry picked from commit 5e8e03b4d030db57c93047551d5b92db0b604063)

commit af4b31cb9770d1710d038c557d547373f58a89a8
Author: James Page <<EMAIL>>
Date:   Tue Apr 5 16:58:58 2016 +0100

    Drop --setuser/--setgroup from osd prestart
    
    These are not supported by /usr/lib/ceph/ceph-osd-prestart.sh,
    resulting in warnings:
    
     ceph-osd-prestart.sh[23367]: getopt: unrecognized option '--setuser'
     ceph-osd-prestart.sh[23367]: getopt: unrecognized option '--setgroup'
    
    --setuser and --setgroup are only needed for the ceph-osd process.
    
    Signed-off-by: James Page <<EMAIL>>
    (cherry picked from commit 74977f78840fe95b67788f3aa135ac69a2353c43)

commit 8a86d08e38a8b68ba5e04b2cc4f59a2983481ee7
Author: Dan van der Ster <<EMAIL>>
Date:   Fri Apr 22 11:19:30 2016 +0200

    osd: fix backwards min/max osd utilization
    
    Signed-off-by: Dan van der Ster <<EMAIL>>
    (cherry picked from commit 7529851f2a186501664461916f785da8456ad612)
