#!/bin/sh

cd "$(dirname "$0")"
cd ..
TOPDIR=`pwd`

install -d -m0755 build-doc

if command -v dpkg >/dev/null; then
    packages=`cat ${TOPDIR}/doc_deps.deb.txt`
    for package in $packages; do
    if [ "$(dpkg --status -- $package 2>&1 | sed -n 's/^Status: //p')" != "install ok installed" ]; then
        # add a space after old values
        missing="${missing:+$missing }$package"
    fi
    done
    if [ -n "$missing" ]; then
        echo "$0: missing required packages, please install them:" 1>&2
        echo "sudo apt-get install -o APT::Install-Recommends=true $missing" 1>&2
        exit 1
    fi
elif command -v yum >/dev/null; then
    for package in ant ditaa doxygen libxslt-devel libxml2-devel graphviz python3-devel python3-pip python3-Cython; do
	if ! rpm -q --whatprovides $package >/dev/null ; then
		missing="${missing:+$missing }$package"
	fi
    done
    if [ -n "$missing" ]; then
        echo "$0: missing required packages, please install them:" 1>&2
        echo "yum install $missing"
        exit 1
    fi
else
    for command in dot doxygen ant ditaa cython; do
        if ! command -v "$command" > /dev/null; then
            # add a space after old values
            missing="${missing:+$missing }$command"
        fi
    done
    if [ -n "$missing" ]; then
        echo "$0: missing required command, please install them:" 1>&2
        echo "$missing" 1>&2
	exit 1
    fi
fi

# Don't enable -e until after running all the potentially-erroring checks
# for availability of commands
set -e

[ -z "$vdir" ] && vdir="$TOPDIR/build-doc/virtualenv"

md5=$vdir/md5
if test -f $md5 && md5sum --check $md5 > /dev/null; then
    # reusing existing venv
    :
else
    python3 -m venv $vdir

    $vdir/bin/pip install --quiet wheel
    $vdir/bin/pip install --quiet \
              -r $TOPDIR/admin/doc-requirements.txt \
              -r $TOPDIR/admin/doc-python-common-requirements.txt
    BUILD_DOC=1 $vdir/bin/pip install --quiet \
         -r $TOPDIR/admin/doc-pybind.txt
    md5sum \
        $TOPDIR/admin/doc-requirements.txt \
        $TOPDIR/admin/doc-python-common-requirements.txt \
        $TOPDIR/admin/doc-pybind.txt \
        > $md5
fi

install -d -m0755 \
    $TOPDIR/build-doc/output/html \
    $TOPDIR/build-doc/output/man

for opt in "$@"; do
    case $opt in
        html|man|livehtml)
            sphinx_targets="$sphinx_targets $opt"
            shift
            ;;
        --)
            shift
            break
    esac
done

if [ -z "$sphinx_targets" ]; then
    sphinx_targets="html man"
fi

cd build-doc

for target in $sphinx_targets; do
    # Build with -W so that warnings are treated as errors and this fails
    case $target in
        html)
            $vdir/bin/sphinx-build -W --keep-going -a -b dirhtml -d doctrees \
                                   $TOPDIR/doc $TOPDIR/build-doc/output/$target
            ;;
        man)
            $vdir/bin/sphinx-build -W --keep-going -a -b man -t man -d doctrees \
                                   -c $TOPDIR/man \
                                   $TOPDIR/doc/man $TOPDIR/build-doc/output/$target
            ;;
        livehtml)
            $vdir/bin/pip install --quiet sphinx-autobuild
            $vdir/bin/sphinx-autobuild --re-ignore '.*\.dot' -b dirhtml "$@" \
                                       $TOPDIR/doc $TOPDIR/build-doc/output/html
            ;;
    esac
done

#
# Build and install JavaDocs
#
JAVADIR=$TOPDIR/src/java

# Clean and build JavaDocs
rm -rf $JAVADIR/doc
ant -buildfile $JAVADIR/build.xml docs

# Create clean target directory
JAVA_OUTDIR=$TOPDIR/build-doc/output/html/cephfs/api/libcephfs-java/javadoc
rm -rf $JAVA_OUTDIR
mkdir $JAVA_OUTDIR

# Copy JavaDocs to target directory
cp -a $JAVADIR/doc/* $JAVA_OUTDIR/

echo "SUCCESS"
